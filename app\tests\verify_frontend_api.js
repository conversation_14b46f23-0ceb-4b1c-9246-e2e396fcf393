// 用于验证前端API配置的代码片段
// 可以在前端控制台中执行

// 测试工单API是否正确配置
function testTicketApis() {
  console.log('测试工单API配置...');
  
  // 获取Vue实例
  const app = document.querySelector('#app').__vue_app__;
  if (!app) {
    console.error('未找到Vue应用实例，请确认在Vue应用加载完成后运行此脚本');
    return;
  }
  
  // 获取API配置
  const api = app._instance.appContext.config.globalProperties.$api;
  
  // 检查工单API函数是否存在
  const ticketApis = [
    'getTicketList',
    'getTicketById',
    'createTicket',
    'updateTicket',
    'deleteTicket',
    'getTicketCategories',
    'getTicketPriorities',
    'getTicketStatuses',
    'getServiceCatalogs'
  ];
  
  console.log('--------- 工单API配置检查 ---------');
  let missingApis = [];
  
  ticketApis.forEach(apiName => {
    if (typeof api[apiName] === 'function') {
      console.log(`✅ ${apiName}: 已正确配置`);
    } else {
      console.log(`❌ ${apiName}: 未找到或配置错误`);
      missingApis.push(apiName);
    }
  });
  
  if (missingApis.length === 0) {
    console.log('工单API配置检查通过! ✨');
  } else {
    console.log(`发现 ${missingApis.length} 个API配置问题，请检查 web/src/api/ticket.js 文件`);
  }
  
  // 检查路由配置
  const router = app._instance.appContext.config.globalProperties.$router;
  const routes = router.getRoutes();
  
  console.log('\n--------- 工单路由配置检查 ---------');
  const ticketRoute = routes.find(r => r.name === 'Ticket');
  
  if (ticketRoute) {
    console.log('✅ 工单管理路由已正确注册');
    console.log(`路径: ${ticketRoute.path}`);
    console.log(`子路由数量: ${ticketRoute.children?.length || 0}`);
    
    if (ticketRoute.children) {
      ticketRoute.children.forEach(child => {
        console.log(`  - ${child.name}: ${child.path}`);
      });
    }
  } else {
    console.log('❌ 未找到工单管理路由，请检查菜单配置和路由注册');
  }
}

// 执行测试
testTicketApis();

// 使用说明:
// 1. 打开前端页面并登录系统
// 2. 打开浏览器开发者工具(F12)
// 3. 切换到控制台(Console)选项卡
// 4. 复制此文件的全部内容并粘贴到控制台中
// 5. 按Enter键执行
// 6. 查看API和路由配置检查结果 