import asyncio
import sys
import json
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from app.discovery.connectors.zabbix_connector import ZabbixHost, ZabbixDataMapper
from app.discovery.engine import ZabbixProcessor, CMDBDiscoveryEngine
from tortoise import Tortoise
from datetime import datetime

async def test_single_item_creation():
    """测试单个配置项的创建过程"""
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 测试单个配置项创建过程 ===")
    
    try:
        # 1. 创建模拟的Zabbix主机数据
        print("1. 创建模拟Zabbix主机数据...")
        mock_host = ZabbixHost(
            hostid="99999",  # 使用一个不太可能重复的ID
            host="test-fix-verification-host",
            name="Test Fix Verification Host",
            status=0,
            interfaces=[
                {"ip": "***************", "port": "10050", "type": "1"},
                {"ip": "*************", "port": "10050", "type": "1"}
            ],
            groups=[{"name": "Test Group"}],
            inventory={
                "os": "Linux",
                "os_full": "Ubuntu 22.04.3 LTS",
                "hardware_full": "Intel Xeon E5-2680 v4",
                "memory": "64GB",
                "location": "Test Data Center",
                "serialno_a": "TEST123456789"
            },
            items=[],
            triggers=[]
        )
        
        # 2. 通过ZabbixDataMapper映射
        print("2. 通过ZabbixDataMapper映射...")
        mapped_data = ZabbixDataMapper.map_host_to_ci(mock_host)
        print(f"   Asset Tag: {mapped_data.get('asset_tag')}")
        print(f"   External ID: {mapped_data.get('external_id')}")
        print(f"   Hostname: {mapped_data.get('hostname')}")
        print(f"   IP Addresses: {mapped_data.get('ip_addresses')}")
        print(f"   Operating System: {mapped_data.get('operating_system')}")
        
        # 3. 通过ZabbixProcessor处理
        print("3. 通过ZabbixProcessor处理...")
        engine = CMDBDiscoveryEngine()
        processor = ZabbixProcessor(engine)
        
        # 标准化数据
        normalized_data = processor.normalize_data(mock_host, "zabbix")
        
        # 映射到CMDB模式
        cmdb_data = processor.map_to_cmdb_schema(normalized_data)
        print(f"   CMDB Asset Tag: {cmdb_data.get('asset_tag')}")
        print(f"   CMDB External ID: {cmdb_data.get('external_id')}")
        print(f"   CMDB Data Source: {cmdb_data.get('data_source')}")
        
        # 4. 检查是否已存在相同的配置项
        print("4. 检查是否已存在相同配置项...")
        existing_item = await ConfigurationItem.filter(
            asset_tag=cmdb_data.get('asset_tag')
        ).first()
        
        if existing_item:
            print(f"   发现已存在的配置项，将删除后重新创建...")
            await existing_item.delete()
        
        # 5. 创建新的配置项
        print("5. 创建新的配置项...")
        
        # 移除不属于模型的字段
        model_data = cmdb_data.copy()
        model_data.pop("id", None)
        
        # 添加必要的时间戳
        model_data["last_discovered_at"] = datetime.now()
        
        new_item = await ConfigurationItem.create(**model_data)
        print(f"   ✅ 成功创建配置项: {new_item.name} (ID: {new_item.id})")
        
        # 6. 验证创建的配置项
        print("6. 验证创建的配置项...")
        created_item = await ConfigurationItem.filter(id=new_item.id).first()
        
        print(f"   Asset Tag: {created_item.asset_tag}")
        print(f"   External ID: {created_item.external_id}")
        print(f"   Hostname: {created_item.hostname}")
        print(f"   IP Addresses: {created_item.ip_addresses}")
        print(f"   Operating System: {created_item.operating_system}")
        print(f"   Data Source: {created_item.data_source}")
        print(f"   Created At: {created_item.created_at}")
        
        # 7. 验证修复效果
        print("7. 验证修复效果...")
        issues = []
        
        if not created_item.external_id:
            issues.append("external_id为空")
        if not created_item.ip_addresses:
            issues.append("ip_addresses为空")
        if not created_item.operating_system or created_item.operating_system == '未知':
            issues.append("operating_system为空或未知")
        if not created_item.data_source:
            issues.append("data_source为空")
        
        if issues:
            print(f"   ❌ 发现问题: {', '.join(issues)}")
            return False
        else:
            print("   ✅ 所有字段都正确填充！")
            
            # 显示修复前后对比
            print("\n=== 修复效果对比 ===")
            print("修复前的问题:")
            print("  - external_id: 100%为空")
            print("  - operating_system: 50%为空")
            print("  - IP地址重复问题")
            
            print("修复后的结果:")
            print(f"  - external_id: ✅ {created_item.external_id}")
            print(f"  - operating_system: ✅ {created_item.operating_system}")
            print(f"  - IP地址: ✅ {created_item.ip_addresses} (已验证)")
            
            return True
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    success = asyncio.run(test_single_item_creation())
    if success:
        print("\n🎉 数据映射修复验证成功！")
    else:
        print("\n❌ 数据映射仍需进一步修复")
