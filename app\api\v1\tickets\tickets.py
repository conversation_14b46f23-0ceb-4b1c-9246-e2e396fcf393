from typing import Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, Query, Path, HTTPException, status
from app.controllers.ticket import TicketController
from app.schemas.ticket import (
    TicketCreate,
    TicketUpdate,
    TicketResponse,
    TicketListResponse
)
from app.core.dependency import AuthControl, DependPermisson
from app.models.admin import User

router = APIRouter()

@router.get("/", response_model=TicketListResponse, summary="获取工单列表", dependencies=[DependPermisson])
async def list_tickets(
    skip: int = Query(0, ge=0, description="跳过条数"),
    limit: int = Query(10, ge=1, le=100, description="每页数量"),
    category_id: Optional[int] = Query(None, description="分类ID筛选"),
    priority_id: Optional[int] = Query(None, description="优先级ID筛选"),
    status_id: Optional[int] = Query(None, description="状态ID筛选"),
    creator_id: Optional[int] = Query(None, description="创建人ID筛选"),
    assignee_id: Optional[int] = Query(None, description="处理人ID筛选"),
    service_id: Optional[int] = Query(None, description="服务目录ID筛选"),
    search: Optional[str] = Query(None, description="标题和描述搜索"),
    created_start: Optional[date] = Query(None, description="创建时间开始日期"),
    created_end: Optional[date] = Query(None, description="创建时间结束日期"),
    updated_start: Optional[date] = Query(None, description="更新时间开始日期"),
    updated_end: Optional[date] = Query(None, description="更新时间结束日期"),
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取工单列表"""
    items, total = await TicketController.get_tickets(
        skip=skip,
        limit=limit,
        category_id=category_id,
        priority_id=priority_id,
        status_id=status_id,
        creator_id=creator_id,
        assignee_id=assignee_id,
        service_id=service_id,
        search=search,
        created_start=created_start,
        created_end=created_end,
        updated_start=updated_start,
        updated_end=updated_end
    )
    return {
        "total": total,
        "items": items
    }

@router.post("/", response_model=TicketResponse, status_code=status.HTTP_201_CREATED, summary="创建工单", dependencies=[DependPermisson])
async def create_ticket(
    ticket_data: TicketCreate,
    current_user: User = Depends(AuthControl.is_authed)
):
    """创建工单"""
    # 设置创建者为当前用户
    ticket_data.creator_id = current_user.id
    ticket = await TicketController.create_ticket(ticket_data)
    # 转换为字典以避免ORM对象序列化问题
    ticket_dict = TicketController._ticket_to_dict(ticket)
    return ticket_dict

@router.get("/{ticket_id}", response_model=TicketResponse, summary="获取工单详情", dependencies=[DependPermisson])
async def get_ticket(
    ticket_id: int = Path(..., description="工单ID"),
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取工单详情"""
    ticket = await TicketController.get_ticket(ticket_id)
    # 转换为字典以避免ORM对象序列化问题
    return TicketController._ticket_to_dict(ticket)

@router.put("/{ticket_id}", response_model=TicketResponse, summary="更新工单", dependencies=[DependPermisson])
async def update_ticket(
    ticket_data: TicketUpdate,
    ticket_id: int = Path(..., description="工单ID"),
    current_user: User = Depends(AuthControl.is_authed)
):
    """更新工单"""
    ticket = await TicketController.update_ticket(ticket_id, ticket_data)
    # 转换为字典以避免ORM对象序列化问题
    return TicketController._ticket_to_dict(ticket)

@router.delete("/{ticket_id}", status_code=status.HTTP_204_NO_CONTENT, summary="删除工单", dependencies=[DependPermisson])
async def delete_ticket(
    ticket_id: int = Path(..., description="工单ID"),
    current_user: User = Depends(AuthControl.is_authed)
):
    """删除工单"""
    await TicketController.delete_ticket(ticket_id) 