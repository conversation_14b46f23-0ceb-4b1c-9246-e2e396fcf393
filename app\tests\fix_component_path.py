import pymysql
from datetime import datetime

# 使用配置文件中的真实数据库配置
conn = pymysql.connect(
    host='************',
    port=3306,
    user='backup_user',
    password='v+SCbs/6LsYNovFngEY=',
    database='fastapi_user',
    charset='utf8mb4'
)

cursor = conn.cursor()

# 获取当前时间
now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

try:
    # 获取工单列表菜单
    cursor.execute("SELECT id, name, path, component, parent_id FROM menu WHERE name = '工单列表'")
    ticket_list_menu = cursor.fetchone()
    
    if ticket_list_menu:
        menu_id = ticket_list_menu[0]
        current_component = ticket_list_menu[3]
        print(f"工单列表菜单ID: {menu_id}, 当前组件路径: {current_component}")
        
        # 修复组件路径 - 根据前端route.js配置
        cursor.execute("""
        UPDATE menu 
        SET component = %s, 
            updated_at = %s
        WHERE id = %s
        """, ('./TicketList.vue', now, menu_id))
        
        print(f"更新工单列表菜单组件路径，影响行数: {cursor.rowcount}")
    else:
        print("未找到工单列表菜单")
    
    # 获取工单管理目录菜单
    cursor.execute("SELECT id, name, path, component, redirect FROM menu WHERE name = '工单管理'")
    ticket_menu = cursor.fetchone()
    
    if ticket_menu:
        menu_id = ticket_menu[0]
        current_component = ticket_menu[3]
        print(f"工单管理菜单ID: {menu_id}, 当前组件路径: {current_component}")
        
        # 修复组件路径 - 确保为Layout
        cursor.execute("""
        UPDATE menu 
        SET component = %s, 
            updated_at = %s
        WHERE id = %s
        """, ('Layout', now, menu_id))
        
        print(f"更新工单管理目录组件路径，影响行数: {cursor.rowcount}")
    else:
        print("未找到工单管理目录菜单")
    
    # 提交事务
    conn.commit()
    
    # 查询已更新的工单菜单
    print("\n更新后的工单相关菜单:")
    cursor.execute("SELECT id, name, path, component, redirect FROM menu WHERE name LIKE '%工单%'")
    updated_menus = cursor.fetchall()
    
    for menu in updated_menus:
        print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, 跳转: {menu[4]}")

except Exception as e:
    # 发生错误时回滚
    conn.rollback()
    print(f"发生错误: {e}")

finally:
    # 关闭数据库连接
    cursor.close()
    conn.close()

print("\n组件路径已修复。请刷新前端页面，清除浏览器缓存后重新登录系统尝试访问工单管理功能。")
print("如果页面仍然显示404，请执行以下步骤:")
print("1. 重启后端服务")
print("2. 重启前端服务")
print("3. 清除浏览器缓存并重新登录") 