# -*- coding: utf-8 -*-
"""
标准化API响应工具

提供统一的响应格式和工具函数
"""

from typing import Any, Optional, Dict
from fastapi.responses import JSONResponse


def success(
    data: Any = None,
    message: str = "操作成功",
    code: int = 200
) -> Dict[str, Any]:
    """
    成功响应
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 状态码
        
    Returns:
        标准化的成功响应
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": True
    }


def fail(
    message: str = "操作失败",
    code: int = 400,
    data: Any = None
) -> Dict[str, Any]:
    """
    失败响应
    
    Args:
        message: 错误消息
        code: 错误码
        data: 额外数据
        
    Returns:
        标准化的错误响应
    """
    return {
        "code": code,
        "message": message,
        "data": data,
        "success": False
    }


def response_json(
    data: Any = None,
    message: str = "操作成功",
    code: int = 200,
    success: bool = True,
    status_code: int = 200
) -> JSONResponse:
    """
    JSON响应
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 业务码
        success: 是否成功
        status_code: HTTP状态码
        
    Returns:
        JSONResponse对象
    """
    return JSONResponse(
        status_code=status_code,
        content={
            "code": code,
            "message": message,
            "data": data,
            "success": success
        }
    )


class APIResponse:
    """API响应类"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """成功响应"""
        return success(data=data, message=message)
    
    @staticmethod
    def fail(message: str = "操作失败", code: int = 400) -> Dict[str, Any]:
        """失败响应"""
        return fail(message=message, code=code)
    
    @staticmethod
    def error(message: str = "系统错误", code: int = 500) -> Dict[str, Any]:
        """错误响应"""
        return fail(message=message, code=code)
    
    @staticmethod
    def not_found(message: str = "资源未找到") -> Dict[str, Any]:
        """404响应"""
        return fail(message=message, code=404)
    
    @staticmethod
    def forbidden(message: str = "访问被拒绝") -> Dict[str, Any]:
        """403响应"""
        return fail(message=message, code=403)
    
    @staticmethod
    def unauthorized(message: str = "未授权访问") -> Dict[str, Any]:
        """401响应"""
        return fail(message=message, code=401) 