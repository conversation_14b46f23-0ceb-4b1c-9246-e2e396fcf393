# -*- coding: utf-8 -*-
"""
Celery应用配置

初始化Celery应用实例，配置任务队列和调度
"""

from celery import Celery
from celery.schedules import crontab
from app.settings.config import get_celery_config

# 创建Celery应用实例
celery_app = Celery("vue_fastapi_admin")

# 从配置加载Celery设置
celery_config = get_celery_config()
celery_app.config_from_object(celery_config)

# 自动发现任务模块
celery_app.autodiscover_tasks([
    'app.discovery.tasks',
    'app.core.tasks'
])

# 配置定期任务
celery_app.conf.beat_schedule = {
    # CMDB发现任务
    'prometheus-discovery': {
        'task': 'app.discovery.tasks.run_prometheus_discovery',
        'schedule': crontab(minute='*/30'),  # 每30分钟执行一次
        'options': {'queue': 'discovery'}
    },
    'zabbix-discovery': {
        'task': 'app.discovery.tasks.run_zabbix_discovery', 
        'schedule': crontab(minute='0'),     # 每小时执行一次
        'options': {'queue': 'discovery'}
    },
    # 健康检查任务
    'discovery-health-check': {
        'task': 'app.discovery.tasks.health_check_task',
        'schedule': crontab(minute='*/5'),   # 每5分钟执行一次
        'options': {'queue': 'monitoring'}
    },
    # 数据清理任务
    'cleanup-old-jobs': {
        'task': 'app.discovery.tasks.cleanup_old_jobs',
        'schedule': crontab(hour='2', minute='0'),  # 每天凌晨2点执行
        'options': {'queue': 'maintenance'}
    }
}

# 配置队列路由
celery_app.conf.task_routes = {
    'app.discovery.tasks.*': {'queue': 'discovery'},
    'app.core.tasks.*': {'queue': 'default'},
}

# 其他Celery配置
celery_app.conf.update(
    task_track_started=True,
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,
    result_expires=3600,  # 结果保存1小时
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
) 