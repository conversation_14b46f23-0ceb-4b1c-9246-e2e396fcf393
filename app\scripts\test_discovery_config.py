#!/usr/bin/env python3
"""
CMDB发现配置测试脚本

此脚本用于验证CMDB发现相关配置是否正确加载，
以及Prometheus连接是否可用。

使用方法:
    python app/scripts/test_discovery_config.py
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.settings.config import (
    settings, 
    get_discovery_config,
    get_prometheus_config,
    get_zabbix_config,
    get_celery_config,
    get_logging_config
)


def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    # 基本配置
    print(f"应用名称: {settings.APP_TITLE}")
    print(f"Prometheus URL: {settings.PROMETHEUS_URL}")
    print(f"发现引擎状态: {'启用' if settings.DISCOVERY_ENABLED else '禁用'}")
    print(f"最大并发任务数: {settings.DISCOVERY_MAX_CONCURRENT_JOBS}")
    
    # 检查Prometheus配置
    print("\n=== Prometheus配置 ===")
    prometheus_config = get_prometheus_config()
    print(f"URL: {prometheus_config['url']}")
    print(f"超时时间: {prometheus_config['timeout']}秒")
    print(f"启用的任务: {', '.join(prometheus_config['enabled_jobs'])}")
    print(f"排除的任务: {', '.join(prometheus_config['excluded_jobs'])}")
    
    # 检查Zabbix配置
    print("\n=== Zabbix配置 ===")
    zabbix_config = get_zabbix_config()
    print(f"URL: {zabbix_config['url']}")
    print(f"用户名: {zabbix_config['username']}")
    print(f"启用的主机组: {', '.join(zabbix_config['enabled_groups'])}")
    
    # 检查Celery配置
    print("\n=== Celery配置 ===")
    celery_config = get_celery_config()
    print(f"Broker URL: {celery_config['broker_url']}")
    print(f"Result Backend: {celery_config['result_backend']}")
    print(f"任务超时: {celery_config['task_time_limit']}秒")
    
    print("\n✅ 配置加载测试完成")


async def test_prometheus_connection():
    """测试Prometheus连接"""
    print("\n=== 测试Prometheus连接 ===")
    
    try:
        from app.discovery.connectors.prometheus_connector import PrometheusAPIClient
        
        prometheus_config = get_prometheus_config()
        url = prometheus_config['url']
        timeout = prometheus_config['timeout']
        
        print(f"正在连接到: {url}")
        
        async with PrometheusAPIClient(url, timeout) as client:
            # 测试基本查询
            result = await client.query("up")
            
            print(f"✅ Prometheus连接成功!")
            print(f"查询结果数量: {len(result)}")
            
            # 获取目标信息
            targets = await client.get_targets()
            active_targets = [t for t in targets if t.get('health') == 'up']
            
            print(f"总监控目标数: {len(targets)}")
            print(f"活跃目标数: {len(active_targets)}")
            
            # 显示前5个活跃目标
            if active_targets:
                print("\n前5个活跃监控目标:")
                for target in active_targets[:5]:
                    job = target.get('labels', {}).get('job', 'unknown')
                    instance = target.get('labels', {}).get('instance', 'unknown')
                    print(f"  - {job}: {instance}")
            
            return True
            
    except Exception as e:
        print(f"❌ Prometheus连接失败: {str(e)}")
        return False


async def test_redis_connection():
    """测试Redis连接"""
    print("\n=== 测试Redis连接 ===")
    
    try:
        import redis.asyncio as aioredis
        from app.settings.config import settings
        
        # 解析Redis URL
        redis_url = settings.REDIS_URL
        print(f"正在连接到: {redis_url.replace(':CjK1rry1as@', ':***@')}")
        
        # 创建Redis连接
        redis_client = aioredis.from_url(redis_url, decode_responses=True)
        
        # 测试连接
        await redis_client.ping()
        
        # 测试基本操作
        await redis_client.set("test_key", "test_value", ex=10)
        value = await redis_client.get("test_key")
        
        if value == "test_value":
            print("✅ Redis连接成功!")
            print("✅ Redis读写操作正常")
            
            # 清理测试数据
            await redis_client.delete("test_key")
            
            # 获取Redis信息
            info = await redis_client.info()
            print(f"Redis版本: {info.get('redis_version', 'unknown')}")
            print(f"内存使用: {info.get('used_memory_human', 'unknown')}")
        
        await redis_client.aclose()
        return True
        
    except Exception as e:
        print(f"❌ Redis连接失败: {str(e)}")
        return False


async def test_zabbix_connection():
    """测试Zabbix连接"""
    print("\n=== 测试Zabbix连接 ===")
    
    try:
        from app.discovery.connectors.zabbix_connector import ZabbixAPIClient
        from app.settings.config import get_zabbix_config
        
        zabbix_config = get_zabbix_config()
        url = zabbix_config['url']
        username = zabbix_config['username']
        
        print(f"正在连接到: {url}")
        print(f"用户名: {username}")
        
        async with ZabbixAPIClient(url, username, zabbix_config['password']) as client:
            # 测试连接（通过获取主机组来验证登录成功）
            print(f"✅ Zabbix连接成功!")
            print(f"认证token: {client.auth_token[:20]}..." if client.auth_token else "无")
            
            # 获取主机组信息
            groups = await client._make_request("hostgroup.get", {"output": ["groupid", "name"]})
            enabled_groups = zabbix_config['enabled_groups']
            
            print(f"总主机组数: {len(groups)}")
            
            # 检查启用的主机组是否存在
            existing_groups = []
            for group in groups:
                if group.get('name') in enabled_groups:
                    existing_groups.append(group['name'])
            
            if existing_groups:
                print(f"已配置的主机组: {', '.join(existing_groups)}")
            else:
                print("⚠️  配置的主机组在Zabbix中未找到")
        
        return True
        
    except Exception as e:
        print(f"❌ Zabbix连接失败: {str(e)}")
        return False


async def test_discovery_engine():
    """测试发现引擎"""
    print("\n=== 测试发现引擎 ===")
    
    try:
        from app.discovery.engine import create_discovery_engine
        
        # 创建发现引擎（使用默认配置）
        engine = create_discovery_engine()
        
        print("✅ 发现引擎创建成功")
        print(f"已注册的连接器: {list(engine.connectors.keys())}")
        
        # 测试所有连接
        print("\n测试数据源连接...")
        connection_results = await engine.test_all_connections()
        
        for source, result in connection_results.items():
            status = "✅ 成功" if result["success"] else "❌ 失败"
            message = result.get("message", "")
            print(f"  {source}: {status} - {message}")
        
        return True
        
    except Exception as e:
        print(f"❌ 发现引擎测试失败: {str(e)}")
        return False


def test_service_discovery_rules():
    """测试服务发现规则"""
    print("\n=== 测试服务发现规则 ===")
    
    discovery_config = get_discovery_config()
    prometheus_config = discovery_config['prometheus']
    rules = prometheus_config.get('service_discovery_rules', {})
    
    print(f"配置的服务发现规则数量: {len(rules)}")
    
    for service_type, rule in rules.items():
        print(f"\n{service_type}:")
        print(f"  作业模式: {rule['job_pattern']}")
        print(f"  名称模板: {rule['name_template']}")
        print(f"  CI类型: {rule['ci_type']}")


def print_environment_info():
    """打印环境信息"""
    print("=== 环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print(f"项目根目录: {project_root}")
    
    # 检查环境变量
    env_vars = [
        'ZABBIX_URL', 'ZABBIX_USERNAME', 'ZABBIX_PASSWORD',
        'REDIS_URL', 'CELERY_BROKER_URL', 'CELERY_RESULT_BACKEND',
        'DISCOVERY_LOG_LEVEL', 'ALERT_WEBHOOK_URL'
    ]
    
    print("\n环境变量:")
    for var in env_vars:
        value = os.getenv(var, '未设置')
        # 隐藏敏感信息
        if 'password' in var.lower() and value != '未设置':
            value = '***'
        print(f"  {var}: {value}")


async def main():
    """主测试函数"""
    print("CMDB发现配置测试")
    print("=" * 50)
    
    # 打印环境信息
    print_environment_info()
    print()
    
    # 测试配置加载
    test_config_loading()
    
    # 测试服务发现规则
    test_service_discovery_rules()
    
    # 测试各个组件连接
    prometheus_ok = await test_prometheus_connection()
    redis_ok = await test_redis_connection()
    zabbix_ok = await test_zabbix_connection()
    
    # 测试发现引擎
    engine_ok = await test_discovery_engine()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"  Prometheus连接: {'✅ 通过' if prometheus_ok else '❌ 失败'}")
    print(f"  Redis连接: {'✅ 通过' if redis_ok else '❌ 失败'}")
    print(f"  Zabbix连接: {'✅ 通过' if zabbix_ok else '❌ 失败'}")
    print(f"  发现引擎: {'✅ 通过' if engine_ok else '❌ 失败'}")
    
    # 检查成功数量
    success_count = sum([prometheus_ok, redis_ok, zabbix_ok, engine_ok])
    total_tests = 4
    
    if success_count == total_tests:
        print("\n🎉 所有测试通过! CMDB发现功能已完全准备就绪。")
        return 0
    elif success_count >= 2:
        print(f"\n⚠️  {success_count}/{total_tests} 项测试通过，基本功能可用，建议修复失败项目。")
        return 0
    else:
        print(f"\n❌ 仅 {success_count}/{total_tests} 项测试通过，请检查配置和网络连接。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 