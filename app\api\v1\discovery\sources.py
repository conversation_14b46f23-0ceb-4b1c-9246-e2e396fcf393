# -*- coding: utf-8 -*-
"""
CMDB发现数据源管理API

提供数据源的创建、查询、测试连接等接口
"""

from fastapi import APIRouter, Depends, HTTPException
from datetime import datetime
import logging

from app.models.cmdb import DiscoverySource
from app.schemas.discovery import DiscoverySourceCreate
from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector
from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector
from app.settings.config import get_discovery_config
from app.core.response import success
from app.core.dependency import AuthControl

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("", summary="创建数据源")
async def create_discovery_source(
    source_data: DiscoverySourceCreate,
    current_user=Depends(AuthControl.is_authed)
):
    """创建新的发现数据源"""
    try:
        # 检查名称唯一性
        existing_source = await DiscoverySource.get_or_none(name=source_data.name)
        if existing_source:
            raise HTTPException(status_code=400, detail="数据源名称已存在")
        
        # 创建数据源
        source = await DiscoverySource.create(
            name=source_data.name,
            display_name=source_data.display_name,
            source_type=source_data.source_type,
            connection_config=source_data.connection_config,
            discovery_config=source_data.discovery_config,
            sync_interval=source_data.sync_interval,
            is_enabled=source_data.is_enabled
        )
        
        return success(data={
            "id": source.id,
            "name": source.name,
            "display_name": source.display_name,
            "source_type": source.source_type,
            "is_enabled": source.is_enabled,
            "message": "数据源创建成功"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建数据源失败: {str(e)}")


@router.get("", summary="获取数据源列表")
async def get_discovery_sources():
    """获取所有发现数据源"""
    try:
        sources = await DiscoverySource.all().order_by("created_at")
        
        sources_data = []
        for source in sources:
            source_data = {
                "id": source.id,
                "name": source.name,
                "display_name": source.display_name,
                "source_type": source.source_type,
                "is_enabled": source.is_enabled,
                "is_healthy": source.is_healthy,
                "sync_interval": source.sync_interval,
                "last_test_at": source.last_test_at.isoformat() if source.last_test_at else None,
                "last_discovery_at": source.last_discovery_at.isoformat() if source.last_discovery_at else None,
                "total_discoveries": source.total_discoveries,
                "successful_discoveries": source.successful_discoveries,
                "created_at": source.created_at.isoformat()
            }
            sources_data.append(source_data)
        
        return success(data=sources_data)
        
    except Exception as e:
        logger.error(f"获取数据源列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据源列表失败: {str(e)}")


@router.get("/{source_id}", summary="获取数据源详情")
async def get_discovery_source(source_id: int):
    """获取数据源详情"""
    try:
        source = await DiscoverySource.get_or_none(id=source_id)
        if not source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        return success(data={
            "id": source.id,
            "name": source.name,
            "display_name": source.display_name,
            "source_type": source.source_type,
            "connection_config": source.connection_config,
            "discovery_config": source.discovery_config,
            "sync_interval": source.sync_interval,
            "is_enabled": source.is_enabled,
            "is_healthy": source.is_healthy,
            "last_test_at": source.last_test_at.isoformat() if source.last_test_at else None,
            "last_discovery_at": source.last_discovery_at.isoformat() if source.last_discovery_at else None,
            "last_error": source.last_error,
            "total_discoveries": source.total_discoveries,
            "successful_discoveries": source.successful_discoveries,
            "created_at": source.created_at.isoformat(),
            "updated_at": source.updated_at.isoformat()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据源详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据源详情失败: {str(e)}")


@router.put("/{source_id}", summary="更新数据源")
async def update_discovery_source(
    source_id: int,
    source_data: DiscoverySourceCreate,
    current_user=Depends(AuthControl.is_authed)
):
    """更新数据源配置"""
    try:
        source = await DiscoverySource.get_or_none(id=source_id)
        if not source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 检查名称唯一性（排除自己）
        existing_source = await DiscoverySource.filter(
            name=source_data.name
        ).exclude(id=source_id).first()
        if existing_source:
            raise HTTPException(status_code=400, detail="数据源名称已存在")
        
        # 更新数据源
        source.name = source_data.name
        source.display_name = source_data.display_name
        source.source_type = source_data.source_type
        source.connection_config = source_data.connection_config
        source.discovery_config = source_data.discovery_config
        source.sync_interval = source_data.sync_interval
        source.is_enabled = source_data.is_enabled
        await source.save()
        
        return success(data={
            "id": source.id,
            "name": source.name,
            "display_name": source.display_name,
            "source_type": source.source_type,
            "is_enabled": source.is_enabled,
            "message": "数据源更新成功"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新数据源失败: {str(e)}")


@router.delete("/{source_id}", summary="删除数据源")
async def delete_discovery_source(
    source_id: int,
    current_user=Depends(AuthControl.is_authed)
):
    """删除数据源"""
    try:
        source = await DiscoverySource.get_or_none(id=source_id)
        if not source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 检查是否有相关的任务
        from app.models.cmdb import DiscoveryJob
        job_count = await DiscoveryJob.filter(discovery_source=source).count()
        if job_count > 0:
            raise HTTPException(
                status_code=400, 
                detail=f"无法删除：该数据源有 {job_count} 个相关任务记录"
            )
        
        await source.delete()
        
        return success(message="数据源删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除数据源失败: {str(e)}")


@router.post("/{source_id}/test", summary="测试数据源连接")
async def test_discovery_source(source_id: int):
    """测试数据源连接"""
    try:
        source = await DiscoverySource.get_or_none(id=source_id)
        if not source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 获取配置并创建连接器
        config = get_discovery_config()
        
        # 测试连接
        if source.source_type == "PROMETHEUS":
            connector = PrometheusDiscoveryConnector(config["prometheus"])
        elif source.source_type == "ZABBIX":
            connector = ZabbixDiscoveryConnector(config["zabbix"])
        else:
            raise HTTPException(status_code=400, detail="不支持的数据源类型")
        
        result = await connector.test_connection()
        
        # 更新数据源状态
        source.is_healthy = result.get("status") == "connected"
        source.last_test_at = datetime.now()
        if not source.is_healthy:
            source.last_error = result.get("error", "连接测试失败")
        else:
            source.last_error = None
        await source.save()
        
        return success(data=result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试数据源连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试连接失败: {str(e)}")


@router.post("/{source_id}/toggle", summary="启用/禁用数据源")
async def toggle_discovery_source(
    source_id: int,
    current_user=Depends(AuthControl.is_authed)
):
    """启用或禁用数据源"""
    try:
        source = await DiscoverySource.get_or_none(id=source_id)
        if not source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 切换状态
        source.is_enabled = not source.is_enabled
        await source.save()
        
        status_text = "启用" if source.is_enabled else "禁用"
        
        return success(data={
            "id": source.id,
            "name": source.name,
            "is_enabled": source.is_enabled,
            "message": f"数据源已{status_text}"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"切换数据源状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}") 