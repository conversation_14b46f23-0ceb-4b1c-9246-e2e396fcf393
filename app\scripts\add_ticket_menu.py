import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from tortoise import Tortoise, run_async
from app.models.admin import Menu, Role
from app.schemas.menus import MenuType

# MySQL 配置
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.mysql",
            "credentials": {
                "host": "************",
                "port": "3306",
                "user": "backup_user",
                "password": "v+SCbs/6LsYNovFngEY=",
                "database": "fastapi_user",
                "charset": "utf8mb4"
            }
        }
    },
    "apps": {
        "models": {
            "models": ["app.models.admin", "app.models.ticket", "aerich.models"],
            "default_connection": "default",
        }
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai",
}

async def add_ticket_menu():
    """添加工单管理菜单"""
    print("正在连接数据库...")
    
    try:
        # 初始化Tortoise ORM
        await Tortoise.init(config=TORTOISE_ORM)
        print("数据库连接成功")
        
        # 检查菜单是否已存在
        existing_menu = await Menu.filter(path="/ticket").first()
        if existing_menu:
            print(f"工单管理菜单已存在，ID: {existing_menu.id}, 名称: {existing_menu.name}")
            return
        
        # 创建工单管理父菜单
        ticket_menu = Menu(
            name="工单管理",
            menu_type=MenuType.MENU,
            icon="ph:ticket-bold",
            path="/ticket",
            order=3,
            parent_id=0,
            is_hidden=False,
            component="/ticket",
            keepalive=True,
            redirect="/ticket/list"
        )
        await ticket_menu.save()
        print(f"创建工单管理父菜单成功，ID: {ticket_menu.id}")
        
        # 创建工单列表子菜单
        ticket_list_menu = Menu(
            name="工单列表",
            menu_type=MenuType.MENU,
            icon="ph:list-bold",
            path="/ticket/list",
            order=1,
            parent_id=ticket_menu.id,
            is_hidden=False,
            component="/ticket/list",
            keepalive=True,
            redirect=""
        )
        await ticket_list_menu.save()
        print(f"创建工单列表子菜单成功，ID: {ticket_list_menu.id}")
        
        # 将菜单分配给所有角色
        roles = await Role.all()
        for role in roles:
            await role.menus.add(ticket_menu)
            await role.menus.add(ticket_list_menu)
            print(f"已将工单菜单分配给角色: {role.name}")
        
        print("工单管理菜单添加成功！")
    
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭连接
        await Tortoise.close_connections()
        print("数据库连接已关闭")

if __name__ == "__main__":
    print("开始添加工单管理菜单...")
    run_async(add_ticket_menu())
    print("添加完成") 