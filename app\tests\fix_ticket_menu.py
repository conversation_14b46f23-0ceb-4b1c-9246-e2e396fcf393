import pymysql
from datetime import datetime

# 使用配置文件中的真实数据库配置
conn = pymysql.connect(
    host='************',
    port=3306,
    user='backup_user',
    password='v+SCbs/6LsYNovFngEY=',
    database='fastapi_user',
    charset='utf8mb4'
)

cursor = conn.cursor()

# 获取当前时间
now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

try:
    # 先检查工单菜单的当前状态
    cursor.execute("SELECT id, name, path, component, redirect, icon, menu_type, `order`, parent_id FROM menu WHERE name LIKE '%工单%'")
    ticket_menus = cursor.fetchall()
    
    print("当前工单相关菜单:")
    parent_id = None
    for menu in ticket_menus:
        print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
              f"跳转: {menu[4]}, 图标: {menu[5]}, 类型: {menu[6]}, 排序: {menu[7]}, 父级ID: {menu[8]}")
        if menu[1] == '工单管理':
            parent_id = menu[0]
    
    # 如果存在工单菜单，则更新它们
    if ticket_menus:
        # 更新工单管理目录
        cursor.execute("""
        UPDATE menu 
        SET path = '/ticket', 
            component = 'Layout', 
            redirect = '/ticket/list', 
            icon = 'ph:ticket-bold',
            `order` = 8,
            updated_at = %s
        WHERE name = '工单管理'
        """, (now,))
        print(f"更新工单管理目录，影响行数: {cursor.rowcount}")
        
        # 更新工单列表子菜单
        cursor.execute("""
        UPDATE menu 
        SET path = 'list', 
            component = '/ticket/TicketList', 
            icon = 'ph:list-bold',
            `order` = 1,
            updated_at = %s
        WHERE name = '工单列表' AND parent_id = %s
        """, (now, parent_id))
        print(f"更新工单列表子菜单，影响行数: {cursor.rowcount}")
    else:
        print("未找到工单相关菜单，将创建新菜单")
        
        # 添加工单管理菜单目录
        cursor.execute("""
        INSERT INTO menu (created_at, updated_at, name, path, component, redirect, icon, menu_type, `order`, parent_id, is_hidden, keepalive)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (now, now, '工单管理', '/ticket', 'Layout', '/ticket/list', 'ph:ticket-bold', 'catalog', 8, 0, 0, 0))
        
        # 获取新增菜单目录的ID
        ticket_menu_id = conn.insert_id()
        print(f"添加工单管理菜单目录，ID: {ticket_menu_id}")
        
        # 添加工单列表子菜单
        cursor.execute("""
        INSERT INTO menu (created_at, updated_at, name, path, component, redirect, icon, menu_type, `order`, parent_id, is_hidden, keepalive)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (now, now, '工单列表', 'list', '/ticket/TicketList', '', 'ph:list-bold', 'menu', 1, ticket_menu_id, 0, 1))
        print(f"添加工单列表子菜单，ID: {conn.insert_id()}")
    
    # 确保管理员角色有菜单权限
    cursor.execute("SELECT id FROM role WHERE name = '管理员' LIMIT 1")
    admin_role = cursor.fetchone()
    admin_role_id = admin_role[0] if admin_role else 1
    
    # 获取工单相关菜单ID
    cursor.execute("SELECT id FROM menu WHERE name LIKE '%工单%'")
    menu_ids = cursor.fetchall()
    
    for menu_id in menu_ids:
        # 检查权限是否已存在
        cursor.execute("SELECT 1 FROM role_menu WHERE role_id = %s AND menu_id = %s", (admin_role_id, menu_id[0]))
        exists = cursor.fetchone()
        
        if not exists:
            cursor.execute("""
            INSERT INTO role_menu (role_id, menu_id)
            VALUES (%s, %s)
            """, (admin_role_id, menu_id[0]))
            print(f"为角色 {admin_role_id} 添加菜单权限 {menu_id[0]}")
        else:
            print(f"角色 {admin_role_id} 已有菜单权限 {menu_id[0]}")
    
    # 检查前端路由对应的组件文件是否存在
    print("\n请确保前端项目中存在以下组件文件:")
    print("- web/src/views/ticket/TicketList.vue")
    
    # 检查web/src/router/routes目录下是否有ticket相关的路由配置
    print("\n请确保前端项目的路由配置正确，检查:")
    print("- web/src/views/ticket/route.js")
    
    # 提交事务
    conn.commit()
    
    # 查询已更新的工单菜单
    print("\n更新后的工单相关菜单:")
    cursor.execute("SELECT id, name, path, component, redirect, icon, menu_type, `order`, parent_id FROM menu WHERE name LIKE '%工单%'")
    updated_menus = cursor.fetchall()
    
    for menu in updated_menus:
        print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
              f"跳转: {menu[4]}, 图标: {menu[5]}, 类型: {menu[6]}, 排序: {menu[7]}, 父级ID: {menu[8]}")

except Exception as e:
    # 发生错误时回滚
    conn.rollback()
    print(f"发生错误: {e}")

finally:
    # 关闭数据库连接
    cursor.close()
    conn.close()

print("\n菜单配置已修复。请刷新前端页面，清除浏览器缓存后重新登录系统尝试访问工单管理功能。")
print("如果页面仍然显示404，请检查以下几点:")
print("1. 前端组件文件是否存在并正确命名")
print("2. 前端路由配置是否正确")
print("3. 确认工单API是否能正常响应") 