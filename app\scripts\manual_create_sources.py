#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
手动创建发现数据源
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))


async def create_sources():
    """手动创建数据源"""
    print("🚀 手动创建发现数据源...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoverySource
        from app.settings.config import settings
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 删除现有的数据源（如果存在）
        await DiscoverySource.all().delete()
        print("  🧹 清理现有数据源")
        
        # 1. 创建Prometheus数据源
        prometheus_source = await DiscoverySource.create(
            name="prometheus",
            display_name="Prometheus监控系统",
            source_type="PROMETHEUS",  # 直接使用字符串
            connection_config={
                "url": "http://10.253.96.111:31891",
                "timeout": 30
            },
            discovery_config={
                "enabled_jobs": ["node-exporter", "mysql-exporter", "redis-exporter"],
                "excluded_jobs": ["prometheus"],
                "service_discovery_rules": {}
            },
            sync_interval=1800,
            is_enabled=True
        )
        print(f"  ✅ 创建Prometheus数据源: {prometheus_source.id}")
        
        # 2. 创建Zabbix数据源
        zabbix_source = await DiscoverySource.create(
            name="zabbix",
            display_name="Zabbix监控系统",
            source_type="ZABBIX",  # 直接使用字符串
            connection_config={
                "url": "http://************/zabbix/api_jsonrpc.php",
                "username": "Admin",
                "password": "zabbix"
            },
            discovery_config={
                "enabled_groups": ["Linux servers", "Windows servers"],
                "excluded_hosts": ["test-host"]
            },
            sync_interval=3600,
            is_enabled=True
        )
        print(f"  ✅ 创建Zabbix数据源: {zabbix_source.id}")
        
        # 验证创建结果
        sources = await DiscoverySource.all()
        print(f"\n📊 总计创建 {len(sources)} 个数据源:")
        for source in sources:
            print(f"  - {source.name} ({source.source_type}) - {source.display_name}")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据源失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        try:
            await Tortoise.close_connections()
        except:
            pass
        return False


if __name__ == "__main__":
    success = asyncio.run(create_sources())
    if success:
        print("\n🎉 数据源创建成功！")
    else:
        print("\n❌ 数据源创建失败！") 