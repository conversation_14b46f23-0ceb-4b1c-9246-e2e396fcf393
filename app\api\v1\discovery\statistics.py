# -*- coding: utf-8 -*-
"""
CMDB发现统计分析API

提供发现系统的统计分析接口
"""

from fastapi import APIRouter, HTTPException
from datetime import datetime, timedelta
import logging

from app.models.cmdb import (
    ConfigurationItem, DiscoverySource, DiscoveryJob, DiscoveryAuditLog,
    CITypeEnum, CIStatusEnum, JobStatusEnum
)
from app.core.response import success

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("", summary="获取发现统计")
async def get_discovery_statistics():
    """获取CMDB发现统计信息"""
    try:
        # 配置项统计
        total_cis = await ConfigurationItem.all().count()
        ci_by_type = {}
        for ci_type in CITypeEnum:
            count = await ConfigurationItem.filter(ci_type=ci_type).count()
            ci_by_type[ci_type.value] = count
        
        # 数据源统计
        total_sources = await DiscoverySource.all().count()
        enabled_sources = await DiscoverySource.filter(is_enabled=True).count()
        healthy_sources = await DiscoverySource.filter(is_healthy=True).count()
        
        # 任务统计
        total_jobs = await DiscoveryJob.all().count()
        running_jobs = await DiscoveryJob.filter(status=JobStatusEnum.RUNNING).count()
        
        # 最近24小时统计
        last_24h = datetime.now() - timedelta(hours=24)
        recent_jobs = await DiscoveryJob.filter(created_at__gte=last_24h).count()
        recent_successful = await DiscoveryJob.filter(
            status=JobStatusEnum.COMPLETED,
            created_at__gte=last_24h
        ).count()
        recent_failed = await DiscoveryJob.filter(
            status=JobStatusEnum.FAILED,
            created_at__gte=last_24h
        ).count()
        
        # 最新发现时间
        latest_discovery = await DiscoveryJob.filter(
            status=JobStatusEnum.COMPLETED
        ).order_by("-completed_at").first()
        
        return success(data={
            "configuration_items": {
                "total": total_cis,
                "by_type": ci_by_type
            },
            "data_sources": {
                "total": total_sources,
                "enabled": enabled_sources,
                "healthy": healthy_sources
            },
            "discovery_jobs": {
                "total": total_jobs,
                "running": running_jobs,
                "recent_24h": {
                    "total": recent_jobs,
                    "successful": recent_successful,
                    "failed": recent_failed,
                    "success_rate": (recent_successful / recent_jobs * 100) if recent_jobs > 0 else 0
                }
            },
            "latest_discovery": {
                "time": latest_discovery.completed_at.isoformat() if latest_discovery else None,
                "job_name": latest_discovery.job_name if latest_discovery else None
            },
            "updated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/dashboard", summary="获取仪表板数据")
async def get_dashboard_statistics():
    """获取仪表板展示数据"""
    try:
        # 基础统计
        total_cis = await ConfigurationItem.all().count()
        total_sources = await DiscoverySource.all().count()
        enabled_sources = await DiscoverySource.filter(is_enabled=True).count()
        
        # 最近7天的发现趋势
        trend_data = []
        for i in range(7):
            date = datetime.now() - timedelta(days=i)
            start_of_day = date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_day = date.replace(hour=23, minute=59, second=59, microsecond=999999)
            
            jobs_count = await DiscoveryJob.filter(
                created_at__gte=start_of_day,
                created_at__lte=end_of_day
            ).count()
            
            successful_count = await DiscoveryJob.filter(
                status=JobStatusEnum.COMPLETED,
                created_at__gte=start_of_day,
                created_at__lte=end_of_day
            ).count()
            
            trend_data.append({
                "date": date.strftime("%Y-%m-%d"),
                "jobs": jobs_count,
                "successful": successful_count
            })
        
        # 配置项类型分布
        ci_distribution = []
        for ci_type in CITypeEnum:
            count = await ConfigurationItem.filter(ci_type=ci_type).count()
            if count > 0:
                ci_distribution.append({
                    "type": ci_type.value,
                    "count": count,
                    "label": ci_type.label
                })
        
        # 数据源状态
        source_status = []
        sources = await DiscoverySource.all()
        for source in sources:
            ci_count = await ConfigurationItem.filter(discovery_source=source).count()
            source_status.append({
                "id": source.id,
                "name": source.name,
                "type": source.source_type,
                "enabled": source.is_enabled,
                "healthy": source.is_healthy,
                "ci_count": ci_count,
                "last_discovery": source.last_discovery_at.isoformat() if source.last_discovery_at else None
            })
        
        # 最近的任务
        recent_jobs = await DiscoveryJob.all().order_by("-created_at").limit(10)
        jobs_data = []
        for job in recent_jobs:
            jobs_data.append({
                "id": job.id,
                "name": job.job_name,
                "status": job.status,
                "created_at": job.created_at.isoformat(),
                "duration": job.execution_duration
            })
        
        return success(data={
            "overview": {
                "total_cis": total_cis,
                "total_sources": total_sources,
                "enabled_sources": enabled_sources,
                "discovery_rate": (enabled_sources / total_sources * 100) if total_sources > 0 else 0
            },
            "trend": trend_data,
            "ci_distribution": ci_distribution,
            "source_status": source_status,
            "recent_jobs": jobs_data,
            "updated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取仪表板数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")


@router.get("/sources/{source_id}/statistics", summary="获取数据源统计")
async def get_source_statistics(source_id: int):
    """获取特定数据源的统计信息"""
    try:
        # 检查数据源是否存在
        source = await DiscoverySource.get_or_none(id=source_id)
        if not source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 配置项统计
        total_cis = await ConfigurationItem.filter(discovery_source=source).count()
        
        # 按类型统计
        ci_by_type = {}
        for ci_type in CITypeEnum:
            count = await ConfigurationItem.filter(
                discovery_source=source,
                ci_type=ci_type
            ).count()
            if count > 0:
                ci_by_type[ci_type.value] = count
        
        # 任务统计
        total_jobs = await DiscoveryJob.filter(discovery_source=source).count()
        successful_jobs = await DiscoveryJob.filter(
            discovery_source=source,
            status=JobStatusEnum.COMPLETED
        ).count()
        failed_jobs = await DiscoveryJob.filter(
            discovery_source=source,
            status=JobStatusEnum.FAILED
        ).count()
        
        # 最近30天的发现历史
        last_30_days = datetime.now() - timedelta(days=30)
        recent_discoveries = await ConfigurationItem.filter(
            discovery_source=source,
            last_discovered_at__gte=last_30_days
        ).count()
        
        # 最新发现的配置项
        latest_cis = await ConfigurationItem.filter(
            discovery_source=source
        ).order_by("-last_discovered_at").limit(5)
        
        latest_cis_data = []
        for ci in latest_cis:
            latest_cis_data.append({
                "id": ci.id,
                "name": ci.name,
                "type": ci.ci_type,
                "last_discovered": ci.last_discovered_at.isoformat() if ci.last_discovered_at else None
            })
        
        return success(data={
            "source": {
                "id": source.id,
                "name": source.name,
                "type": source.source_type,
                "enabled": source.is_enabled,
                "healthy": source.is_healthy
            },
            "configuration_items": {
                "total": total_cis,
                "by_type": ci_by_type,
                "recent_discoveries": recent_discoveries
            },
            "discovery_jobs": {
                "total": total_jobs,
                "successful": successful_jobs,
                "failed": failed_jobs,
                "success_rate": (successful_jobs / total_jobs * 100) if total_jobs > 0 else 0
            },
            "latest_discovered": latest_cis_data,
            "updated_at": datetime.now().isoformat()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取数据源统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据源统计失败: {str(e)}")


@router.get("/audit/summary", summary="获取审计摘要")
async def get_audit_summary():
    """获取审计日志摘要"""
    try:
        # 最近24小时的操作统计
        last_24h = datetime.now() - timedelta(hours=24)
        
        create_count = await DiscoveryAuditLog.filter(
            operation_type="CREATE",
            created_at__gte=last_24h
        ).count()
        
        update_count = await DiscoveryAuditLog.filter(
            operation_type="UPDATE",
            created_at__gte=last_24h
        ).count()
        
        delete_count = await DiscoveryAuditLog.filter(
            operation_type="DELETE",
            created_at__gte=last_24h
        ).count()
        
        failed_operations = await DiscoveryAuditLog.filter(
            operation_status="FAILED",
            created_at__gte=last_24h
        ).count()
        
        # 最近的审计记录
        recent_audits = await DiscoveryAuditLog.all().order_by("-created_at").limit(10)
        
        audit_data = []
        for audit in recent_audits:
            audit_data.append({
                "id": audit.id,
                "operation_type": audit.operation_type,
                "operation_status": audit.operation_status,
                "ci_id": audit.ci_id,
                "error_message": audit.error_message,
                "created_at": audit.created_at.isoformat()
            })
        
        return success(data={
            "operations_24h": {
                "created": create_count,
                "updated": update_count,
                "deleted": delete_count,
                "failed": failed_operations,
                "total": create_count + update_count + delete_count
            },
            "recent_audits": audit_data,
            "updated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取审计摘要失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取审计摘要失败: {str(e)}")


@router.get("/performance", summary="获取性能统计")
async def get_performance_statistics():
    """获取发现系统性能统计"""
    try:
        # 平均任务执行时间
        completed_jobs = await DiscoveryJob.filter(
            status=JobStatusEnum.COMPLETED,
            execution_duration__not_isnull=True
        ).all()
        
        if completed_jobs:
            total_duration = sum(job.execution_duration for job in completed_jobs)
            avg_duration = total_duration / len(completed_jobs)
            max_duration = max(job.execution_duration for job in completed_jobs)
            min_duration = min(job.execution_duration for job in completed_jobs)
        else:
            avg_duration = max_duration = min_duration = 0
        
        # 发现效率统计
        total_discovered_items = sum(
            job.successful_items for job in completed_jobs if job.successful_items
        )
        
        # 最近的高性能任务
        fast_jobs = await DiscoveryJob.filter(
            status=JobStatusEnum.COMPLETED,
            execution_duration__lte=60  # 60秒以内完成的任务
        ).order_by("-completed_at").limit(5)
        
        fast_jobs_data = []
        for job in fast_jobs:
            fast_jobs_data.append({
                "id": job.id,
                "name": job.job_name,
                "duration": job.execution_duration,
                "items_discovered": job.successful_items,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None
            })
        
        return success(data={
            "execution_time": {
                "average": round(avg_duration, 2),
                "maximum": max_duration,
                "minimum": min_duration,
                "unit": "seconds"
            },
            "discovery_efficiency": {
                "total_jobs": len(completed_jobs),
                "total_items_discovered": total_discovered_items,
                "average_items_per_job": round(total_discovered_items / len(completed_jobs), 2) if completed_jobs else 0
            },
            "fast_jobs": fast_jobs_data,
            "updated_at": datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"获取性能统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取性能统计失败: {str(e)}") 