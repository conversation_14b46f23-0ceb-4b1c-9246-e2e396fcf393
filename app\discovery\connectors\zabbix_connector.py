# -*- coding: utf-8 -*-
"""
Zabbix API连接器

提供与Zabbix监控系统的API集成，用于自动发现主机、获取监控数据和配置信息。
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import aiohttp
import json
from dataclasses import dataclass

from app.settings.config import settings
from app.utils.exceptions import DiscoveryError

logger = logging.getLogger(__name__)


@dataclass
class ZabbixHost:
    """Zabbix主机数据结构"""
    hostid: str
    host: str
    name: str
    status: int
    interfaces: List[Dict]
    groups: List[Dict]
    inventory: Dict
    items: List[Dict]
    triggers: List[Dict]


class ZabbixAPIClient:
    """Zabbix API客户端"""
    
    def __init__(self, url: str, username: str, password: str):
        self.url = url.rstrip('/')
        self.username = username
        self.password = password
        self.auth_token = None
        self.session = None
        self._request_id = 0
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        await self.login()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.auth_token:
            await self.logout()
        if self.session:
            await self.session.close()
    
    async def _make_request(self, method: str, params: Dict = None) -> Dict:
        """发送API请求"""
        self._request_id += 1
        payload = {
            "jsonrpc": "2.0",
            "method": method,
            "params": params or {},
            "id": self._request_id
        }
        
        # 添加认证token（除了登录请求）
        if self.auth_token and method != "user.login":
            payload["auth"] = self.auth_token
        
        headers = {"Content-Type": "application/json"}
        
        try:
            async with self.session.post(
                f"{self.url}/api_jsonrpc.php",
                json=payload,
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status != 200:
                    raise DiscoveryError(f"HTTP错误: {response.status}")
                
                result = await response.json()
                
                if "error" in result:
                    error_msg = result["error"].get("data", result["error"].get("message", "未知错误"))
                    raise DiscoveryError(f"Zabbix API错误: {error_msg}")
                
                return result.get("result", {})
                
        except aiohttp.ClientError as e:
            raise DiscoveryError(f"网络错误: {str(e)}")
        except json.JSONDecodeError as e:
            raise DiscoveryError(f"JSON解析错误: {str(e)}")
    
    async def login(self) -> str:
        """登录并获取认证token"""
        logger.info(f"正在连接Zabbix: {self.url}")
        
        result = await self._make_request("user.login", {
            "user": self.username,
            "password": self.password
        })
        
        self.auth_token = result
        logger.info("Zabbix登录成功")
        return self.auth_token
    
    async def logout(self):
        """登出"""
        if self.auth_token:
            await self._make_request("user.logout")
            self.auth_token = None
            logger.info("Zabbix登出成功")
    
    async def get_hosts(self, **filters) -> List[Dict]:
        """获取主机列表"""
        params = {
            "output": ["hostid", "host", "name", "status"],
            "selectInterfaces": ["interfaceid", "ip", "dns", "port", "type"],
            "selectGroups": ["groupid", "name"],
            "selectInventory": "extend",
            **filters
        }
        
        hosts = await self._make_request("host.get", params)
        logger.debug(f"获取到 {len(hosts)} 个主机")
        return hosts
    
    async def get_host_items(self, hostid: str) -> List[Dict]:
        """获取主机的监控项"""
        params = {
            "hostids": hostid,
            "output": ["itemid", "name", "key_", "value_type", "units", "status"],
            "monitored": True
        }
        
        items = await self._make_request("item.get", params)
        return items
    
    async def get_host_triggers(self, hostid: str) -> List[Dict]:
        """获取主机的触发器"""
        params = {
            "hostids": hostid,
            "output": ["triggerid", "description", "status", "priority", "value"],
            "monitored": True,
            "active": True
        }
        
        triggers = await self._make_request("trigger.get", params)
        return triggers
    
    async def get_latest_data(self, hostid: str, item_keys: List[str] = None) -> Dict:
        """获取主机最新数据"""
        params = {
            "hostids": hostid,
            "output": ["itemid", "name", "key_", "lastvalue", "lastclock"],
            "monitored": True
        }
        
        if item_keys:
            params["search"] = {"key_": item_keys}
        
        items = await self._make_request("item.get", params)
        
        # 转换为字典格式，key为item_key
        latest_data = {}
        for item in items:
            latest_data[item["key_"]] = {
                "value": item.get("lastvalue"),
                "timestamp": datetime.fromtimestamp(int(item.get("lastclock", 0)))
            }
        
        return latest_data


class ZabbixDiscoveryConnector:
    """Zabbix发现连接器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.url = config["url"]
        self.username = config["username"]
        self.password = config["password"]
        self.enabled_groups = config.get("enabled_groups", [])
        self.excluded_hosts = config.get("excluded_hosts", [])
    
    async def discover_hosts(self) -> List[ZabbixHost]:
        """发现所有主机"""
        logger.info("开始从Zabbix发现主机...")
        
        hosts = []
        async with ZabbixAPIClient(self.url, self.username, self.password) as client:
            # 获取主机列表
            raw_hosts = await client.get_hosts()
            
            for host_data in raw_hosts:
                # 过滤排除的主机
                if host_data["host"] in self.excluded_hosts:
                    continue
                
                # 过滤主机组（如果配置了）
                if self.enabled_groups:
                    host_groups = [g["name"] for g in host_data.get("groups", [])]
                    if not any(group in host_groups for group in self.enabled_groups):
                        continue
                
                try:
                    # 获取详细信息
                    hostid = host_data["hostid"]
                    items = await client.get_host_items(hostid)
                    triggers = await client.get_host_triggers(hostid)
                    
                    zabbix_host = ZabbixHost(
                        hostid=hostid,
                        host=host_data["host"],
                        name=host_data["name"],
                        status=int(host_data["status"]),
                        interfaces=host_data.get("interfaces", []),
                        groups=host_data.get("groups", []),
                        inventory=host_data.get("inventory", {}),
                        items=items,
                        triggers=triggers
                    )
                    
                    hosts.append(zabbix_host)
                    
                except Exception as e:
                    logger.error(f"获取主机 {host_data['host']} 详细信息失败: {str(e)}")
                    continue
        
        logger.info(f"从Zabbix发现了 {len(hosts)} 个主机")
        return hosts
    
    async def get_host_performance_data(self, hostid: str) -> Dict[str, Any]:
        """获取主机性能数据"""
        async with ZabbixAPIClient(self.url, self.username, self.password) as client:
            # 定义关键性能指标
            key_metrics = [
                "system.cpu.util[,idle]",
                "vm.memory.util[pavailable]",
                "vfs.fs.size[/,pused]",
                "net.if.in[eth0]",
                "net.if.out[eth0]",
                "system.uptime",
                "agent.ping"
            ]
            
            return await client.get_latest_data(hostid, key_metrics)
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        try:
            async with ZabbixAPIClient(self.url, self.username, self.password) as client:
                # 通过获取主机组来测试连接（登录成功意味着连接正常）
                result = await client._make_request("hostgroup.get", {"output": ["groupid", "name"], "limit": 1})
                
                return {
                    "success": True,
                    "message": "连接成功",
                    "version": result,
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"连接失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }


class ZabbixDataMapper:
    """Zabbix数据映射器"""
    
    @staticmethod
    def map_host_to_ci(zabbix_host: ZabbixHost) -> Dict[str, Any]:
        """将Zabbix主机映射为CMDB配置项"""
        
        # 从接口信息提取IP地址，确保去重
        ip_addresses = []
        mac_addresses = []
        for interface in zabbix_host.interfaces:
            if interface.get("ip") and interface["ip"] not in ip_addresses:
                ip_addresses.append(interface["ip"])
            # MAC地址通常在inventory中
        
        # 从inventory提取硬件信息
        inventory = zabbix_host.inventory
        hardware_specs = {
            "cpu": {
                "model": inventory.get("hardware_full"),
                "count": inventory.get("cpu_num"),
                "cores": inventory.get("cpu_cores")
            },
            "memory": {
                "total": inventory.get("memory"),
                "type": inventory.get("memory_type")
            },
            "storage": {
                "total": inventory.get("disk_capacity")
            }
        }
        
        # 确定CI类型
        ci_type = "PHYSICAL_SERVER"
        if "virtual" in zabbix_host.host.lower() or "vm" in zabbix_host.host.lower():
            ci_type = "VIRTUAL_SERVER"
        
        # 确定运行状态
        operational_status = "运行中" if zabbix_host.status == 0 else "离线"
        
        # 修复主机名和资产标签问题
        # 使用name作为主机名（可读），host作为UUID标识符，hostid确保asset_tag唯一性
        hostname = zabbix_host.name or f"Host-{zabbix_host.hostid}"
        asset_tag = f"zabbix_{zabbix_host.hostid}_{zabbix_host.host[:8]}"
        
        # 获取操作系统信息，优先使用os_full，再使用os
        operating_system = inventory.get("os_full") or inventory.get("os") or "未知"
        
        return {
            "id": f"zabbix_host_{zabbix_host.hostid}",
            "ci_type_code": ci_type,
            "name": hostname,
            "display_name": hostname,
            "short_description": f"Zabbix监控主机: {hostname}",
            
            # 基本属性 - 修复asset_tag重复问题
            "asset_tag": asset_tag,
            "serial_number": inventory.get("serialno_a"),
            "model": inventory.get("model"),
            "version": inventory.get("software_full"),
            
            # 状态信息
            "lifecycle_state": "使用中",
            "operational_status": operational_status,
            
            # 位置信息
            "location_name": inventory.get("location"),
            
            # 技术信息 - 修复hostname和操作系统
            "ip_addresses": ip_addresses,
            "mac_addresses": mac_addresses,
            "hostname": hostname,  # 使用可读的主机名
            "operating_system": operating_system,  # 改进操作系统信息获取
            "os_version": inventory.get("os_full"),
            
            # 硬件规格
            "hardware_specifications": hardware_specs,
            
            # 监控集成
            "monitoring_enabled": True,
            "zabbix_host_id": zabbix_host.hostid,
            
            # 发现信息
            "discovery_source": "zabbix",
            "last_discovered": datetime.now(),
            "discovery_status": "成功",
            
            # 扩展属性
            "custom_attributes": {
                "zabbix_groups": [g["name"] for g in zabbix_host.groups],
                "item_count": len(zabbix_host.items),
                "trigger_count": len(zabbix_host.triggers),
                "inventory": inventory,
                "zabbix_host_uuid": zabbix_host.host,  # 保存原始UUID
                "interfaces": zabbix_host.interfaces  # 保存接口信息以供调试
            },
            
            "tags": ["zabbix", "monitored"] + [g["name"] for g in zabbix_host.groups]
        }


# 使用示例和测试函数
async def test_zabbix_discovery():
    """测试Zabbix发现功能"""
    from app.settings.config import get_zabbix_config
    
    config = get_zabbix_config()
    connector = ZabbixDiscoveryConnector(config)
    
    # 测试连接
    connection_result = await connector.test_connection()
    print(f"连接测试: {connection_result}")
    
    if connection_result["success"]:
        # 发现主机
        hosts = await connector.discover_hosts()
        print(f"发现 {len(hosts)} 个主机")
        
        # 映射第一个主机为CI
        if hosts:
            first_host = hosts[0]
            ci_data = ZabbixDataMapper.map_host_to_ci(first_host)
            print(f"映射的CI数据: {json.dumps(ci_data, indent=2, ensure_ascii=False, default=str)}")


if __name__ == "__main__":
    asyncio.run(test_zabbix_discovery()) 