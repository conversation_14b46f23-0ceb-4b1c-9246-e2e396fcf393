#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境配置管理器

负责根据环境类型加载相应的配置文件，并提供环境变量的统一管理。
支持开发/测试环境共用一套配置，生产环境使用独立配置。
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional

from loguru import logger


class EnvironmentManager:
    """环境配置管理器"""
    
    # 支持的环境类型
    SUPPORTED_ENVIRONMENTS = {
        'development': 'env.development',
        'testing': 'env.development',  # 测试环境使用开发环境配置
        'production': 'env.production'
    }
    
    def __init__(self):
        self.current_env = self._detect_environment()
        self.config_dir = Path(__file__).parent
        self.env_vars: Dict[str, str] = {}
        
    def _detect_environment(self) -> str:
        """
        检测当前运行环境
        
        优先级：
        1. ENV 环境变量
        2. ENVIRONMENT 环境变量
        3. 默认为 development
        
        Returns:
            str: 环境类型
        """
        env = os.getenv('ENV') or os.getenv('ENVIRONMENT', 'development')
        env = env.lower()
        
        if env not in self.SUPPORTED_ENVIRONMENTS:
            logger.warning(f"不支持的环境类型: {env}，将使用默认的 development 环境")
            env = 'development'
            
        logger.info(f"检测到运行环境: {env}")
        return env
    
    def _load_env_file(self, file_path: Path) -> Dict[str, str]:
        """
        加载环境变量文件
        
        Args:
            file_path: 环境变量文件路径
            
        Returns:
            Dict[str, str]: 环境变量字典
        """
        env_vars = {}
        
        if not file_path.exists():
            logger.error(f"环境配置文件不存在: {file_path}")
            return env_vars
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过空行和注释行
                    if not line or line.startswith('#'):
                        continue
                    
                    # 解析 KEY=VALUE 格式
                    if '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        # 移除值两边的引号
                        if value.startswith('"') and value.endswith('"'):
                            value = value[1:-1]
                        elif value.startswith("'") and value.endswith("'"):
                            value = value[1:-1]
                        
                        # 处理布尔值
                        if value.lower() in ('true', 'false'):
                            value = value.lower()
                        
                        env_vars[key] = value
                        # 设置到环境变量中
                        os.environ[key] = value
                    else:
                        logger.warning(f"环境变量文件 {file_path} 第 {line_num} 行格式错误: {line}")
                        
        except Exception as e:
            logger.error(f"读取环境变量文件 {file_path} 失败: {e}")
            raise
            
        return env_vars
    
    def load_environment(self) -> Dict[str, str]:
        """
        加载当前环境的配置
        
        Returns:
            Dict[str, str]: 环境变量字典
        """
        config_file = self.SUPPORTED_ENVIRONMENTS[self.current_env]
        config_path = self.config_dir / config_file
        
        logger.info(f"加载环境配置文件: {config_path}")
        
        try:
            self.env_vars = self._load_env_file(config_path)
            logger.info(f"成功加载 {len(self.env_vars)} 个环境变量")
            
            # 验证必要的环境变量
            self._validate_environment()
            
            return self.env_vars
            
        except Exception as e:
            logger.error(f"加载环境配置失败: {e}")
            raise
    
    def _validate_environment(self):
        """验证环境配置的有效性"""
        required_vars = [
            'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
            'SECRET_KEY', 'JWT_ALGORITHM'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            error_msg = f"缺少必需的环境变量: {', '.join(missing_vars)}"
            logger.error(error_msg)
            raise EnvironmentError(error_msg)
        
        # 生产环境安全检查
        if self.current_env == 'production':
            self._validate_production_security()
    
    def _validate_production_security(self):
        """验证生产环境的安全配置"""
        secret_key = os.getenv('SECRET_KEY', '')
        debug = os.getenv('DEBUG', 'false').lower()
        
        # 检查是否使用了默认的密钥
        if 'dev_secret_key' in secret_key or 'development' in secret_key:
            logger.error("生产环境不能使用开发环境的密钥")
            raise ValueError("生产环境不能使用开发环境的密钥")
        
        # 检查密钥长度
        if len(secret_key) < 32:
            logger.warning("建议使用长度至少为32字符的 SECRET_KEY")
        
        # 检查调试模式
        if debug == 'true':
            logger.warning("生产环境建议关闭 DEBUG 模式")
            
    def get_env_info(self) -> Dict[str, str]:
        """
        获取当前环境信息摘要
        
        Returns:
            Dict[str, str]: 环境信息字典
        """
        return {
            "current_environment": self.current_env,
            "config_file": self.SUPPORTED_ENVIRONMENTS[self.current_env],
            "db_host": os.getenv("DB_HOST", "未设置"),
            "db_name": os.getenv("DB_NAME", "未设置"),
            "debug_mode": os.getenv("DEBUG", "未设置"),
            "redis_url": self._mask_sensitive_url(os.getenv("REDIS_URL", "未设置")),
            "prometheus_url": os.getenv("PROMETHEUS_URL", "未设置"),
            "zabbix_url": os.getenv("ZABBIX_URL", "未设置"),
        }
    
    def _mask_sensitive_url(self, url: str) -> str:
        """对包含密码的URL进行脱敏处理"""
        if url == "未设置" or not url:
            return url
        
        # 简单的脱敏处理，隐藏密码部分
        if "://" in url and "@" in url:
            parts = url.split("://")
            if len(parts) == 2:
                scheme = parts[0]
                rest = parts[1]
                if "@" in rest:
                    auth_part = rest.split("@")[0]
                    host_part = rest.split("@", 1)[1]
                    # 只显示用户名，隐藏密码
                    if ":" in auth_part:
                        user = auth_part.split(":")[0]
                        return f"{scheme}://{user}:***@{host_part}"
        
        return url
    
    def print_env_info(self):
        """打印环境信息"""
        info = self.get_env_info()
        logger.info("=== 环境配置信息 ===")
        for key, value in info.items():
            logger.info(f"  {key}: {value}")
        logger.info("==================")


# 全局环境管理器实例
env_manager = EnvironmentManager()


def init_environment() -> Dict[str, str]:
    """
    初始化环境配置
    
    Returns:
        Dict[str, str]: 加载的环境变量
    """
    try:
        env_vars = env_manager.load_environment()
        env_manager.print_env_info()
        return env_vars
    except Exception as e:
        logger.error(f"环境初始化失败: {e}")
        sys.exit(1)


def get_current_environment() -> str:
    """获取当前环境类型"""
    return env_manager.current_env


def is_production() -> bool:
    """判断是否为生产环境"""
    return env_manager.current_env == 'production'


def is_development() -> bool:
    """判断是否为开发环境"""
    return env_manager.current_env in ('development', 'testing')


if __name__ == "__main__":
    # 测试环境配置加载
    init_environment() 