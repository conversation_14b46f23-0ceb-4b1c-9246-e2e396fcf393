import pytest
import pytest_asyncio
from httpx import AsyncClient
from app.models.ticket import Ticket, TicketCategory, TicketPriority, TicketStatus, ServiceCatalog
from app.models.admin import User
from .conftest import get_auth_headers, TEST_DATA_PREFIX


class TestTicketsList:
    """测试获取工单列表接口"""
    
    @pytest.mark.asyncio
    async def test_get_tickets_list_success(self, client: AsyncClient, test_ticket: Ticket):
        """测试成功获取工单列表"""
        response = await client.get(
            "/api/v1/tickets/",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "total" in data
        assert "items" in data
        # 由于使用真实数据库，可能有其他数据，所以只检查至少有我们的测试数据
        assert data["total"] >= 1
        assert len(data["items"]) >= 1
        
        # 验证工单数据结构
        ticket_data = data["items"][0]
        assert "id" in ticket_data
        assert "title" in ticket_data
        assert "description" in ticket_data
        assert "category_id" in ticket_data
        assert "priority_id" in ticket_data
        assert "status_id" in ticket_data
    
    @pytest.mark.asyncio
    async def test_get_tickets_list_with_pagination(self, client: AsyncClient, multiple_test_tickets):
        """测试分页参数"""
        response = await client.get(
            "/api/v1/tickets/?skip=0&limit=2",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) <= 2
    
    @pytest.mark.asyncio
    async def test_get_tickets_list_with_search(self, client: AsyncClient, test_ticket: Ticket):
        """测试搜索功能"""
        # 搜索测试工单
        response = await client.get(
            f"/api/v1/tickets/?search={TEST_DATA_PREFIX}测试工单",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 200
        data = response.json()
        if data["total"] > 0:
            # 验证搜索结果包含搜索关键词
            ticket = data["items"][0]
            assert TEST_DATA_PREFIX in ticket["title"] or "测试" in ticket["description"]
    
    @pytest.mark.asyncio
    async def test_get_tickets_list_with_filters(self, client: AsyncClient, test_ticket: Ticket):
        """测试筛选功能"""
        response = await client.get(
            f"/api/v1/tickets/?category_id={test_ticket.category_id}",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 200
        data = response.json()
        if data["total"] > 0:
            # 找到我们的测试工单
            test_tickets = [item for item in data["items"] if item["category_id"] == test_ticket.category_id]
            assert len(test_tickets) >= 1
    
    @pytest.mark.asyncio
    async def test_get_tickets_list_unauthorized(self, client: AsyncClient):
        """测试未授权访问"""
        response = await client.get("/api/v1/tickets/")
        # 307是重定向到登录页面，在真实环境中这是正常的
        assert response.status_code in [307, 401, 403, 422]


class TestCreateTicket:
    """测试创建工单接口"""
    
    @pytest.mark.asyncio
    async def test_create_ticket_success(
        self, 
        client: AsyncClient, 
        test_user: User,
        ticket_category: TicketCategory,
        ticket_priority: TicketPriority,
        ticket_status: TicketStatus,
        service_catalog: ServiceCatalog
    ):
        """测试成功创建工单"""
        ticket_data = {
            "title": f"{TEST_DATA_PREFIX}新建测试工单",
            "description": "这是通过API创建的测试工单",
            "category_id": ticket_category.id,
            "priority_id": ticket_priority.id,
            "service_catalog_id": service_catalog.id,
            "requester_id": test_user.id,
            "status_id": ticket_status.id
        }
        
        response = await client.post(
            "/api/v1/tickets/",
            json=ticket_data,
            headers=get_auth_headers()
        )
        
        if response.status_code == 201:
            data = response.json()
            assert data["title"] == ticket_data["title"]
            assert data["description"] == ticket_data["description"]
            assert data["category_id"] == ticket_data["category_id"]
            assert data["priority_id"] == ticket_data["priority_id"]
            assert "id" in data
            assert "created_at" in data
            
            # 清理创建的工单
            await Ticket.filter(id=data["id"]).delete()
        else:
            # 如果创建失败，检查是否是权限问题
            assert response.status_code in [401, 403, 422]
    
    @pytest.mark.asyncio
    async def test_create_ticket_missing_required_fields(self, client: AsyncClient):
        """测试缺少必填字段"""
        ticket_data = {
            "title": "不完整的工单"
            # 缺少其他必填字段
        }
        
        response = await client.post(
            "/api/v1/tickets/",
            json=ticket_data,
            headers=get_auth_headers()
        )
        
        assert response.status_code == 422


class TestGetTicketDetail:
    """测试获取工单详情接口"""
    
    @pytest.mark.asyncio
    async def test_get_ticket_detail_success(self, client: AsyncClient, test_ticket: Ticket):
        """测试成功获取工单详情"""
        response = await client.get(
            f"/api/v1/tickets/{test_ticket.id}",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_ticket.id
        assert data["title"] == test_ticket.title
        assert data["description"] == test_ticket.description
        assert data["category_id"] == test_ticket.category_id
        assert data["priority_id"] == test_ticket.priority_id
        assert data["status_id"] == test_ticket.status_id
    
    @pytest.mark.asyncio
    async def test_get_ticket_detail_not_found(self, client: AsyncClient):
        """测试获取不存在的工单"""
        # 使用一个极大的ID，应该不存在
        response = await client.get(
            "/api/v1/tickets/999999999",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "不存在" in data["msg"]
    
    @pytest.mark.asyncio
    async def test_get_ticket_detail_invalid_id(self, client: AsyncClient):
        """测试无效的工单ID"""
        response = await client.get(
            "/api/v1/tickets/invalid_id",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 422


class TestUpdateTicket:
    """测试更新工单接口"""
    
    @pytest.mark.asyncio
    async def test_update_ticket_success(self, client: AsyncClient, test_ticket: Ticket):
        """测试成功更新工单"""
        original_title = test_ticket.title
        original_description = test_ticket.description
        
        update_data = {
            "title": f"{TEST_DATA_PREFIX}更新后的工单标题",
            "description": f"{TEST_DATA_PREFIX}更新后的工单描述"
        }
        
        response = await client.put(
            f"/api/v1/tickets/{test_ticket.id}",
            json=update_data,
            headers=get_auth_headers()
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
        assert data["description"] == update_data["description"]
        # 其他字段应该保持不变
        assert data["category_id"] == test_ticket.category_id
        assert data["priority_id"] == test_ticket.priority_id
        
        # 恢复原始数据
        await Ticket.filter(id=test_ticket.id).update(
            title=original_title,
            description=original_description
        )
    
    @pytest.mark.asyncio
    async def test_update_ticket_not_found(self, client: AsyncClient):
        """测试更新不存在的工单"""
        update_data = {
            "title": "更新不存在的工单"
        }
        
        response = await client.put(
            "/api/v1/tickets/999999999",
            json=update_data,
            headers=get_auth_headers()
        )
        
        assert response.status_code == 404


class TestDeleteTicket:
    """测试删除工单接口"""
    
    @pytest.mark.asyncio
    async def test_delete_ticket_success(self, client: AsyncClient, test_user: User, ticket_category: TicketCategory, ticket_priority: TicketPriority, ticket_status: TicketStatus, service_catalog: ServiceCatalog):
        """测试成功删除工单"""
        # 创建一个专门用于删除测试的工单
        ticket = await Ticket.create(
            ticket_no=f"{TEST_DATA_PREFIX}DELETE_TEST",
            title=f"{TEST_DATA_PREFIX}删除测试工单",
            description="用于删除测试的工单",
            category_id=ticket_category.id,
            service_id=service_catalog.id,
            priority_id=ticket_priority.id,
            status_id=ticket_status.id,
            creator_id=test_user.id
        )
        
        response = await client.delete(
            f"/api/v1/tickets/{ticket.id}",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 204
        
        # 验证工单已被删除
        get_response = await client.get(
            f"/api/v1/tickets/{ticket.id}",
            headers=get_auth_headers()
        )
        assert get_response.status_code == 404
    
    @pytest.mark.asyncio
    async def test_delete_ticket_not_found(self, client: AsyncClient):
        """测试删除不存在的工单"""
        response = await client.delete(
            "/api/v1/tickets/999999999",
            headers=get_auth_headers()
        )
        
        assert response.status_code == 404


class TestTicketsIntegration:
    """集成测试"""
    
    @pytest.mark.asyncio
    async def test_ticket_crud_workflow(
        self, 
        client: AsyncClient,
        test_user: User,
        ticket_category: TicketCategory,
        ticket_priority: TicketPriority,
        ticket_status: TicketStatus,
        service_catalog: ServiceCatalog
    ):
        """测试完整的CRUD工作流程"""
        headers = get_auth_headers()
        
        # 1. 创建工单
        create_data = {
            "title": f"{TEST_DATA_PREFIX}集成测试工单",
            "description": "用于集成测试的工单",
            "category_id": ticket_category.id,
            "priority_id": ticket_priority.id,
            "service_catalog_id": service_catalog.id,
            "requester_id": test_user.id,
            "status_id": ticket_status.id
        }
        
        create_response = await client.post(
            "/api/v1/tickets/",
            json=create_data,
            headers=headers
        )
        
        if create_response.status_code != 201:
            # 如果创建失败，跳过后续测试
            pytest.skip(f"无法创建工单进行集成测试: {create_response.status_code}")
        
        created_ticket = create_response.json()
        ticket_id = created_ticket["id"]
        
        try:
            # 2. 获取工单详情
            get_response = await client.get(
                f"/api/v1/tickets/{ticket_id}",
                headers=headers
            )
            assert get_response.status_code == 200
            ticket_detail = get_response.json()
            assert ticket_detail["title"] == create_data["title"]
            
            # 3. 更新工单
            update_data = {
                "title": f"{TEST_DATA_PREFIX}更新后的集成测试工单",
                "description": "更新后的描述"
            }
            
            update_response = await client.put(
                f"/api/v1/tickets/{ticket_id}",
                json=update_data,
                headers=headers
            )
            assert update_response.status_code == 200
            updated_ticket = update_response.json()
            assert updated_ticket["title"] == update_data["title"]
            
            # 4. 验证工单出现在列表中
            list_response = await client.get(
                f"/api/v1/tickets/?search={TEST_DATA_PREFIX}集成测试",
                headers=headers
            )
            assert list_response.status_code == 200
            tickets_list = list_response.json()
            ticket_ids = [ticket["id"] for ticket in tickets_list["items"]]
            assert ticket_id in ticket_ids
            
        finally:
            # 5. 删除工单（确保清理）
            delete_response = await client.delete(
                f"/api/v1/tickets/{ticket_id}",
                headers=headers
            )
            assert delete_response.status_code == 204
            
            # 6. 验证工单已被删除
            final_get_response = await client.get(
                f"/api/v1/tickets/{ticket_id}",
                headers=headers
            )
            assert final_get_response.status_code == 404 