from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

class TicketBase(BaseModel):
    title: str = Field(..., description="工单标题")
    description: str = Field(..., description="工单描述")
    category_id: int = Field(..., description="工单分类ID")
    priority_id: int = Field(..., description="优先级ID")
    service_id: int = Field(..., description="服务目录ID")
    creator_id: int = Field(..., description="创建者ID")
    assignee_id: Optional[int] = Field(None, description="处理人ID")
    status_id: int = Field(..., description="状态ID")

class TicketCreate(TicketBase):
    pass

class TicketUpdate(BaseModel):
    title: Optional[str] = Field(None, description="工单标题")
    description: Optional[str] = Field(None, description="工单描述")
    category_id: Optional[int] = Field(None, description="工单分类ID")
    priority_id: Optional[int] = Field(None, description="优先级ID")
    service_id: Optional[int] = Field(None, description="服务目录ID")
    assignee_id: Optional[int] = Field(None, description="处理人ID")
    status_id: Optional[int] = Field(None, description="状态ID")

class TicketInDB(TicketBase):
    id: int
    ticket_no: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TicketResponse(TicketInDB):
    pass

class TicketListResponse(BaseModel):
    total: int
    items: List[TicketResponse]

    class Config:
        from_attributes = True 