#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接测试发现功能
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))


async def test_discovery_engine():
    """直接测试发现引擎"""
    print("🚀 直接测试发现引擎...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoveryJob, DiscoverySource, ConfigurationItem
        from app.settings.config import settings, get_discovery_config
        from app.discovery.engine import create_discovery_engine
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 查找可用的数据源
        prometheus_source = await DiscoverySource.filter(name="prometheus").first()
        zabbix_source = await DiscoverySource.filter(name="zabbix").first()
        
        if not prometheus_source and not zabbix_source:
            print("❌ 没有找到任何数据源")
            await Tortoise.close_connections()
            return False
        
        # 选择一个数据源进行测试
        source = prometheus_source if prometheus_source else zabbix_source
        
        print(f"📝 使用数据源: {source.name} ({source.source_type})")
        
        # 创建测试任务
        job = await DiscoveryJob.create(
            job_name=f"直接测试任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            description="直接发现引擎测试任务",
            discovery_source=source,
            job_config={"test": True, "direct": True}
        )
        
        print(f"📝 创建测试任务: {job.id} - {job.job_name}")
        
        # 查看任务前配置项数量
        ci_count_before = await ConfigurationItem.all().count()
        print(f"📊 任务执行前配置项数量: {ci_count_before}")
        
        # 创建发现引擎并运行
        print("🔄 创建发现引擎...")
        config = get_discovery_config()
        engine = create_discovery_engine(config)
        
        print("🔄 开始执行发现...")
        results = await engine.run_discovery(
            source_names=[source.name],
            job_id=job.id
        )
        
        # 处理结果
        if results:
            source_result = results.get(source.name)
            if source_result:
                print("✅ 发现任务执行完成:")
                print(f"  状态: {source_result.status}")
                print(f"  总项目: {source_result.total_items}")
                print(f"  成功项目: {source_result.successful_items}")
                print(f"  失败项目: {source_result.failed_items}")
                print(f"  跳过项目: {source_result.skipped_items}")
                print(f"  执行时间: {source_result.execution_time:.2f}秒")
                
                if source_result.errors:
                    print(f"  错误信息: {source_result.errors}")
                
                # 更新任务状态
                job.status = "COMPLETED" if source_result.status.value == "已完成" else "FAILED"
                job.total_items = source_result.total_items
                job.successful_items = source_result.successful_items
                job.failed_items = source_result.failed_items
                job.skipped_items = source_result.skipped_items
                job.execution_duration = int(source_result.execution_time)
                job.completed_at = datetime.now()
                job.progress = 100
                await job.save()
                
                # 查看任务后配置项数量
                ci_count_after = await ConfigurationItem.all().count()
                print(f"📊 任务执行后配置项数量: {ci_count_after}")
                print(f"📈 新增配置项: {ci_count_after - ci_count_before}")
                
                # 显示最新的配置项
                if ci_count_after > ci_count_before:
                    print("\n📋 新增的配置项:")
                    new_items = await ConfigurationItem.all().limit(10).order_by('-created_at')
                    for item in new_items:
                        ip_str = ', '.join(item.ip_addresses) if item.ip_addresses else '无IP'
                        print(f"  - {item.name} ({item.ci_type}) - {ip_str}")
                
                await Tortoise.close_connections()
                return True
            else:
                print("❌ 没有获取到发现结果")
        else:
            print("❌ 发现执行失败")
        
        await Tortoise.close_connections()
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        try:
            await Tortoise.close_connections()
        except:
            pass
        return False


async def test_data_source_health():
    """测试数据源健康状态"""
    print("\n🔍 测试数据源健康状态...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoverySource
        from app.settings.config import settings, get_discovery_config
        from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector
        from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        config = get_discovery_config()
        sources = await DiscoverySource.filter(is_enabled=True).all()
        
        for source in sources:
            try:
                print(f"🔗 测试 {source.name} ({source.source_type})...")
                
                if source.source_type == "PROMETHEUS":
                    connector = PrometheusDiscoveryConnector(config["prometheus"])
                    result = await connector.test_connection()
                elif source.source_type == "ZABBIX":
                    connector = ZabbixDiscoveryConnector(config["zabbix"])
                    result = await connector.test_connection()
                else:
                    continue
                
                if result.get("status") == "connected":
                    print(f"  ✅ 连接成功")
                    source.is_healthy = True
                    source.last_error = None
                else:
                    print(f"  ❌ 连接失败: {result.get('error', '未知错误')}")
                    source.is_healthy = False
                    source.last_error = result.get('error', '未知错误')
                
                source.last_test_at = datetime.now()
                await source.save()
                
            except Exception as e:
                print(f"  ❌ 测试失败: {str(e)}")
                source.is_healthy = False
                source.last_error = str(e)
                source.last_test_at = datetime.now()
                await source.save()
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")
        try:
            await Tortoise.close_connections()
        except:
            pass
        return False


async def show_discovery_status():
    """显示发现系统状态"""
    print("\n📊 发现系统状态总览...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoverySource, DiscoveryJob, ConfigurationItem
        from app.settings.config import settings
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 数据源状态
        sources = await DiscoverySource.all()
        print(f"🔗 数据源: {len(sources)} 个")
        for source in sources:
            status = "健康" if source.is_healthy else "异常"
            enabled = "启用" if source.is_enabled else "禁用"
            print(f"  - {source.name}: {enabled}, {status}")
            if source.last_error:
                print(f"    最后错误: {source.last_error}")
        
        # 任务状态
        total_jobs = await DiscoveryJob.all().count()
        recent_jobs = await DiscoveryJob.all().limit(5).order_by('-created_at')
        print(f"\n📋 任务记录: {total_jobs} 个")
        for job in recent_jobs:
            print(f"  - {job.job_name}: {job.status} ({job.created_at})")
        
        # 配置项状态
        total_cis = await ConfigurationItem.all().count()
        ci_by_type = {}
        all_cis = await ConfigurationItem.all()
        for ci in all_cis:
            ci_type = ci.ci_type
            if ci_type not in ci_by_type:
                ci_by_type[ci_type] = 0
            ci_by_type[ci_type] += 1
        
        print(f"\n📦 配置项: {total_cis} 个")
        for ci_type, count in ci_by_type.items():
            print(f"  - {ci_type}: {count} 个")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ 状态查询失败: {str(e)}")
        try:
            await Tortoise.close_connections()
        except:
            pass
        return False


async def main():
    """主函数"""
    print("🧪 发现引擎直接测试")
    print("=" * 60)
    
    # 显示当前状态
    status_ok = await show_discovery_status()
    
    # 测试数据源健康
    health_ok = await test_data_source_health()
    
    # 执行发现测试
    discovery_ok = await test_discovery_engine()
    
    # 再次显示状态（对比变化）
    if discovery_ok:
        print("\n" + "=" * 60)
        print("📊 测试后状态:")
        await show_discovery_status()
    
    print("\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"  状态查询: {'✅' if status_ok else '❌'}")
    print(f"  健康检查: {'✅' if health_ok else '❌'}")
    print(f"  发现测试: {'✅' if discovery_ok else '❌'}")
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}") 