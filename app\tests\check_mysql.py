import pymysql

# 使用配置文件中的真实数据库配置
conn = pymysql.connect(
    host='************',
    port=3306,
    user='backup_user',
    password='v+SCbs/6LsYNovFngEY=',
    database='fastapi_user',
    charset='utf8mb4'
)

cursor = conn.cursor()

try:
    print("检查菜单配置...")
    # 查询所有菜单
    cursor.execute("SELECT id, name, path, component, menu_type, parent_id FROM menu")
    menus = cursor.fetchall()
    
    print("菜单列表:")
    for menu in menus:
        print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
              f"类型: {menu[4]}, 父级ID: {menu[5]}")
    
    print("\n检查工单相关菜单...")
    cursor.execute("SELECT id, name, path, component, menu_type, parent_id FROM menu WHERE name LIKE %s", ('%工单%',))
    ticket_menus = cursor.fetchall()
    
    print("工单相关菜单:")
    for menu in ticket_menus:
        print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
              f"类型: {menu[4]}, 父级ID: {menu[5]}")
    
    print("\n检查API表结构...")
    # 获取API表的列名
    cursor.execute("SHOW COLUMNS FROM api")
    api_columns = cursor.fetchall()
    print("API表列名:")
    for col in api_columns:
        print(f"列名: {col[0]}, 类型: {col[1]}")
    
    print("\n检查API配置...")
    # 查询所有API
    cursor.execute("SELECT * FROM api LIMIT 5")
    apis = cursor.fetchall()
    
    print("API样例数据:")
    for api in apis:
        print(f"API数据: {api}")
    
    print("\n检查工单相关API...")
    cursor.execute("SELECT * FROM api WHERE path LIKE %s", ('%tickets%',))
    ticket_apis = cursor.fetchall()
    
    print("工单相关API:")
    for api in ticket_apis:
        print(f"API数据: {api}")
    
    # 检查角色和API权限
    print("\n检查角色和API权限关联...")
    cursor.execute("""
    SELECT r.id, r.name, COUNT(ra.api_id) as api_count 
    FROM role r 
    LEFT JOIN role_api ra ON r.id = ra.role_id 
    GROUP BY r.id
    """)
    role_apis = cursor.fetchall()
    
    print("角色和API权限数量:")
    for role in role_apis:
        print(f"角色ID: {role[0]}, 角色名称: {role[1]}, API权限数量: {role[2]}")
    
    # 检查用户角色
    print("\n检查当前用户角色...")
    cursor.execute("""
    SELECT u.id, u.username, r.id, r.name 
    FROM user u 
    JOIN user_role ur ON u.id = ur.user_id 
    JOIN role r ON ur.role_id = r.id
    """)
    user_roles = cursor.fetchall()
    
    print("用户角色:")
    for user in user_roles:
        print(f"用户ID: {user[0]}, 用户名: {user[1]}, 角色ID: {user[2]}, 角色名: {user[3]}")

except Exception as e:
    print(f"发生错误: {e}")

finally:
    cursor.close()
    conn.close()

print("\n检查完成。") 