import os
import sys

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(current_dir)

from tortoise import Tortoise, run_async
from app.settings.config import TORTOISE_ORM
from app.models.admin import Menu
from app.schemas.menus import MenuType

async def update_menus():
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 获取系统管理菜单
    system_menu = await Menu.filter(name="系统管理").first()
    
    if system_menu:
        # 检查数据库总览表菜单是否已存在
        database_menu = await Menu.filter(name="数据库总览表").first()
        
        if not database_menu:
            # 创建新菜单
            await Menu.create(
                menu_type=MenuType.MENU,
                name="数据库总览表",
                path="database",
                order=7,
                parent_id=system_menu.id,
                icon="mdi-new-box",
                is_hidden=False,
                component="/system/database",
                keepalive=False,
            )
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    run_async(update_menus()) 