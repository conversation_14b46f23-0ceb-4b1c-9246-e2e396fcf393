#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

# 直接测试映射函数，不需要数据库连接
from app.discovery.connectors.zabbix_connector import <PERSON>abbixHost, ZabbixDataMapper
from app.discovery.connectors.prometheus_connector import PrometheusTarget, PrometheusDataMapper
from datetime import datetime

def test_zabbix_mapping():
    """测试Zabbix数据映射"""
    print("=== 测试Zabbix数据映射 ===")
    
    # 创建模拟数据
    mock_host = ZabbixHost(
        hostid="12345",
        host="test-server-uuid",
        name="Test Server",
        status=0,
        interfaces=[
            {"ip": "*************", "port": "10050"},
            {"ip": "**********", "port": "10050"}
        ],
        groups=[{"name": "Linux servers"}],
        inventory={
            "os": "Linux",
            "os_full": "Ubuntu 20.04 LTS",
            "hardware_full": "Intel Xeon",
            "memory": "32GB"
        },
        items=[],
        triggers=[]
    )
    
    # 测试映射
    result = ZabbixDataMapper.map_host_to_ci(mock_host)
    
    print(f"Asset Tag: {result.get('asset_tag')}")
    print(f"External ID: {result.get('external_id')}")
    print(f"Hostname: {result.get('hostname')}")
    print(f"IP Addresses: {result.get('ip_addresses')}")
    print(f"Operating System: {result.get('operating_system')}")
    print(f"Data Source: {result.get('data_source')}")
    print()

def test_prometheus_mapping():
    """测试Prometheus数据映射"""
    print("=== 测试Prometheus数据映射 ===")
    
    # 创建模拟数据
    mock_target = PrometheusTarget(
        scrape_pool="node-exporter",
        scrape_url="http://*************:9100/metrics",
        instance="*************:9100",
        job="node-exporter",
        labels={"job": "node-exporter", "instance": "*************:9100"},
        discovered_labels={"__address__": "*************:9100"},
        health="up",
        last_error="",
        last_scrape=datetime.now(),
        scrape_duration=0.05,
        scrape_interval="15s"
    )
    
    # 测试映射
    result = PrometheusDataMapper.map_target_to_ci(mock_target)
    
    print(f"Asset Tag: {result.get('asset_tag')}")
    print(f"External ID: {result.get('external_id')}")
    print(f"Hostname: {result.get('hostname')}")
    print(f"IP Addresses: {result.get('ip_addresses')}")
    print(f"Service Name: {result.get('service_name')}")
    print(f"Data Source: {result.get('data_source')}")
    print()

if __name__ == "__main__":
    print("开始测试数据映射修复...")
    print()
    
    try:
        test_zabbix_mapping()
        test_prometheus_mapping()
        print("✅ 测试完成")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
