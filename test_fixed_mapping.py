#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
sys.path.insert(0, os.path.abspath('.'))

from app.settings.env_manager import init_environment
from app.discovery.connectors.zabbix_connector import <PERSON><PERSON>bixHost, ZabbixDataMapper
from app.discovery.connectors.prometheus_connector import PrometheusTarget, PrometheusDataMapper
from app.discovery.engine import ZabbixProcessor, PrometheusProcessor, CMDBDiscoveryEngine
from datetime import datetime

def test_zabbix_full_pipeline():
    """测试Zabbix完整数据处理管道"""
    print("=== 测试Zabbix完整数据处理管道 ===")
    
    # 1. 创建模拟的Zabbix主机数据
    mock_host = ZabbixHost(
        hostid="12345",
        host="test-server-uuid-123",
        name="Test Server",
        status=0,
        interfaces=[
            {"ip": "*************", "port": "10050", "type": "1"},
            {"ip": "**********", "port": "10050", "type": "1"}
        ],
        groups=[{"name": "Linux servers"}],
        inventory={
            "os": "Linux",
            "os_full": "Ubuntu 20.04.3 LTS",
            "hardware_full": "Intel Xeon E5-2680 v4",
            "memory": "32GB",
            "location": "Data Center A",
            "serialno_a": "SN123456789"
        },
        items=[],
        triggers=[]
    )
    
    # 2. 通过ZabbixDataMapper映射
    mapped_data = ZabbixDataMapper.map_host_to_ci(mock_host)
    print("ZabbixDataMapper映射结果:")
    print(f"  Asset Tag: {mapped_data.get('asset_tag')}")
    print(f"  External ID: {mapped_data.get('external_id')}")
    print(f"  Hostname: {mapped_data.get('hostname')}")
    print(f"  IP Addresses: {mapped_data.get('ip_addresses')}")
    print(f"  Operating System: {mapped_data.get('operating_system')}")
    print(f"  CI Type: {mapped_data.get('ci_type')}")
    print(f"  Data Source: {mapped_data.get('data_source')}")
    
    # 3. 通过ZabbixProcessor进一步处理
    engine = CMDBDiscoveryEngine()
    processor = ZabbixProcessor(engine)
    
    # 标准化数据
    normalized_data = processor.normalize_data(mock_host, "zabbix")
    print("\nZabbixProcessor标准化结果:")
    print(f"  Asset Tag: {normalized_data.get('asset_tag')}")
    print(f"  External ID: {normalized_data.get('external_id')}")
    
    # 映射到CMDB模式
    cmdb_data = processor.map_to_cmdb_schema(normalized_data)
    print("\nCMDB模式映射结果:")
    print(f"  Asset Tag: {cmdb_data.get('asset_tag')}")
    print(f"  External ID: {cmdb_data.get('external_id')}")
    print(f"  Hostname: {cmdb_data.get('hostname')}")
    print(f"  IP Addresses: {cmdb_data.get('ip_addresses')}")
    print(f"  Operating System: {cmdb_data.get('operating_system')}")
    print(f"  Data Source: {cmdb_data.get('data_source')}")
    print()
    
    return cmdb_data

def test_prometheus_full_pipeline():
    """测试Prometheus完整数据处理管道"""
    print("=== 测试Prometheus完整数据处理管道 ===")
    
    # 1. 创建模拟的Prometheus目标数据
    mock_target = PrometheusTarget(
        scrape_pool="node-exporter",
        scrape_url="http://*************:9100/metrics",
        instance="*************:9100",
        job="node-exporter",
        labels={
            "job": "node-exporter",
            "instance": "*************:9100",
            "env": "production"
        },
        discovered_labels={
            "__address__": "*************:9100",
            "__metrics_path__": "/metrics"
        },
        health="up",
        last_error="",
        last_scrape=datetime.now(),
        scrape_duration=0.05,
        scrape_interval="15s"
    )
    
    # 2. 通过PrometheusDataMapper映射
    mapped_data = PrometheusDataMapper.map_target_to_ci(mock_target)
    print("PrometheusDataMapper映射结果:")
    print(f"  Asset Tag: {mapped_data.get('asset_tag')}")
    print(f"  External ID: {mapped_data.get('external_id')}")
    print(f"  Hostname: {mapped_data.get('hostname')}")
    print(f"  IP Addresses: {mapped_data.get('ip_addresses')}")
    print(f"  Service Name: {mapped_data.get('service_name')}")
    print(f"  CI Type: {mapped_data.get('ci_type')}")
    print(f"  Data Source: {mapped_data.get('data_source')}")
    
    # 3. 通过PrometheusProcessor进一步处理
    engine = CMDBDiscoveryEngine()
    processor = PrometheusProcessor(engine)
    
    # 标准化数据
    normalized_data = processor.normalize_data(mock_target, "target")
    print("\nPrometheusProcessor标准化结果:")
    print(f"  Asset Tag: {normalized_data.get('asset_tag')}")
    print(f"  External ID: {normalized_data.get('external_id')}")
    
    # 映射到CMDB模式
    cmdb_data = processor.map_to_cmdb_schema(normalized_data)
    print("\nCMDB模式映射结果:")
    print(f"  Asset Tag: {cmdb_data.get('asset_tag')}")
    print(f"  External ID: {cmdb_data.get('external_id')}")
    print(f"  Hostname: {cmdb_data.get('hostname')}")
    print(f"  IP Addresses: {cmdb_data.get('ip_addresses')}")
    print(f"  Service Name: {cmdb_data.get('service_name')}")
    print(f"  Data Source: {cmdb_data.get('data_source')}")
    print()
    
    return cmdb_data

def validate_mapping_results(zabbix_data, prometheus_data):
    """验证映射结果"""
    print("=== 验证映射结果 ===")
    
    issues = []
    
    # 验证Zabbix数据
    if not zabbix_data.get('external_id'):
        issues.append("Zabbix external_id为空")
    if not zabbix_data.get('ip_addresses'):
        issues.append("Zabbix ip_addresses为空")
    if zabbix_data.get('operating_system') == '未知':
        issues.append("Zabbix operating_system为'未知'")
    if not zabbix_data.get('data_source'):
        issues.append("Zabbix data_source为空")
    
    # 验证Prometheus数据
    if not prometheus_data.get('external_id'):
        issues.append("Prometheus external_id为空")
    if not prometheus_data.get('ip_addresses'):
        issues.append("Prometheus ip_addresses为空")
    if not prometheus_data.get('data_source'):
        issues.append("Prometheus data_source为空")
    
    if issues:
        print(f"❌ 发现问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 所有映射测试通过")
        return True

def main():
    """主测试函数"""
    init_environment()
    
    print("开始测试修复后的数据映射...")
    print()
    
    try:
        # 测试Zabbix完整管道
        zabbix_data = test_zabbix_full_pipeline()
        
        # 测试Prometheus完整管道
        prometheus_data = test_prometheus_full_pipeline()
        
        # 验证结果
        success = validate_mapping_results(zabbix_data, prometheus_data)
        
        if success:
            print("\n🎉 数据映射修复成功！")
            print("主要修复内容:")
            print("1. ✅ 添加了external_id字段映射")
            print("2. ✅ 改进了IP地址提取和验证逻辑")
            print("3. ✅ 修复了操作系统信息提取")
            print("4. ✅ 统一了字段名称（ci_type, data_source等）")
            print("5. ✅ 修复了数据处理管道中的字段映射")
        else:
            print("\n❌ 数据映射仍有问题，需要进一步修复")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
