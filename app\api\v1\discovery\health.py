# -*- coding: utf-8 -*-
"""
CMDB发现健康检查API

提供系统健康状态检查接口
"""

from fastapi import APIRouter
from datetime import datetime, timedelta
import logging

from app.models.cmdb import DiscoverySource, DiscoveryJob, JobStatusEnum
from app.discovery.engine import create_discovery_engine
from app.settings.config import get_discovery_config
from app.core.response import success, fail

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/health", summary="健康检查")
async def health_check():
    """检查CMDB发现系统的健康状态"""
    try:
        # 检查数据源连接
        config = get_discovery_config()
        engine = create_discovery_engine(config)
        connection_results = await engine.test_all_connections()
        
        # 检查数据源状态
        sources = await DiscoverySource.filter(is_enabled=True).all()
        source_status = {}
        
        for source in sources:
            source_status[source.name] = {
                "type": source.source_type,
                "enabled": source.is_enabled,
                "healthy": source.is_healthy,
                "last_test": source.last_test_at.isoformat() if source.last_test_at else None,
                "last_error": source.last_error
            }
        
        # 检查最近的任务
        recent_jobs = await DiscoveryJob.filter(
            created_at__gte=datetime.now() - timedelta(hours=24)
        ).count()
        
        failed_jobs = await DiscoveryJob.filter(
            status=JobStatusEnum.FAILED,
            created_at__gte=datetime.now() - timedelta(hours=24)
        ).count()
        
        return success(data={
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "connections": connection_results,
            "sources": source_status,
            "statistics": {
                "recent_jobs_24h": recent_jobs,
                "failed_jobs_24h": failed_jobs,
                "success_rate": (recent_jobs - failed_jobs) / recent_jobs * 100 if recent_jobs > 0 else 0
            }
        })
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return fail(message=f"健康检查失败: {str(e)}") 