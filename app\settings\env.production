# Vue FastAPI Admin - 生产环境配置
# 此配置用于生产环境（线上环境）
# 注意：生产环境配置需要更强的安全性，请根据实际生产环境修改

# =================
# 环境标识
# =================
ENVIRONMENT=production

# =================
# 数据库配置 - 生产环境 (请根据实际生产环境修改)
# =================
DB_HOST=your_production_db_host
DB_PORT=3306
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password
DB_NAME=fastapi_user_prod
DB_CHARSET=utf8mb4

# =================
# 安全配置 - 生产环境 (请务必修改为强密钥)
# =================
SECRET_KEY=please_change_this_to_a_strong_production_secret_key_32_chars_or_more
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=10080
DEBUG=false

# =================
# 应用配置 - 生产环境
# =================
APP_TITLE=自动化运维系统
PROJECT_NAME=自动化运维系统
APP_DESCRIPTION=Production Environment
CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
CORS_ALLOW_CREDENTIALS=true

# =================
# CMDB发现配置 - 生产环境 (请根据实际环境修改)
# =================

# Prometheus配置
PROMETHEUS_URL=http://your_prometheus_host:9090
PROMETHEUS_TIMEOUT=30

# Zabbix配置
ZABBIX_URL=http://your_zabbix_host/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=your_production_zabbix_user
ZABBIX_PASSWORD=your_production_zabbix_password

# Redis配置 (用于Celery任务队列)
REDIS_URL=redis://your_production_redis_host:6379/0
CELERY_BROKER_URL=redis://your_production_redis_host:6379/0
CELERY_RESULT_BACKEND=redis://your_production_redis_host:6379/1

# =================
# 发现引擎配置 - 生产环境
# =================
DISCOVERY_ENABLED=true
DISCOVERY_MAX_CONCURRENT_JOBS=10
DISCOVERY_JOB_TIMEOUT=3600
DISCOVERY_RETRY_ATTEMPTS=5
DISCOVERY_RETRY_DELAY=120

# =================
# 日志配置 - 生产环境
# =================
DISCOVERY_LOG_LEVEL=WARNING
LOG_FILE_MAX_SIZE=50MB
LOG_BACKUP_COUNT=10

# =================
# 监控和告警配置 - 生产环境
# =================
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_INTERVAL=180
ALERT_ON_FAILURE=true
ALERT_FAILURE_THRESHOLD=2
ALERT_WEBHOOK_URL=https://your_production_alert_webhook_url

# =================
# 同步间隔配置 - 生产环境
# =================
PROMETHEUS_SYNC_INTERVAL=900
ZABBIX_SYNC_INTERVAL=1800 