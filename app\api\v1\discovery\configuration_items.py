# -*- coding: utf-8 -*-
"""
CMDB配置项管理API

提供配置项的查询、管理接口
"""

from fastapi import APIRouter, HTTPException, Query
from typing import Optional
import logging

from app.models.cmdb import ConfigurationItem, CITypeEnum, CIStatusEnum
from app.core.response import success
from app.utils.pagination import paginate

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("", summary="获取配置项列表")
async def get_configuration_items(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    ci_type: Optional[CITypeEnum] = None,
    ci_status: Optional[CIStatusEnum] = None,
    source_id: Optional[int] = None,
    search: Optional[str] = None
):
    """获取配置项列表"""
    try:
        query = ConfigurationItem.all()
        
        # 添加过滤条件
        if ci_type:
            query = query.filter(ci_type=ci_type)
        if ci_status:
            query = query.filter(ci_status=ci_status)
        if source_id:
            query = query.filter(discovery_source_id=source_id)
        if search:
            query = query.filter(
                name__icontains=search
            ) | query.filter(
                hostname__icontains=search
            ) | query.filter(
                asset_tag__icontains=search
            )
        
        # 排序
        query = query.order_by("-last_discovered_at")
        
        # 分页
        result = await paginate(query, page, page_size)
        
        # 序列化数据
        cis_data = []
        for ci in result["items"]:
            ci_data = {
                "id": ci.id,
                "name": ci.name,
                "display_name": ci.display_name,
                "ci_type": ci.ci_type,
                "ci_status": ci.ci_status,
                "asset_tag": ci.asset_tag,
                "hostname": ci.hostname,
                "ip_addresses": ci.ip_addresses,
                "operating_system": ci.operating_system,
                "service_name": ci.service_name,
                "service_version": ci.service_version,
                "environment": ci.environment,
                "location_name": ci.location_name,
                "owner_team": ci.owner_team,
                "last_discovered_at": ci.last_discovered_at.isoformat() if ci.last_discovered_at else None,
                "last_sync_at": ci.last_sync_at.isoformat() if ci.last_sync_at else None,
                "created_at": ci.created_at.isoformat()
            }
            cis_data.append(ci_data)
        
        return success(data={
            "items": cis_data,
            "total": result["total"],
            "page": page,
            "page_size": page_size,
            "total_pages": result["total_pages"]
        })
        
    except Exception as e:
        logger.error(f"获取配置项列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置项列表失败: {str(e)}")


@router.get("/{ci_id}", summary="获取配置项详情")
async def get_configuration_item(ci_id: int):
    """获取配置项详情"""
    try:
        ci = await ConfigurationItem.get_or_none(id=ci_id).prefetch_related(
            "discovery_source", "discovery_job"
        )
        if not ci:
            raise HTTPException(status_code=404, detail="配置项不存在")
        
        return success(data={
            "id": ci.id,
            "name": ci.name,
            "display_name": ci.display_name,
            "description": ci.description,
            "ci_type": ci.ci_type,
            "ci_status": ci.ci_status,
            "asset_tag": ci.asset_tag,
            "hostname": ci.hostname,
            "ip_addresses": ci.ip_addresses,
            "operating_system": ci.operating_system,
            "hardware_specifications": ci.hardware_specifications,
            "service_name": ci.service_name,
            "service_version": ci.service_version,
            "service_port": ci.service_port,
            "environment": ci.environment,
            "location_name": ci.location_name,
            "owner_team": ci.owner_team,
            "tags": ci.tags,
            "custom_attributes": ci.custom_attributes,
            "discovery_info": {
                "source": {
                    "id": ci.discovery_source.id if ci.discovery_source else None,
                    "name": ci.discovery_source.name if ci.discovery_source else None,
                    "type": ci.discovery_source.source_type if ci.discovery_source else None
                },
                "last_job": {
                    "id": ci.discovery_job.id if ci.discovery_job else None,
                    "name": ci.discovery_job.job_name if ci.discovery_job else None
                },
                "data_source": ci.data_source,
                "last_discovered_at": ci.last_discovered_at.isoformat() if ci.last_discovered_at else None,
                "last_sync_at": ci.last_sync_at.isoformat() if ci.last_sync_at else None
            },
            "timestamps": {
                "created_at": ci.created_at.isoformat(),
                "updated_at": ci.updated_at.isoformat()
            }
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置项详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置项详情失败: {str(e)}")


@router.get("/types/summary", summary="获取配置项类型统计")
async def get_ci_types_summary():
    """获取配置项按类型的统计信息"""
    try:
        ci_by_type = {}
        
        for ci_type in CITypeEnum:
            count = await ConfigurationItem.filter(ci_type=ci_type).count()
            ci_by_type[ci_type.value] = count
        
        # 按状态统计
        ci_by_status = {}
        for ci_status in CIStatusEnum:
            count = await ConfigurationItem.filter(ci_status=ci_status).count()
            ci_by_status[ci_status.value] = count
        
        # 按数据源统计
        source_stats = []
        from app.models.cmdb import DiscoverySource
        sources = await DiscoverySource.all()
        
        for source in sources:
            count = await ConfigurationItem.filter(discovery_source=source).count()
            source_stats.append({
                "source_id": source.id,
                "source_name": source.name,
                "source_type": source.source_type,
                "ci_count": count
            })
        
        return success(data={
            "by_type": ci_by_type,
            "by_status": ci_by_status,
            "by_source": source_stats,
            "total": sum(ci_by_type.values())
        })
        
    except Exception as e:
        logger.error(f"获取配置项统计失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置项统计失败: {str(e)}")


@router.get("/search/advanced", summary="高级搜索配置项")
async def advanced_search_configuration_items(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    hostname: Optional[str] = None,
    ip_address: Optional[str] = None,
    operating_system: Optional[str] = None,
    service_name: Optional[str] = None,
    environment: Optional[str] = None,
    owner_team: Optional[str] = None,
    tags: Optional[str] = None
):
    """高级搜索配置项"""
    try:
        query = ConfigurationItem.all()
        
        # 构建复杂查询条件
        if hostname:
            query = query.filter(hostname__icontains=hostname)
        if ip_address:
            query = query.filter(ip_addresses__icontains=ip_address)
        if operating_system:
            query = query.filter(operating_system__icontains=operating_system)
        if service_name:
            query = query.filter(service_name__icontains=service_name)
        if environment:
            query = query.filter(environment__icontains=environment)
        if owner_team:
            query = query.filter(owner_team__icontains=owner_team)
        if tags:
            # 假设tags是JSON字段，可能需要使用特殊的查询方法
            query = query.filter(tags__icontains=tags)
        
        # 排序
        query = query.order_by("-last_discovered_at")
        
        # 分页
        result = await paginate(query, page, page_size)
        
        # 序列化数据
        cis_data = []
        for ci in result["items"]:
            ci_data = {
                "id": ci.id,
                "name": ci.name,
                "display_name": ci.display_name,
                "ci_type": ci.ci_type,
                "ci_status": ci.ci_status,
                "asset_tag": ci.asset_tag,
                "hostname": ci.hostname,
                "ip_addresses": ci.ip_addresses,
                "operating_system": ci.operating_system,
                "service_name": ci.service_name,
                "environment": ci.environment,
                "owner_team": ci.owner_team,
                "tags": ci.tags,
                "last_discovered_at": ci.last_discovered_at.isoformat() if ci.last_discovered_at else None,
                "created_at": ci.created_at.isoformat()
            }
            cis_data.append(ci_data)
        
        return success(data={
            "items": cis_data,
            "total": result["total"],
            "page": page,
            "page_size": page_size,
            "total_pages": result["total_pages"],
            "search_criteria": {
                "hostname": hostname,
                "ip_address": ip_address,
                "operating_system": operating_system,
                "service_name": service_name,
                "environment": environment,
                "owner_team": owner_team,
                "tags": tags
            }
        })
        
    except Exception as e:
        logger.error(f"高级搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"高级搜索失败: {str(e)}")


@router.get("/{ci_id}/history", summary="获取配置项变更历史")
async def get_configuration_item_history(
    ci_id: int,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100)
):
    """获取配置项的变更历史"""
    try:
        # 检查配置项是否存在
        ci = await ConfigurationItem.get_or_none(id=ci_id)
        if not ci:
            raise HTTPException(status_code=404, detail="配置项不存在")
        
        # 获取审计日志
        from app.models.cmdb import DiscoveryAuditLog
        query = DiscoveryAuditLog.filter(ci_id=ci_id).order_by("-created_at")
        
        # 分页
        result = await paginate(query, page, page_size)
        
        # 序列化数据
        history_data = []
        for log in result["items"]:
            log_data = {
                "id": log.id,
                "operation_type": log.operation_type,
                "operation_status": log.operation_status,
                "changes_detected": log.changes_detected,
                "old_data": log.old_data,
                "new_data": log.new_data,
                "error_message": log.error_message,
                "created_at": log.created_at.isoformat()
            }
            history_data.append(log_data)
        
        return success(data={
            "ci_id": ci_id,
            "ci_name": ci.name,
            "history": history_data,
            "total": result["total"],
            "page": page,
            "page_size": page_size,
            "total_pages": result["total_pages"]
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置项历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置项历史失败: {str(e)}") 