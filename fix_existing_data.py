import asyncio
import sys
import json
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from tortoise import Tortoise

async def fix_zabbix_data():
    """修复现有的Zabbix数据"""
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 开始修复Zabbix数据 ===")
    
    # 获取所有Zabbix配置项
    zabbix_items = await ConfigurationItem.filter(data_source='zabbix').all()
    print(f"找到 {len(zabbix_items)} 个Zabbix配置项")
    
    fixed_count = 0
    for item in zabbix_items:
        updated = False
        
        # 修复external_id为空的问题
        if not item.external_id and item.custom_attributes:
            # 从custom_attributes中提取zabbix_host_id
            zabbix_host_id = item.custom_attributes.get('zabbix_host_id')
            if zabbix_host_id:
                item.external_id = str(zabbix_host_id)
                updated = True
                print(f"修复 {item.asset_tag} 的external_id: {item.external_id}")
        
        # 修复操作系统信息
        if not item.operating_system or item.operating_system == '未知':
            if item.custom_attributes and 'inventory' in item.custom_attributes:
                inventory = item.custom_attributes['inventory']
                os_info = (
                    inventory.get('os_full') or 
                    inventory.get('os') or 
                    inventory.get('software') or 
                    inventory.get('software_full')
                )
                if os_info:
                    item.operating_system = os_info
                    updated = True
                    print(f"修复 {item.asset_tag} 的操作系统: {item.operating_system}")
        
        # 修复IP地址问题 - 从interfaces中重新提取
        if item.custom_attributes and 'interfaces' in item.custom_attributes:
            interfaces = item.custom_attributes['interfaces']
            ip_addresses = []
            
            for interface in interfaces:
                ip = interface.get('ip', '').strip()
                if ip and ip != '0.0.0.0' and ip != '127.0.0.1' and ip not in ip_addresses:
                    # 简单的IP验证
                    parts = ip.split('.')
                    if len(parts) == 4 and all(part.isdigit() and 0 <= int(part) <= 255 for part in parts):
                        ip_addresses.append(ip)
            
            if ip_addresses and ip_addresses != item.ip_addresses:
                item.ip_addresses = ip_addresses
                updated = True
                print(f"修复 {item.asset_tag} 的IP地址: {item.ip_addresses}")
        
        # 保存更新
        if updated:
            await item.save()
            fixed_count += 1
    
    print(f"修复完成，共修复 {fixed_count} 个配置项")
    await Tortoise.close_connections()

async def fix_prometheus_data():
    """修复现有的Prometheus数据"""
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 开始修复Prometheus数据 ===")
    
    # 获取所有Prometheus配置项
    prometheus_items = await ConfigurationItem.filter(data_source='prometheus').all()
    print(f"找到 {len(prometheus_items)} 个Prometheus配置项")
    
    fixed_count = 0
    for item in prometheus_items:
        updated = False
        
        # 修复external_id为空的问题
        if not item.external_id and item.custom_attributes:
            # 从custom_attributes中提取prometheus_job和prometheus_instance
            prometheus_job = item.custom_attributes.get('prometheus_job')
            prometheus_instance = item.custom_attributes.get('prometheus_instance')
            if prometheus_job and prometheus_instance:
                item.external_id = f"{prometheus_job}:{prometheus_instance}"
                updated = True
                print(f"修复 {item.asset_tag} 的external_id: {item.external_id}")
            elif prometheus_job:
                item.external_id = prometheus_job
                updated = True
                print(f"修复 {item.asset_tag} 的external_id: {item.external_id}")
        
        # 修复hostname字段
        if not item.hostname and item.custom_attributes:
            prometheus_instance = item.custom_attributes.get('prometheus_instance')
            if prometheus_instance:
                item.hostname = prometheus_instance
                updated = True
                print(f"修复 {item.asset_tag} 的hostname: {item.hostname}")
        
        # 保存更新
        if updated:
            await item.save()
            fixed_count += 1
    
    print(f"修复完成，共修复 {fixed_count} 个配置项")
    await Tortoise.close_connections()

async def check_data_after_fix():
    """检查修复后的数据质量"""
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 修复后数据质量检查 ===")
    
    # 检查Zabbix数据
    total_zabbix = await ConfigurationItem.filter(data_source='zabbix').count()
    zabbix_with_external_id = await ConfigurationItem.filter(
        data_source='zabbix', 
        external_id__not_isnull=True
    ).count()
    zabbix_with_os = await ConfigurationItem.filter(
        data_source='zabbix', 
        operating_system__not_isnull=True
    ).exclude(operating_system='未知').count()
    
    print(f"Zabbix配置项:")
    print(f"  总数: {total_zabbix}")
    print(f"  有external_id: {zabbix_with_external_id} ({zabbix_with_external_id/total_zabbix*100:.1f}%)")
    print(f"  有操作系统信息: {zabbix_with_os} ({zabbix_with_os/total_zabbix*100:.1f}%)")
    
    # 检查Prometheus数据
    total_prometheus = await ConfigurationItem.filter(data_source='prometheus').count()
    prometheus_with_external_id = await ConfigurationItem.filter(
        data_source='prometheus', 
        external_id__not_isnull=True
    ).count()
    
    print(f"\nPrometheus配置项:")
    print(f"  总数: {total_prometheus}")
    print(f"  有external_id: {prometheus_with_external_id} ({prometheus_with_external_id/total_prometheus*100:.1f}%)")
    
    # 显示修复后的样本数据
    print("\n=== 修复后样本数据 ===")
    sample_zabbix = await ConfigurationItem.filter(data_source='zabbix').first()
    if sample_zabbix:
        print(f"Zabbix样本:")
        print(f"  Asset Tag: {sample_zabbix.asset_tag}")
        print(f"  External ID: {sample_zabbix.external_id}")
        print(f"  Hostname: {sample_zabbix.hostname}")
        print(f"  IP Addresses: {sample_zabbix.ip_addresses}")
        print(f"  Operating System: {sample_zabbix.operating_system}")
    
    sample_prometheus = await ConfigurationItem.filter(data_source='prometheus').first()
    if sample_prometheus:
        print(f"\nPrometheus样本:")
        print(f"  Asset Tag: {sample_prometheus.asset_tag}")
        print(f"  External ID: {sample_prometheus.external_id}")
        print(f"  Hostname: {sample_prometheus.hostname}")
        print(f"  IP Addresses: {sample_prometheus.ip_addresses}")
    
    await Tortoise.close_connections()

async def main():
    """主函数"""
    print("开始修复现有数据...")
    
    # 修复Zabbix数据
    await fix_zabbix_data()
    print()
    
    # 修复Prometheus数据
    await fix_prometheus_data()
    print()
    
    # 检查修复结果
    await check_data_after_fix()

if __name__ == "__main__":
    asyncio.run(main())
