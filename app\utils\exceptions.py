"""
CMDB发现模块异常定义

定义发现过程中可能出现的各种异常类型
"""


class DiscoveryError(Exception):
    """CMDB发现基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "DISCOVERY_ERROR"
        self.details = details or {}


class ConnectionError(DiscoveryError):
    """数据源连接异常"""
    
    def __init__(self, message: str, source_type: str = None, url: str = None):
        super().__init__(message, "CONNECTION_ERROR")
        self.source_type = source_type
        self.url = url


class AuthenticationError(DiscoveryError):
    """认证失败异常"""
    
    def __init__(self, message: str, source_type: str = None, username: str = None):
        super().__init__(message, "AUTH_ERROR")
        self.source_type = source_type
        self.username = username


class DataProcessingError(DiscoveryError):
    """数据处理异常"""
    
    def __init__(self, message: str, data_source: str = None, record_id: str = None):
        super().__init__(message, "DATA_PROCESSING_ERROR")
        self.data_source = data_source
        self.record_id = record_id


class SyncError(DiscoveryError):
    """CMDB同步异常"""
    
    def __init__(self, message: str, ci_id: str = None, operation: str = None):
        super().__init__(message, "SYNC_ERROR")
        self.ci_id = ci_id
        self.operation = operation


class ConfigurationError(DiscoveryError):
    """配置错误异常"""
    
    def __init__(self, message: str, config_key: str = None):
        super().__init__(message, "CONFIG_ERROR")
        self.config_key = config_key


class TimeoutError(DiscoveryError):
    """超时异常"""
    
    def __init__(self, message: str, timeout_seconds: int = None, operation: str = None):
        super().__init__(message, "TIMEOUT_ERROR")
        self.timeout_seconds = timeout_seconds
        self.operation = operation


class ValidationError(DiscoveryError):
    """数据验证异常"""
    
    def __init__(self, message: str, field_name: str = None, field_value: str = None):
        super().__init__(message, "VALIDATION_ERROR")
        self.field_name = field_name
        self.field_value = field_value 