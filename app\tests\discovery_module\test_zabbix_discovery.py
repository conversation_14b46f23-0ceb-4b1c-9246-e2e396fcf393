#!/usr/bin/env python3
"""
专门测试Zabbix发现功能
Test Zabbix discovery functionality specifically
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from loguru import logger
from datetime import datetime

from app.settings.env_manager import init_environment
from app.models.cmdb import DiscoverySource, ConfigurationItem, DiscoveryJob
from app.discovery.engine import CMDBDiscoveryEngine

async def test_zabbix_discovery():
    """测试Zabbix发现功能"""
    
    try:
        # 加载环境配置
        logger.info("加载环境配置...")
        init_environment()
        
        # 初始化数据库连接
        logger.info("初始化数据库连接...")
        from app.settings.config import settings
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        logger.info("🧪 专门测试Zabbix发现功能")
        logger.info("=" * 60)
        
        # 检查Zabbix数据源状态
        logger.info("🔍 检查Zabbix数据源...")
        zabbix_source = await DiscoverySource.filter(source_type="ZABBIX").first()
        
        if not zabbix_source:
            logger.error("❌ 未找到Zabbix数据源！")
            return
            
        logger.info(f"✅ 找到Zabbix数据源: {zabbix_source.name}")
        logger.info(f"   状态: {zabbix_source.is_enabled}")
        logger.info(f"   健康状态: {zabbix_source.is_healthy}")
        logger.info(f"   连接配置: {zabbix_source.connection_config}")
        logger.info(f"   发现配置: {zabbix_source.discovery_config}")
        
        # 创建Zabbix发现引擎
        logger.info("🔄 创建Zabbix发现引擎...")
        discovery_engine = CMDBDiscoveryEngine()
        
        # 执行Zabbix发现
        logger.info("🚀 开始执行Zabbix发现...")
        logger.info(f"📝 使用数据源: {zabbix_source.name} ({zabbix_source.source_type})")
        
        # 创建测试任务
        job = await DiscoveryJob.create(
            job_name=f"Zabbix测试任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            discovery_source=zabbix_source,
            status="RUNNING",
            started_at=datetime.now()
        )
        
        logger.info(f"📝 创建测试任务: {job.id} - {job.job_name}")
        
        # 统计执行前的配置项数量
        before_count = await ConfigurationItem.all().count()
        logger.info(f"📊 任务执行前配置项数量: {before_count}")
        
        # 首先注册Zabbix连接器  
        from app.settings.config import get_discovery_config
        config = get_discovery_config()
        
        if "zabbix" in config:
            from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector
            discovery_engine.register_connector("zabbix", ZabbixDiscoveryConnector, config["zabbix"])
            logger.info("✅ 成功注册Zabbix连接器")
        else:
            logger.error("❌ 未找到Zabbix配置")
            return
            
        # 执行发现
        try:
            results = await discovery_engine.run_discovery(
                source_names=["zabbix"],
                job_id=job.id
            )
            
            zabbix_result = results.get("zabbix")
            if zabbix_result:
                logger.info(f"✅ Zabbix发现任务执行完成:")
                logger.info(f"  状态: {zabbix_result.status}")
                logger.info(f"  总项目: {zabbix_result.total_items}")
                logger.info(f"  成功项目: {zabbix_result.successful_items}")
                logger.info(f"  失败项目: {zabbix_result.failed_items}")
                logger.info(f"  跳过项目: {zabbix_result.skipped_items}")
                logger.info(f"  执行时间: {zabbix_result.execution_time:.2f}秒")
                
                # 更新任务状态
                await job.update_from_dict({
                    "status": "COMPLETED" if zabbix_result.status.value == "已完成" else "FAILED",
                    "completed_at": datetime.now(),
                    "total_items": zabbix_result.total_items,
                    "successful_items": zabbix_result.successful_items,
                    "failed_items": zabbix_result.failed_items,
                    "skipped_items": zabbix_result.skipped_items,
                    "execution_duration": int(zabbix_result.execution_time)
                })
                await job.save()
            else:
                logger.error("❌ 未获取到Zabbix发现结果")
            
        except Exception as e:
            logger.error(f"❌ Zabbix发现失败: {str(e)}")
            logger.exception("详细错误信息:")
            
            # 更新任务状态为失败
            await job.update_from_dict({
                "status": "FAILED",
                "completed_at": datetime.now(),
                "error_details": {"error": str(e)}
            })
            await job.save()
            
        # 统计执行后的配置项数量
        after_count = await ConfigurationItem.all().count()
        logger.info(f"📊 任务执行后配置项数量: {after_count}")
        logger.info(f"📈 新增配置项: {after_count - before_count}")
        
        # 显示Zabbix相关的配置项
        if after_count > before_count:
            logger.info("📋 新增的Zabbix配置项:")
            zabbix_cis = await ConfigurationItem.filter(
                data_source="zabbix"
            ).order_by("-created_at").limit(10)
            
            for ci in zabbix_cis:
                ip_list = ", ".join(ci.ip_addresses) if ci.ip_addresses else "无IP"
                logger.info(f"  - {ci.name} ({ci.ci_type}) - {ip_list}")
        
        logger.info("=" * 60)
        logger.info("🎉 Zabbix测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_zabbix_discovery()) 