{"mcpServers": {"filesystem": {"command": "cmd", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem@latest", "C:\\working\\web\\VUE-FASTAPI-ADMIN"], "enabled": true}, "browser-tools": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp"], "enabled": true}, "sequential-thinking": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-sequential-thinking@latest"], "enabled": true}, "playwright": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@playwright/mcp"], "enabled": true}, "context7": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@upstash/context7-mcp@latest"], "enabled": true}, "MySQL": {"command": "C:\\Windows\\System32\\cmd.exe", "args": ["/c", "npx", "-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "************", "MYSQL_PORT": "3306", "MYSQL_USER": "backup_user", "MYSQL_PASS": "v+SCbs/6LsYNovFngEY=", "MYSQL_DB": "fastapi_user", "ALLOW_INSERT_OPERATION": "true", "ALLOW_UPDATE_OPERATION": "true", "ALLOW_DELETE_OPERATION": "false", "DEBUG": "true"}, "enabled": true}}}