import requests
import json

BASE_URL = "http://localhost:9999"  # 调整为您的API服务器地址

def get_token():
    """尝试登录获取token"""
    try:
        # 尝试使用开发模式token
        return "dev"
    except Exception as e:
        print(f"无法获取token: {str(e)}")
        return None

def test_menu_api():
    """测试菜单API"""
    token = get_token()
    if not token:
        print("无法获取token，测试终止")
        return
    
    headers = {"token": token}
    
    # 测试获取用户菜单API
    print("\n测试 /api/v1/base/usermenu API:")
    response = requests.get(f"{BASE_URL}/api/v1/base/usermenu", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        # 只打印工单相关菜单
        ticket_menus = [menu for menu in data.get('data', []) if '工单' in menu.get('name', '')]
        if ticket_menus:
            print("发现工单相关菜单:")
            for menu in ticket_menus:
                print(f"ID: {menu.get('id')}, 名称: {menu.get('name')}, 路径: {menu.get('path')}, "
                      f"组件: {menu.get('component')}, 重定向: {menu.get('redirect', '')}")
                if 'children' in menu and menu['children']:
                    for child in menu['children']:
                        print(f"  子菜单 - ID: {child.get('id')}, 名称: {child.get('name')}, "
                              f"路径: {child.get('path')}, 组件: {child.get('component')}")
        else:
            print("未找到工单相关菜单")
    else:
        print(f"请求失败: {response.text}")

def test_ticket_api():
    """测试工单API"""
    token = get_token()
    if not token:
        print("无法获取token，测试终止")
        return
    
    headers = {"token": token}
    
    # 测试工单列表API
    print("\n测试 /api/v1/tickets/ API:")
    response = requests.get(f"{BASE_URL}/api/v1/tickets/", headers=headers)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"工单总数: {data.get('total', 0)}")
        items = data.get('items', [])
        if items:
            print(f"显示前3条工单数据:")
            for item in items[:3]:
                print(f"ID: {item.get('id')}, 标题: {item.get('title')}, 工单号: {item.get('ticket_no')}")
        else:
            print("暂无工单数据")
    else:
        print(f"请求失败: {response.text}")
    
    # 测试工单基础数据API
    endpoints = [
        "/api/v1/tickets/ticket/categories",
        "/api/v1/tickets/ticket/priorities",
        "/api/v1/tickets/ticket/statuses",
        "/api/v1/tickets/ticket/service-catalogs"
    ]
    
    print("\n测试工单基础数据API:")
    for endpoint in endpoints:
        response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
        print(f"{endpoint} 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            items = data.get('data', [])
            print(f"数据条数: {len(items)}")
            if items:
                print(f"示例数据: {items[0]}")

if __name__ == "__main__":
    print("======== 测试菜单API ========")
    test_menu_api()
    
    print("\n======== 测试工单API ========")
    test_ticket_api()
    
    print("\n测试完成。如果API能正常响应但前端仍无法访问，请检查:")
    print("1. 前端开发者工具中的网络请求，确认请求是否发出")
    print("2. 前端路由配置是否正确")
    print("3. 清除浏览器缓存后重新登录") 