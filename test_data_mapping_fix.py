import asyncio
import sys
import json
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.discovery.connectors.zabbix_connector import <PERSON><PERSON>bixHost, ZabbixDataMapper
from app.discovery.connectors.prometheus_connector import PrometheusTarget, PrometheusDataMapper
from datetime import datetime

def test_zabbix_mapping():
    """测试Zabbix数据映射修复"""
    print("=== 测试Zabbix数据映射修复 ===")
    
    # 创建模拟的Zabbix主机数据
    mock_zabbix_host = ZabbixHost(
        hostid="12345",
        host="test-server-uuid-123",
        name="Test Server",
        status=0,
        interfaces=[
            {"ip": "*************", "port": "10050", "type": "1"},
            {"ip": "**********", "port": "10050", "type": "1"}
        ],
        groups=[
            {"name": "Linux servers"},
            {"name": "Production"}
        ],
        inventory={
            "os": "Linux",
            "os_full": "Ubuntu 20.04.3 LTS",
            "hardware_full": "Intel Xeon E5-2680 v4",
            "memory": "32GB",
            "location": "Data Center A",
            "serialno_a": "SN123456789"
        },
        items=[],
        triggers=[]
    )
    
    # 测试映射
    mapped_data = ZabbixDataMapper.map_host_to_ci(mock_zabbix_host)
    
    print("映射后的数据:")
    print(f"  Asset Tag: {mapped_data.get('asset_tag')}")
    print(f"  External ID: {mapped_data.get('external_id')}")
    print(f"  Hostname: {mapped_data.get('hostname')}")
    print(f"  IP Addresses: {mapped_data.get('ip_addresses')}")
    print(f"  Operating System: {mapped_data.get('operating_system')}")
    print(f"  CI Type: {mapped_data.get('ci_type')}")
    print(f"  Data Source: {mapped_data.get('data_source')}")
    print()
    
    # 验证关键字段
    issues = []
    if not mapped_data.get('external_id'):
        issues.append("external_id为空")
    if not mapped_data.get('ip_addresses'):
        issues.append("ip_addresses为空")
    if mapped_data.get('operating_system') == '未知':
        issues.append("operating_system为'未知'")
    if not mapped_data.get('data_source'):
        issues.append("data_source为空")
    
    if issues:
        print(f"❌ 发现问题: {', '.join(issues)}")
    else:
        print("✅ Zabbix映射测试通过")
    
    return mapped_data

def test_prometheus_mapping():
    """测试Prometheus数据映射修复"""
    print("=== 测试Prometheus数据映射修复 ===")
    
    # 创建模拟的Prometheus目标数据
    mock_prometheus_target = PrometheusTarget(
        scrape_pool="node-exporter",
        scrape_url="http://*************:9100/metrics",
        instance="*************:9100",
        job="node-exporter",
        labels={
            "job": "node-exporter",
            "instance": "*************:9100",
            "env": "production"
        },
        discovered_labels={
            "__address__": "*************:9100",
            "__metrics_path__": "/metrics"
        },
        health="up",
        last_error="",
        last_scrape=datetime.now(),
        scrape_duration=0.05,
        scrape_interval="15s"
    )
    
    # 测试映射
    mapped_data = PrometheusDataMapper.map_target_to_ci(mock_prometheus_target)
    
    print("映射后的数据:")
    print(f"  Asset Tag: {mapped_data.get('asset_tag')}")
    print(f"  External ID: {mapped_data.get('external_id')}")
    print(f"  Hostname: {mapped_data.get('hostname')}")
    print(f"  IP Addresses: {mapped_data.get('ip_addresses')}")
    print(f"  Service Name: {mapped_data.get('service_name')}")
    print(f"  CI Type: {mapped_data.get('ci_type')}")
    print(f"  Data Source: {mapped_data.get('data_source')}")
    print()
    
    # 验证关键字段
    issues = []
    if not mapped_data.get('external_id'):
        issues.append("external_id为空")
    if not mapped_data.get('ip_addresses'):
        issues.append("ip_addresses为空")
    if not mapped_data.get('data_source'):
        issues.append("data_source为空")
    
    if issues:
        print(f"❌ 发现问题: {', '.join(issues)}")
    else:
        print("✅ Prometheus映射测试通过")
    
    return mapped_data

def main():
    """主测试函数"""
    init_environment()
    
    print("开始测试数据映射修复...")
    print()
    
    # 测试Zabbix映射
    zabbix_data = test_zabbix_mapping()
    print()
    
    # 测试Prometheus映射
    prometheus_data = test_prometheus_mapping()
    print()
    
    print("=== 测试总结 ===")
    print("修复的主要问题:")
    print("1. ✅ 添加了external_id字段映射")
    print("2. ✅ 改进了IP地址提取和验证逻辑")
    print("3. ✅ 修复了操作系统信息提取")
    print("4. ✅ 统一了字段名称（ci_type, data_source等）")
    print("5. ✅ 添加了数据验证和错误处理")

if __name__ == "__main__":
    main()
