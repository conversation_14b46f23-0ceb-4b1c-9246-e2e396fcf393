import asyncio
import sys
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from tortoise import Tortoise

async def check_zabbix_data():
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    # 查询最新的10个Zabbix配置项
    items = await ConfigurationItem.filter(data_source='zabbix').order_by('-created_at').limit(10)
    
    print(f'=== 最新10个Zabbix配置项数据质量检查 ===')
    for i, item in enumerate(items, 1):
        print(f'{i}. Asset Tag: {item.asset_tag}')
        print(f'   Hostname: {item.hostname}')
        print(f'   IP: {item.ip_addresses}')
        print(f'   OS: {item.operating_system}')
        print(f'   Source ID: {item.external_id}')
        print()
    
    # 检查IP地址重复问题
    print("=== IP地址重复情况检查 ===")
    items_with_duplicate_ips = await ConfigurationItem.filter(
        data_source='zabbix',
        ip_addresses__in=['["***********", "***********"]', '["***********", "***********"]']
    ).count()
    print(f"包含重复IP地址的配置项数量: {items_with_duplicate_ips}")
    
    # 检查asset_tag唯一性
    print("=== Asset Tag唯一性检查 ===")
    total_zabbix = await ConfigurationItem.filter(data_source='zabbix').count()
    unique_assets = await ConfigurationItem.filter(data_source='zabbix').distinct().values_list('asset_tag', flat=True)
    print(f"Zabbix配置项总数: {total_zabbix}")
    print(f"唯一Asset Tag数量: {len(unique_assets)}")
    if total_zabbix == len(unique_assets):
        print("✅ Asset Tag完全唯一，无重复！")
    else:
        print("❌ Asset Tag存在重复")
    
    # 关闭数据库连接
    await Tortoise.close_connections()

if __name__ == '__main__':
    asyncio.run(check_zabbix_data()) 