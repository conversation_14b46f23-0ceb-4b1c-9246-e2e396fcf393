# -*- coding: utf-8 -*-
"""
CMDB 配置管理数据库模型

包含配置项(CI)、发现任务、数据源、发现规则等核心数据模型
"""

from tortoise.models import Model
from tortoise import fields
from datetime import datetime
from typing import Optional, Dict, Any
import json
from app.models.base import BaseModel, TimestampMixin
from app.models.enums import EnumBase


class CITypeEnum(EnumBase):
    """配置项类型枚举"""
    PHYSICAL_SERVER = "PHYSICAL_SERVER"
    VIRTUAL_SERVER = "VIRTUAL_SERVER"
    CONTAINER_SERVICE = "CONTAINER_SERVICE"
    DATABASE_SERVICE = "DATABASE_SERVICE"
    CACHE_SERVICE = "CACHE_SERVICE"
    APPLICATION_SERVICE = "APPLICATION_SERVICE"
    NETWORK_DEVICE = "NETWORK_DEVICE"
    STORAGE_DEVICE = "STORAGE_DEVICE"


class CIStatusEnum(EnumBase):
    """配置项状态枚举"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    MAINTENANCE = "MAINTENANCE"
    DECOMMISSIONED = "DECOMMISSIONED"


class DiscoverySourceTypeEnum(EnumBase):
    """发现数据源类型枚举"""
    PROMETHEUS = "PROMETHEUS"
    ZABBIX = "ZABBIX"
    KUBERNETES = "KUBERNETES"
    VMWARE = "VMWARE"
    MANUAL = "MANUAL"


class JobStatusEnum(EnumBase):
    """任务状态枚举"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELLED = "CANCELLED"


class ConfigurationItem(BaseModel, TimestampMixin):
    """
    配置项(CI)核心模型
    
    存储所有通过自动发现获取的配置项信息
    """
    
    # 基本信息
    name = fields.CharField(max_length=200, description="配置项名称")
    display_name = fields.CharField(max_length=200, null=True, description="显示名称")
    ci_type = fields.CharEnumField(CITypeEnum, description="配置项类型")
    ci_status = fields.CharEnumField(CIStatusEnum, default=CIStatusEnum.ACTIVE, description="配置项状态")
    
    # 唯一标识
    asset_tag = fields.CharField(max_length=100, unique=True, description="资产标签/唯一标识")
    external_id = fields.CharField(max_length=200, null=True, description="外部系统ID")
    
    # 网络信息
    hostname = fields.CharField(max_length=200, null=True, description="主机名")
    ip_addresses = fields.JSONField(default=list, description="IP地址列表")
    mac_addresses = fields.JSONField(default=list, description="MAC地址列表")
    
    # 系统信息
    operating_system = fields.CharField(max_length=100, null=True, description="操作系统")
    os_version = fields.CharField(max_length=100, null=True, description="操作系统版本")
    architecture = fields.CharField(max_length=50, null=True, description="系统架构")
    
    # 硬件规格
    hardware_specifications = fields.JSONField(default=dict, description="硬件规格")
    cpu_info = fields.JSONField(default=dict, description="CPU信息")
    memory_info = fields.JSONField(default=dict, description="内存信息")
    disk_info = fields.JSONField(default=dict, description="磁盘信息")
    
    # 服务信息
    service_name = fields.CharField(max_length=200, null=True, description="服务名称")
    service_version = fields.CharField(max_length=100, null=True, description="服务版本")
    service_port = fields.IntField(null=True, description="服务端口")
    service_labels = fields.JSONField(default=dict, description="服务标签")
    
    # 位置和环境
    location = fields.CharField(max_length=200, null=True, description="物理位置")
    environment = fields.CharField(max_length=50, null=True, description="环境标识")
    cluster_name = fields.CharField(max_length=200, null=True, description="集群名称")
    namespace = fields.CharField(max_length=200, null=True, description="命名空间")
    
    # 发现相关
    discovery_source = fields.ForeignKeyField("models.DiscoverySource", null=True, description="发现数据源")
    discovery_job = fields.ForeignKeyField("models.DiscoveryJob", null=True, description="发现任务")
    last_discovered_at = fields.DatetimeField(auto_now=True, description="最后发现时间")
    
    # 状态信息
    operational_status = fields.CharField(max_length=50, null=True, description="运行状态")
    health_status = fields.CharField(max_length=50, null=True, description="健康状态")
    monitoring_enabled = fields.BooleanField(default=True, description="是否启用监控")
    
    # 自定义属性
    custom_attributes = fields.JSONField(default=dict, description="自定义属性")
    tags = fields.JSONField(default=list, description="标签列表")
    
    # 维护信息
    owner = fields.CharField(max_length=100, null=True, description="负责人")
    department = fields.CharField(max_length=100, null=True, description="所属部门")
    cost_center = fields.CharField(max_length=100, null=True, description="成本中心")
    
    # 数据验证
    data_source = fields.CharField(max_length=50, description="数据来源")
    data_hash = fields.CharField(max_length=64, null=True, description="数据哈希值")
    last_sync_at = fields.DatetimeField(auto_now=True, description="最后同步时间")
    
    class Meta:
        table = "cmdb_configuration_items"
        indexes = [
            ["asset_tag"],
            ["ci_type"],
            ["hostname"],
            ["discovery_source"],
            ["last_discovered_at"]
        ]

    def __str__(self):
        return f"{self.name} ({self.ci_type})"


class DiscoverySource(BaseModel, TimestampMixin):
    """
    发现数据源配置
    
    存储各种数据源的连接配置信息
    """
    
    name = fields.CharField(max_length=100, unique=True, description="数据源名称")
    display_name = fields.CharField(max_length=200, description="显示名称")
    source_type = fields.CharEnumField(DiscoverySourceTypeEnum, description="数据源类型")
    
    # 连接配置
    connection_config = fields.JSONField(default=dict, description="连接配置")
    
    # 发现配置
    discovery_config = fields.JSONField(default=dict, description="发现配置")
    sync_interval = fields.IntField(default=3600, description="同步间隔(秒)")
    
    # 状态信息
    is_enabled = fields.BooleanField(default=True, description="是否启用")
    is_healthy = fields.BooleanField(default=True, description="是否健康")
    last_test_at = fields.DatetimeField(null=True, description="最后测试时间")
    last_error = fields.TextField(null=True, description="最后错误信息")
    
    # 统计信息
    total_discoveries = fields.IntField(default=0, description="总发现次数")
    successful_discoveries = fields.IntField(default=0, description="成功发现次数")
    last_discovery_at = fields.DatetimeField(null=True, description="最后发现时间")
    
    class Meta:
        table = "cmdb_discovery_sources"

    def __str__(self):
        return f"{self.display_name} ({self.source_type})"


class DiscoveryRule(BaseModel, TimestampMixin):
    """
    发现规则配置
    
    定义自动发现的规则和策略
    """
    
    name = fields.CharField(max_length=100, description="规则名称")
    description = fields.TextField(null=True, description="规则描述")
    
    # 关联数据源
    discovery_source = fields.ForeignKeyField("models.DiscoverySource", description="发现数据源")
    
    # 规则配置
    rule_config = fields.JSONField(default=dict, description="规则配置")
    filter_config = fields.JSONField(default=dict, description="过滤配置")
    mapping_config = fields.JSONField(default=dict, description="映射配置")
    
    # 调度配置
    is_enabled = fields.BooleanField(default=True, description="是否启用")
    schedule_type = fields.CharField(max_length=20, default="interval", description="调度类型")
    schedule_config = fields.JSONField(default=dict, description="调度配置")
    
    # 优先级和策略
    priority = fields.IntField(default=5, description="优先级(1-10)")
    sync_strategy = fields.CharField(max_length=20, default="incremental", description="同步策略")
    conflict_resolution = fields.CharField(max_length=20, default="source_wins", description="冲突解决策略")
    
    # 执行统计
    total_executions = fields.IntField(default=0, description="总执行次数")
    successful_executions = fields.IntField(default=0, description="成功执行次数")
    last_execution_at = fields.DatetimeField(null=True, description="最后执行时间")
    next_execution_at = fields.DatetimeField(null=True, description="下次执行时间")
    
    class Meta:
        table = "cmdb_discovery_rules"

    def __str__(self):
        return f"{self.name} ({self.discovery_source.name})"


class DiscoveryJob(BaseModel, TimestampMixin):
    """
    发现任务执行记录
    
    记录每次发现任务的执行情况
    """
    
    job_name = fields.CharField(max_length=200, description="任务名称")
    description = fields.TextField(null=True, description="任务描述")
    
    # 任务配置
    discovery_rule = fields.ForeignKeyField("models.DiscoveryRule", null=True, description="发现规则")
    discovery_source = fields.ForeignKeyField("models.DiscoverySource", description="发现数据源")
    job_config = fields.JSONField(default=dict, description="任务配置")
    
    # 执行状态
    status = fields.CharEnumField(JobStatusEnum, default=JobStatusEnum.PENDING, description="任务状态")
    progress = fields.IntField(default=0, description="执行进度(0-100)")
    
    # 时间信息
    scheduled_at = fields.DatetimeField(null=True, description="计划执行时间")
    started_at = fields.DatetimeField(null=True, description="开始执行时间")
    completed_at = fields.DatetimeField(null=True, description="完成时间")
    execution_duration = fields.IntField(null=True, description="执行时长(秒)")
    
    # 执行结果
    total_items = fields.IntField(default=0, description="总项目数")
    processed_items = fields.IntField(default=0, description="处理项目数")
    successful_items = fields.IntField(default=0, description="成功项目数")
    failed_items = fields.IntField(default=0, description="失败项目数")
    skipped_items = fields.IntField(default=0, description="跳过项目数")
    
    # 详细结果
    result_summary = fields.JSONField(default=dict, description="结果摘要")
    error_details = fields.JSONField(default=dict, description="错误详情")
    
    # 执行环境
    worker_id = fields.CharField(max_length=100, null=True, description="工作节点ID")
    celery_task_id = fields.CharField(max_length=100, null=True, description="Celery任务ID")
    log_file_path = fields.CharField(max_length=500, null=True, description="日志文件路径")
    
    class Meta:
        table = "cmdb_discovery_jobs"
        indexes = [
            ["status"],
            ["discovery_source"],
            ["started_at"],
            ["celery_task_id"]
        ]

    def __str__(self):
        return f"{self.job_name} ({self.status})"


class CIRelationship(BaseModel, TimestampMixin):
    """
    配置项关系模型
    
    定义配置项之间的依赖和关联关系
    """
    
    source_ci = fields.ForeignKeyField("models.ConfigurationItem", related_name="source_relationships", description="源配置项")
    target_ci = fields.ForeignKeyField("models.ConfigurationItem", related_name="target_relationships", description="目标配置项")
    
    relationship_type = fields.CharField(max_length=50, description="关系类型")
    relationship_direction = fields.CharField(max_length=20, default="bidirectional", description="关系方向")
    
    # 关系属性
    attributes = fields.JSONField(default=dict, description="关系属性")
    weight = fields.FloatField(default=1.0, description="关系权重")
    
    # 发现信息
    discovered_by = fields.CharField(max_length=50, null=True, description="发现方式")
    confidence_score = fields.FloatField(default=1.0, description="置信度分数")
    
    # 状态信息
    is_active = fields.BooleanField(default=True, description="是否活跃")
    last_verified_at = fields.DatetimeField(auto_now=True, description="最后验证时间")
    
    class Meta:
        table = "cmdb_ci_relationships"
        unique_together = [["source_ci", "target_ci", "relationship_type"]]

    def __str__(self):
        return f"{self.source_ci.name} -> {self.target_ci.name} ({self.relationship_type})"


class DiscoveryAuditLog(BaseModel, TimestampMixin):
    """
    发现审计日志
    
    记录所有发现过程中的操作和变更
    """
    
    # 基本信息
    job = fields.ForeignKeyField("models.DiscoveryJob", description="相关任务")
    ci = fields.ForeignKeyField("models.ConfigurationItem", null=True, description="相关配置项")
    
    # 操作信息
    operation_type = fields.CharField(max_length=20, description="操作类型")  # CREATE, UPDATE, DELETE, SKIP
    operation_status = fields.CharField(max_length=20, description="操作状态")  # SUCCESS, FAILED
    
    # 变更详情
    old_data = fields.JSONField(null=True, description="变更前数据")
    new_data = fields.JSONField(null=True, description="变更后数据")
    changes_detected = fields.JSONField(default=dict, description="检测到的变更")
    
    # 错误信息
    error_message = fields.TextField(null=True, description="错误信息")
    error_code = fields.CharField(max_length=50, null=True, description="错误代码")
    
    # 执行上下文
    execution_context = fields.JSONField(default=dict, description="执行上下文")
    processing_time = fields.FloatField(null=True, description="处理时间(秒)")
    
    class Meta:
        table = "cmdb_discovery_audit_logs"
        indexes = [
            ["job"],
            ["operation_type"],
            ["created_at"]
        ]

    def __str__(self):
        return f"{self.operation_type} - {self.ci.name if self.ci else 'N/A'}" 