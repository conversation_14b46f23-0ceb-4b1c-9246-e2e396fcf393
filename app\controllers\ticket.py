from typing import List, Optional, Tuple, Dict, Any
from datetime import datetime, date
from app.models.ticket import Ticket, TicketCategory, TicketPriority, TicketStatus, ServiceCatalog
from app.models.admin import User
from app.schemas.ticket import TicketCreate, TicketUpdate
from app.core.exceptions import NotFoundError
from tortoise.expressions import Q

class TicketController:
    @staticmethod
    def _ticket_to_dict(ticket: Ticket) -> Dict[str, Any]:
        """将Ticket对象转换为字典，确保外键字段是ID值"""
        return {
            "id": ticket.id,
            "ticket_no": ticket.ticket_no,
            "title": ticket.title,
            "description": ticket.description,
            "category_id": ticket.category_id.id if hasattr(ticket.category_id, 'id') else ticket.category_id,
            "service_id": ticket.service_id.id if hasattr(ticket.service_id, 'id') else ticket.service_id,
            "priority_id": ticket.priority_id.id if hasattr(ticket.priority_id, 'id') else ticket.priority_id,
            "status_id": ticket.status_id.id if hasattr(ticket.status_id, 'id') else ticket.status_id,
            "creator_id": ticket.creator_id.id if hasattr(ticket.creator_id, 'id') else ticket.creator_id,
            "assignee_id": ticket.assignee_id.id if hasattr(ticket.assignee_id, 'id') else ticket.assignee_id,
            "created_at": ticket.created_at,
            "updated_at": ticket.updated_at,
        }

    @staticmethod
    async def get_tickets(
        skip: int = 0,
        limit: int = 10,
        category_id: Optional[int] = None,
        priority_id: Optional[int] = None,
        status_id: Optional[int] = None,
        creator_id: Optional[int] = None,
        assignee_id: Optional[int] = None,
        service_id: Optional[int] = None,
        search: Optional[str] = None,
        created_start: Optional[date] = None,
        created_end: Optional[date] = None,
        updated_start: Optional[date] = None,
        updated_end: Optional[date] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取工单列表"""
        query = Ticket.all()
        
        # 构建筛选条件
        if category_id:
            query = query.filter(category_id=category_id)
        if priority_id:
            query = query.filter(priority_id=priority_id)
        if status_id:
            query = query.filter(status_id=status_id)
        if creator_id:
            query = query.filter(creator_id=creator_id)
        if assignee_id:
            query = query.filter(assignee_id=assignee_id)
        if service_id:
            query = query.filter(service_id=service_id)
        if search:
            query = query.filter(
                Q(title__icontains=search) | Q(description__icontains=search)
            )
        
        # 时间范围筛选
        if created_start:
            query = query.filter(created_at__gte=datetime.combine(created_start, datetime.min.time()))
        if created_end:
            query = query.filter(created_at__lte=datetime.combine(created_end, datetime.max.time()))
        if updated_start:
            query = query.filter(updated_at__gte=datetime.combine(updated_start, datetime.min.time()))
        if updated_end:
            query = query.filter(updated_at__lte=datetime.combine(updated_end, datetime.max.time()))

        total = await query.count()
        items = await query.select_related(
            'category_id', 'service_id', 'priority_id', 
            'status_id', 'creator_id', 'assignee_id'
        ).offset(skip).limit(limit).order_by("-created_at")
        
        # 转换为字典列表
        items_dict = [TicketController._ticket_to_dict(ticket) for ticket in items]
        
        return items_dict, total

    @staticmethod
    async def create_ticket(ticket_data: TicketCreate) -> Ticket:
        """创建工单"""
        # 创建工单编号
        import time
        ticket_no = f"TK{int(time.time())}"
        
        # 创建工单
        ticket = Ticket()
        ticket.ticket_no = ticket_no
        ticket.title = ticket_data.title
        ticket.description = ticket_data.description
        ticket.category_id = await TicketCategory.get(id=ticket_data.category_id)
        ticket.service_id = await ServiceCatalog.get(id=ticket_data.service_id)
        ticket.priority_id = await TicketPriority.get(id=ticket_data.priority_id)
        ticket.status_id = await TicketStatus.get(id=ticket_data.status_id)
        ticket.creator_id = await User.get(id=ticket_data.creator_id)
        if ticket_data.assignee_id:
            ticket.assignee_id = await User.get(id=ticket_data.assignee_id)
        
        await ticket.save()
        return ticket

    @staticmethod
    async def get_ticket(ticket_id: int) -> Ticket:
        """获取工单详情"""
        ticket = await Ticket.filter(id=ticket_id).select_related(
            'category_id', 'service_id', 'priority_id',
            'status_id', 'creator_id', 'assignee_id'
        ).first()
        if not ticket:
            raise NotFoundError(f"工单 {ticket_id} 不存在")
        return ticket

    @staticmethod
    async def update_ticket(ticket_id: int, ticket_data: TicketUpdate) -> Ticket:
        """更新工单"""
        ticket = await TicketController.get_ticket(ticket_id)
        update_data = ticket_data.dict(exclude_unset=True)
        
        if update_data:
            await ticket.update_from_dict(update_data).save()
        
        return ticket

    @staticmethod
    async def delete_ticket(ticket_id: int) -> None:
        """删除工单"""
        ticket = await TicketController.get_ticket(ticket_id)
        await ticket.delete() 