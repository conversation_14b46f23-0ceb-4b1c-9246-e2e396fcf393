# -*- coding: utf-8 -*-
"""
Prometheus API连接器

提供与Prometheus监控系统的API集成，用于自动发现服务、获取指标数据和元数据。
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
import aiohttp
import json
from dataclasses import dataclass
from urllib.parse import quote

from app.utils.exceptions import DiscoveryError

logger = logging.getLogger(__name__)


@dataclass
class PrometheusTarget:
    """Prometheus监控目标数据结构"""
    scrape_pool: str
    scrape_url: str
    instance: str
    job: str
    labels: Dict[str, str]
    discovered_labels: Dict[str, str]
    health: str
    last_error: str
    last_scrape: datetime
    scrape_duration: float
    scrape_interval: str


@dataclass
class PrometheusService:
    """从Prometheus发现的服务"""
    service_name: str
    job: str
    instances: List[str]
    labels: Dict[str, str]
    metrics: List[str]
    health_status: str


class PrometheusAPIClient:
    """Prometheus API客户端"""
    
    def __init__(self, url: str, timeout: int = 30):
        self.url = url.rstrip('/')
        self.timeout = timeout
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=self.timeout)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """发送API请求"""
        url = f"{self.url}{endpoint}"
        
        try:
            async with self.session.get(url, params=params) as response:
                if response.status != 200:
                    raise DiscoveryError(f"HTTP错误 {response.status}: {await response.text()}")
                
                result = await response.json()
                
                if result.get("status") != "success":
                    error_msg = result.get("error", "未知错误")
                    raise DiscoveryError(f"Prometheus API错误: {error_msg}")
                
                return result.get("data", {})
                
        except aiohttp.ClientError as e:
            raise DiscoveryError(f"网络错误: {str(e)}")
        except json.JSONDecodeError as e:
            raise DiscoveryError(f"JSON解析错误: {str(e)}")
    
    async def query(self, query: str, time: datetime = None) -> List[Dict]:
        """执行PromQL查询"""
        params = {"query": query}
        if time:
            params["time"] = time.timestamp()
        
        result = await self._make_request("/api/v1/query", params)
        return result.get("result", [])
    
    async def query_range(self, query: str, start: datetime, end: datetime, step: str = "15s") -> Dict:
        """执行范围查询"""
        params = {
            "query": query,
            "start": start.timestamp(),
            "end": end.timestamp(),
            "step": step
        }
        
        return await self._make_request("/api/v1/query_range", params)
    
    async def get_targets(self) -> List[Dict]:
        """获取所有监控目标"""
        result = await self._make_request("/api/v1/targets")
        return result.get("activeTargets", [])
    
    async def get_metadata(self, metric: str = None) -> Dict:
        """获取指标元数据"""
        params = {}
        if metric:
            params["metric"] = metric
        
        return await self._make_request("/api/v1/metadata", params)
    
    async def get_label_names(self) -> List[str]:
        """获取所有标签名称"""
        result = await self._make_request("/api/v1/labels")
        return result
    
    async def get_label_values(self, label: str) -> List[str]:
        """获取指定标签的所有值"""
        result = await self._make_request(f"/api/v1/label/{quote(label)}/values")
        return result
    
    async def get_series(self, match: List[str], start: datetime = None, end: datetime = None) -> List[Dict]:
        """获取时间序列"""
        params = {"match[]": match}
        if start:
            params["start"] = start.timestamp()
        if end:
            params["end"] = end.timestamp()
        
        return await self._make_request("/api/v1/series", params)


class PrometheusDiscoveryConnector:
    """Prometheus发现连接器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.url = config["url"]
        self.timeout = config.get("timeout", 30)
        self.enabled_jobs = config.get("enabled_jobs", [])
        self.excluded_jobs = config.get("excluded_jobs", [])
        self.service_discovery_rules = config.get("service_discovery_rules", {})
    
    async def discover_targets(self) -> List[PrometheusTarget]:
        """发现所有监控目标"""
        logger.info("开始从Prometheus发现监控目标...")
        
        targets = []
        async with PrometheusAPIClient(self.url, self.timeout) as client:
            raw_targets = await client.get_targets()
            
            for target_data in raw_targets:
                try:
                    # 过滤作业
                    job = target_data.get("labels", {}).get("job", "")
                    if self.excluded_jobs and job in self.excluded_jobs:
                        continue
                    if self.enabled_jobs and job not in self.enabled_jobs:
                        continue
                    
                    # 解析最后抓取时间
                    last_scrape_str = target_data.get("lastScrape", "")
                    last_scrape = datetime.fromisoformat(last_scrape_str.replace('Z', '+00:00')) if last_scrape_str else datetime.now()
                    
                    target = PrometheusTarget(
                        scrape_pool=target_data.get("scrapePool", ""),
                        scrape_url=target_data.get("scrapeUrl", ""),
                        instance=target_data.get("labels", {}).get("__address__", ""),
                        job=job,
                        labels=target_data.get("labels", {}),
                        discovered_labels=target_data.get("discoveredLabels", {}),
                        health=target_data.get("health", "unknown"),
                        last_error=target_data.get("lastError", ""),
                        last_scrape=last_scrape,
                        scrape_duration=float(target_data.get("lastScrapeDuration", 0)),
                        scrape_interval=target_data.get("scrapeInterval", "")
                    )
                    
                    targets.append(target)
                    
                except Exception as e:
                    logger.error(f"解析目标数据失败: {str(e)}")
                    continue
        
        logger.info(f"从Prometheus发现了 {len(targets)} 个监控目标")
        return targets
    
    async def discover_services(self) -> List[PrometheusService]:
        """发现服务"""
        logger.info("开始从Prometheus发现服务...")
        
        services = {}
        async with PrometheusAPIClient(self.url, self.timeout) as client:
            # 查询所有up指标来发现服务
            up_metrics = await client.query('up')
            
            for metric in up_metrics:
                labels = metric.get("metric", {})
                job = labels.get("job", "unknown")
                instance = labels.get("instance", "unknown")
                
                # 过滤作业
                if self.excluded_jobs and job in self.excluded_jobs:
                    continue
                if self.enabled_jobs and job not in self.enabled_jobs:
                    continue
                
                # 按job分组服务
                if job not in services:
                    services[job] = {
                        "instances": set(),
                        "labels": {},
                        "metrics": set(),
                        "health_status": "healthy"
                    }
                
                services[job]["instances"].add(instance)
                services[job]["labels"].update(labels)
                
                # 检查健康状态
                if metric.get("value", [None, "0"])[1] == "0":
                    services[job]["health_status"] = "unhealthy"
            
            # 获取每个服务的指标
            for job in services:
                try:
                    # 查询该job的所有指标
                    metrics_query = f'{{job="{job}"}}'
                    series = await client.get_series([metrics_query])
                    
                    for serie in series:
                        metric_name = serie.get("__name__", "")
                        if metric_name:
                            services[job]["metrics"].add(metric_name)
                            
                except Exception as e:
                    logger.error(f"获取服务 {job} 的指标失败: {str(e)}")
        
        # 转换为PrometheusService对象
        service_list = []
        for job, service_data in services.items():
            service = PrometheusService(
                service_name=self._extract_service_name(job, service_data["labels"]),
                job=job,
                instances=list(service_data["instances"]),
                labels=service_data["labels"],
                metrics=list(service_data["metrics"]),
                health_status=service_data["health_status"]
            )
            service_list.append(service)
        
        logger.info(f"从Prometheus发现了 {len(service_list)} 个服务")
        return service_list
    
    def _extract_service_name(self, job: str, labels: Dict[str, str]) -> str:
        """从job和标签中提取服务名称"""
        # 应用服务发现规则
        for rule_name, rule_config in self.service_discovery_rules.items():
            if rule_config.get("job_pattern") and job.startswith(rule_config["job_pattern"]):
                name_template = rule_config.get("name_template", "{job}")
                
                # 准备格式化参数，避免job键冲突
                format_params = labels.copy()
                format_params["job"] = job  # 确保使用传入的job值而不是labels中的
                
                return name_template.format(**format_params)
        
        # 默认使用job名称
        return job
    
    async def get_service_metrics(self, job: str, metrics: List[str] = None) -> Dict[str, Any]:
        """获取服务指标数据"""
        async with PrometheusAPIClient(self.url, self.timeout) as client:
            result = {}
            
            # 默认获取的关键指标
            if not metrics:
                metrics = [
                    "up",
                    "process_cpu_seconds_total",
                    "process_resident_memory_bytes",
                    "http_requests_total",
                    "http_request_duration_seconds"
                ]
            
            for metric in metrics:
                try:
                    query = f'{metric}{{job="{job}"}}'
                    data = await client.query(query)
                    result[metric] = data
                except Exception as e:
                    logger.error(f"获取指标 {metric} 失败: {str(e)}")
                    result[metric] = []
            
            return result
    
    async def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        try:
            async with PrometheusAPIClient(self.url, self.timeout) as client:
                # 执行简单查询测试连接
                result = await client.query("up")
                
                return {
                    "success": True,
                    "message": "连接成功",
                    "metrics_count": len(result),
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            return {
                "success": False,
                "message": f"连接失败: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }


class PrometheusDataMapper:
    """Prometheus数据映射器"""
    
    @staticmethod
    def map_target_to_ci(target: PrometheusTarget) -> Dict[str, Any]:
        """将Prometheus目标映射为CMDB配置项"""

        # 从标签中提取信息
        labels = target.labels or {}
        discovered_labels = target.discovered_labels or {}

        # 确定CI类型
        ci_type = "APPLICATION_SERVICE"
        if "kubernetes" in target.job.lower() or "k8s" in target.job.lower():
            ci_type = "CONTAINER_SERVICE"
        elif "node" in target.job.lower() or "server" in target.job.lower():
            ci_type = "PHYSICAL_SERVER"

        # 提取地址信息，确保IP地址有效
        instance_parts = target.instance.split(":")
        ip_address = instance_parts[0] if instance_parts else ""
        port = instance_parts[1] if len(instance_parts) > 1 else ""

        # 验证IP地址
        ip_addresses = []
        if ip_address and ip_address != "0.0.0.0" and ip_address != "127.0.0.1":
            try:
                import ipaddress
                ipaddress.ip_address(ip_address)
                ip_addresses.append(ip_address)
            except ValueError:
                logger.warning(f"无效的IP地址: {ip_address}")

        # 确定运行状态
        operational_status = "运行中" if target.health == "up" else "故障"
        
        # 生成唯一标识符
        instance_safe = target.instance.replace(':', '_').replace('.', '_')
        asset_tag = f"prometheus_target_{target.job}_{instance_safe}"

        return {
            "id": f"prometheus_target_{target.job}_{instance_safe}",
            "ci_type": ci_type,  # 修正字段名
            "name": f"{target.job}-{target.instance}",
            "display_name": labels.get("instance", target.instance),

            # 基本属性
            "asset_tag": asset_tag,
            "external_id": f"{target.job}:{target.instance}",  # 添加external_id字段

            # 状态信息
            "operational_status": operational_status,

            # 技术信息
            "ip_addresses": ip_addresses,
            "service_port": instance_parts[1] if len(instance_parts) > 1 else None,
            "service_name": target.job,
            "hostname": labels.get("instance", target.instance),

            # 业务信息
            "environment": labels.get("env", "生产"),

            # 监控集成
            "monitoring_enabled": True,

            # 发现信息
            "data_source": "prometheus",  # 修正字段名
            "last_discovered_at": datetime.now(),

            # 扩展属性
            "custom_attributes": {
                "scrape_pool": target.scrape_pool,
                "scrape_url": target.scrape_url,
                "scrape_interval": target.scrape_interval,
                "scrape_duration": target.scrape_duration,
                "last_error": target.last_error,
                "prometheus_labels": labels,
                "discovered_labels": discovered_labels,
                "prometheus_job": target.job,
                "prometheus_instance": target.instance,
                "version": labels.get("version", "")
            },

            "tags": ["prometheus", "monitored", target.job] +
                   [f"{k}:{v}" for k, v in labels.items() if k not in ["job", "instance"]]
        }
    
    @staticmethod
    def map_service_to_ci(service: PrometheusService) -> Dict[str, Any]:
        """将Prometheus服务映射为CMDB配置项"""
        
        # 确定运行状态
        operational_status = "运行中" if service.health_status == "healthy" else "故障"
        
        # 提取地址信息
        ip_addresses = []
        for instance in service.instances:
            ip = instance.split(":")[0]
            if ip and ip not in ip_addresses:
                ip_addresses.append(ip)
        
        return {
            "id": f"prometheus_service_{service.job}",
            "ci_type": "APPLICATION_SERVICE",  # 修正字段名
            "name": service.service_name,
            "display_name": service.service_name,

            # 基本属性
            "asset_tag": f"service_{service.job}_{hash(str(service.instances)) % 1000}",
            "external_id": service.job,  # 添加external_id字段

            # 状态信息
            "operational_status": operational_status,

            # 技术信息
            "ip_addresses": ip_addresses,
            "service_name": service.service_name,
            "hostname": service.service_name,

            # 业务信息
            "environment": service.labels.get("env", "生产"),

            # 监控集成
            "monitoring_enabled": True,

            # 发现信息
            "data_source": "prometheus",  # 修正字段名
            "last_discovered_at": datetime.now(),

            # 扩展属性
            "custom_attributes": {
                "instances": service.instances,
                "instance_count": len(service.instances),
                "metrics": service.metrics,
                "metric_count": len(service.metrics),
                "health_status": service.health_status,
                "prometheus_labels": service.labels,
                "prometheus_job": service.job,
                "version": service.labels.get("version", "")
            },

            "tags": ["prometheus", "service", service.job] +
                   [f"{k}:{v}" for k, v in service.labels.items() if k not in ["job", "instance"]]
        }


# 使用示例和测试函数
async def test_prometheus_discovery():
    """测试Prometheus发现功能"""
    from app.settings.config import get_prometheus_config
    
    config = get_prometheus_config()
    connector = PrometheusDiscoveryConnector(config)
    
    # 测试连接
    connection_result = await connector.test_connection()
    print(f"连接测试: {connection_result}")
    
    if connection_result["success"]:
        # 发现目标
        targets = await connector.discover_targets()
        print(f"发现 {len(targets)} 个监控目标")
        
        # 发现服务
        services = await connector.discover_services()
        print(f"发现 {len(services)} 个服务")
        
        # 映射第一个目标为CI
        if targets:
            first_target = targets[0]
            ci_data = PrometheusDataMapper.map_target_to_ci(first_target)
            print(f"目标映射的CI数据: {json.dumps(ci_data, indent=2, ensure_ascii=False, default=str)}")
        
        # 映射第一个服务为CI
        if services:
            first_service = services[0]
            service_ci_data = PrometheusDataMapper.map_service_to_ci(first_service)
            print(f"服务映射的CI数据: {json.dumps(service_ci_data, indent=2, ensure_ascii=False, default=str)}")


if __name__ == "__main__":
    asyncio.run(test_prometheus_discovery()) 