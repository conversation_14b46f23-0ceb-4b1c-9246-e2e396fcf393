# 数据库迁移指南

## 概述

本项目使用通用数据库迁移工具 `app/scripts/aerich_migrate.py` 来管理所有数据库表的创建和迁移。这个工具会自动发现新的模型并创建相应的数据表，无需为每个新模型编写单独的脚本。

## 通用迁移工具功能

### 核心特性

- ✨ **自动模型发现**: 自动扫描 `app/models/` 目录中的所有 Tortoise 模型
- 🔍 **智能状态检查**: 检查数据库连接和表状态
- 🤖 **智能迁移策略**: 优先使用 Aerich，失败时自动切换到直接表生成
- 📋 **详细日志记录**: 提供详细的操作日志和状态反馈
- 🧪 **模型测试**: 自动测试所有模型的基本操作

### 可用命令

```bash
# 检查数据库和迁移状态
python app/scripts/aerich_migrate.py status

# 自动迁移（推荐使用）
python app/scripts/aerich_migrate.py auto

# 初始化 Aerich 迁移系统
python app/scripts/aerich_migrate.py init

# 创建新的迁移文件
python app/scripts/aerich_migrate.py migrate [迁移名称]

# 应用所有待执行的迁移
python app/scripts/aerich_migrate.py upgrade

# 回滚迁移
python app/scripts/aerich_migrate.py downgrade [目标版本]

# 查看迁移历史记录
python app/scripts/aerich_migrate.py history

# 直接生成表结构（绕过 Aerich）
python app/scripts/aerich_migrate.py generate

# 测试所有模型的基本操作
python app/scripts/aerich_migrate.py test

# 查看帮助信息
python app/scripts/aerich_migrate.py help
```

## 添加新模型的流程

当您需要添加新的数据模型时，只需要：

1. **创建模型文件**: 在 `app/models/` 目录下创建新的模型文件
2. **定义模型**: 使用 Tortoise ORM 定义您的模型类
3. **运行迁移**: 执行 `python app/scripts/aerich_migrate.py auto`

### 示例

假设您要添加一个新的 `Product` 模型：

1. 创建 `app/models/product.py`:
```python
from tortoise.models import Model
from tortoise import fields
from app.models.base import BaseModel, TimestampMixin

class Product(BaseModel, TimestampMixin):
    """产品模型"""
    
    name = fields.CharField(max_length=100, description="产品名称")
    description = fields.TextField(description="产品描述")
    price = fields.DecimalField(max_digits=10, decimal_places=2, description="价格")
    is_active = fields.BooleanField(default=True, description="是否启用")
    
    class Meta:
        table = "product"
        table_description = "产品信息表"
```

2. 运行自动迁移:
```bash
python app/scripts/aerich_migrate.py auto
```

工具会自动：
- 发现新的 `product` 模型
- 尝试使用 Aerich 创建迁移
- 如果失败，自动使用直接方式创建表
- 测试新模型的基本操作
- 显示操作结果

## 脚本目录结构

清理后的 `app/scripts/` 目录结构：

```
app/scripts/
├── aerich_migrate.py              # 🔧 通用数据库迁移工具（核心）
├── init_cmdb_discovery.py         # 🚀 CMDB发现系统初始化
├── setup_env.py                   # 🔧 环境配置工具
├── init_ticket_data.py            # 📊 工单基础数据初始化
├── init_discovery.py              # 📋 发现系统初始化
├── test_prometheus_discovery.py   # 🧪 Prometheus发现测试
├── test_discovery_config.py       # 🧪 发现配置测试
├── fix_ticket_menu.py             # 🔧 工单菜单修复
├── add_ticket_menu.py             # ➕ 添加工单菜单
├── check_services.py              # 🔍 服务检查
├── check_ticket_data.py           # 🔍 工单数据检查
└── update_menu.py                 # 🔄 菜单更新
```

### 已删除的脚本

以下单独的表创建和初始化脚本已被删除，功能已集成到通用工具中：

- ❌ `init_cmdb_tables.py` - CMDB表创建（已集成到 `aerich_migrate.py`）
- ❌ `init_db.py` - 基础数据库初始化（已集成到 `aerich_migrate.py`）
- ❌ `init_seed_data.py` - 种子数据初始化（功能分散到具体数据初始化脚本）
- ❌ `test_db_tables.py` - 数据库表测试（已集成到 `aerich_migrate.py`）
- ❌ `create_cmdb_tables.py` - CMDB表创建（已集成到 `aerich_migrate.py`）

## 最佳实践

### 1. 使用自动模式
对于大多数情况，推荐使用自动模式：
```bash
python app/scripts/aerich_migrate.py auto
```

### 2. 检查状态
在进行迁移前，可以先检查状态：
```bash
python app/scripts/aerich_migrate.py status
```

### 3. 测试模型
迁移完成后，可以测试所有模型：
```bash
python app/scripts/aerich_migrate.py test
```

### 4. 模型命名规范
- 使用 PascalCase 命名模型类
- 使用 snake_case 命名表名
- 继承适当的基类（如 `BaseModel`, `TimestampMixin`）

### 5. 字段定义规范
- 为每个字段添加 `description` 参数
- 合理设置字段约束（如 `max_length`, `null`, `default`）
- 使用适当的字段类型

## 故障排除

### 常见问题

1. **Aerich 迁移失败**
   - 工具会自动切换到直接表生成模式
   - 检查模型定义是否正确
   - 确认数据库连接配置

2. **模型导入错误**
   - 检查模型类是否正确继承 `Model`
   - 确认模型文件中没有语法错误
   - 检查导入路径是否正确

3. **表已存在错误**
   - 使用 `safe=True` 参数避免重复创建
   - 检查现有表结构是否与模型匹配

### 日志分析

工具提供详细的日志输出，包括：
- 🔍 模型发现过程
- 📊 数据库状态检查
- ⚙️ 表创建过程
- 🧪 模型测试结果
- ✅ 操作成功/失败状态

通过这些日志可以快速定位和解决问题。

## 工具使用示例

### 常用命令组合

```bash
# 完整的新项目初始化流程
python app/scripts/aerich_migrate.py status  # 检查当前状态
python app/scripts/aerich_migrate.py auto    # 自动处理所有迁移
python app/scripts/aerich_migrate.py test    # 测试所有模型

# 添加新模型后的流程
# 1. 创建新的模型文件
# 2. 运行自动迁移
python app/scripts/aerich_migrate.py auto

# 手动迁移流程（当自动模式有问题时）
python app/scripts/aerich_migrate.py migrate "添加新模型"
python app/scripts/aerich_migrate.py upgrade
python app/scripts/aerich_migrate.py test

# 直接表生成（绕过 Aerich）
python app/scripts/aerich_migrate.py generate
```

### 输出示例

```bash
$ python app/scripts/aerich_migrate.py status

🔍 检查数据库状态...
🔍 正在发现模型模块...
  ✅ 发现模型模块: app.models.admin
  ✅ 发现模型模块: app.models.base
  ✅ 发现模型模块: app.models.cmdb
  📋 枚举模块: app.models.enums (包含枚举定义)
  ✅ 发现模型模块: app.models.ticket
📋 共发现 4 个应用模型模块
  ✅ 数据库连接正常
  📊 数据库中共有 32 个表
  ✅ Aerich迁移表存在
```

## 技术细节

### 自动模型发现机制

工具会自动扫描 `app/models/` 目录：
1. 发现所有 `.py` 文件（除了 `__` 开头的文件）
2. 尝试导入每个模块
3. 检查模块中是否包含 Tortoise 模型类
4. 区分模型模块和枚举模块
5. 自动配置到 Tortoise ORM 配置中

### 智能迁移策略

1. **优先使用 Aerich**: 标准的迁移工具，支持版本控制
2. **备用直接生成**: 当 Aerich 失败时，使用 `generate_schemas` 直接创建表
3. **自动测试**: 迁移完成后自动验证模型功能

### 配置自动化

- 自动从 `app.settings.config` 读取数据库配置
- 如果配置不可用，使用预设的默认配置
- 动态设置模型模块列表

## 总结

通过使用通用数据库迁移工具，您可以：
- 🚀 快速添加新模型而无需编写额外脚本
- 🔧 统一管理所有数据库迁移操作
- 📋 获得详细的操作日志和状态反馈
- 🛠️ 享受自动化的表创建和测试流程

这大大简化了数据库管理工作，提高了开发效率。无论是初始化新项目还是添加新功能，都只需要运行 `python app/scripts/aerich_migrate.py auto` 即可完成所有必要的数据库操作。 