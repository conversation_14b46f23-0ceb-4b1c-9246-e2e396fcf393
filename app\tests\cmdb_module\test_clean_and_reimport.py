import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.models.cmdb import ConfigurationItem, DiscoverySource
from app.discovery.engine import create_discovery_engine
from app.core.database import init_database
from tortoise import Tortoise

async def clean_and_reimport():
    """清理并重新导入CMDB数据"""
    
    # 初始化数据库连接
    await init_database()
    
    print("=== 清理现有数据 ===")
    
    # 删除所有从Zabbix发现的配置项
    zabbix_items = await ConfigurationItem.filter(data_source="zabbix")
    zabbix_count = len(zabbix_items)
    if zabbix_count > 0:
        await ConfigurationItem.filter(data_source="zabbix").delete()
        print(f"已删除 {zabbix_count} 个Zabbix配置项")
    
    # 删除所有从Prometheus发现的配置项  
    prometheus_items = await ConfigurationItem.filter(data_source="prometheus")
    prometheus_count = len(prometheus_items)
    if prometheus_count > 0:
        await ConfigurationItem.filter(data_source="prometheus").delete()
        print(f"已删除 {prometheus_count} 个Prometheus配置项")
    
    # 显示剩余数据
    remaining_count = await ConfigurationItem.all().count()
    print(f"数据库中剩余配置项: {remaining_count} 个")
    
    print("\n=== 重新导入数据 ===")
    
    # 创建发现引擎并运行发现
    discovery_engine = create_discovery_engine()
    results = await discovery_engine.run_discovery()
    
    print("\n=== 导入结果 ===")
    for source_name, result in results.items():
        print(f"{source_name}:")
        print(f"  状态: {result.status.value}")
        print(f"  总项目: {result.total_items}")
        print(f"  成功: {result.successful_items}")
        print(f"  失败: {result.failed_items}")
        print(f"  执行时间: {result.execution_time:.2f}秒")
        if result.errors:
            print(f"  错误: {result.errors[:3]}")  # 只显示前3个错误
    
    # 检查最终结果
    print("\n=== 最终统计 ===")
    total_items = await ConfigurationItem.all().count()
    zabbix_items = await ConfigurationItem.filter(data_source="zabbix").count()
    prometheus_items = await ConfigurationItem.filter(data_source="prometheus").count()
    other_items = total_items - zabbix_items - prometheus_items
    
    print(f"总配置项: {total_items}")
    print(f"  Zabbix: {zabbix_items}")
    print(f"  Prometheus: {prometheus_items}")
    print(f"  其他: {other_items}")
    
    # 检查IP地址分布
    print("\n=== IP地址分布检查 ===")
    from collections import Counter
    
    all_items = await ConfigurationItem.all()
    ip_counter = Counter()
    
    for item in all_items:
        if item.ip_addresses:
            for ip in item.ip_addresses:
                ip_counter[ip] += 1
    
    print("IP地址使用统计 (显示使用次数>1的IP):")
    for ip, count in ip_counter.most_common(10):
        if count > 1:
            print(f"  {ip}: {count} 次")
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(clean_and_reimport()) 