#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CMDB发现系统初始化脚本

初始化CMDB发现功能，包括：
1. 创建数据库表结构
2. 初始化默认数据源配置
3. 创建初始发现规则
4. 测试连接和执行初始发现

使用方法:
    python app/scripts/init_cmdb_discovery.py
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from app.settings.config import settings, get_discovery_config
from app.models.cmdb import (
    DiscoverySource, DiscoveryRule, DiscoveryJob,
    DiscoverySourceTypeEnum, JobStatusEnum
)
from app.discovery.engine import create_discovery_engine
from app.discovery.tasks import run_discovery_job

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def init_database():
    """初始化数据库连接和表结构"""
    print("🔗 正在初始化数据库连接...")
    
    try:
        # 初始化Tortoise ORM
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 生成数据库表结构
        await Tortoise.generate_schemas(safe=True)
        
        print("✅ 数据库初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False


async def create_default_data_sources():
    """创建默认数据源配置"""
    print("\n📊 正在创建默认数据源...")
    
    config = get_discovery_config()
    created_sources = []
    
    try:
        # 1. Prometheus数据源
        if "prometheus" in config:
            prometheus_config = config["prometheus"]
            
            prometheus_source = await DiscoverySource.get_or_none(name="prometheus")
            if not prometheus_source:
                prometheus_source = await DiscoverySource.create(
                    name="prometheus",
                    display_name="Prometheus监控系统",
                    source_type="PROMETHEUS",
                    connection_config={
                        "url": prometheus_config["url"],
                        "timeout": prometheus_config["timeout"]
                    },
                    discovery_config={
                        "enabled_jobs": prometheus_config.get("enabled_jobs", []),
                        "excluded_jobs": prometheus_config.get("excluded_jobs", []),
                        "service_discovery_rules": prometheus_config.get("service_discovery_rules", {})
                    },
                    sync_interval=prometheus_config.get("sync_interval", 1800),
                    is_enabled=True
                )
                created_sources.append(prometheus_source)
                print(f"  ✅ 创建Prometheus数据源: {prometheus_source.display_name}")
            else:
                print(f"  ℹ️  Prometheus数据源已存在: {prometheus_source.display_name}")
        
        # 2. Zabbix数据源
        if "zabbix" in config:
            zabbix_config = config["zabbix"]
            
            zabbix_source = await DiscoverySource.get_or_none(name="zabbix")
            if not zabbix_source:
                zabbix_source = await DiscoverySource.create(
                    name="zabbix",
                    display_name="Zabbix监控系统",
                    source_type="ZABBIX",
                    connection_config={
                        "url": zabbix_config["url"],
                        "username": zabbix_config["username"],
                        "password": zabbix_config["password"]
                    },
                    discovery_config={
                        "enabled_groups": zabbix_config.get("enabled_groups", []),
                        "excluded_hosts": zabbix_config.get("excluded_hosts", [])
                    },
                    sync_interval=zabbix_config.get("sync_interval", 3600),
                    is_enabled=True
                )
                created_sources.append(zabbix_source)
                print(f"  ✅ 创建Zabbix数据源: {zabbix_source.display_name}")
            else:
                print(f"  ℹ️  Zabbix数据源已存在: {zabbix_source.display_name}")
        
        print(f"\n📊 数据源创建完成，共创建 {len(created_sources)} 个新数据源")
        return created_sources
        
    except Exception as e:
        print(f"❌ 创建数据源失败: {str(e)}")
        return []


async def create_default_discovery_rules():
    """创建默认发现规则"""
    print("\n📋 正在创建默认发现规则...")
    
    try:
        created_rules = []
        
        # 获取数据源
        prometheus_source = await DiscoverySource.get_or_none(name="prometheus")
        zabbix_source = await DiscoverySource.get_or_none(name="zabbix")
        
        # 1. Prometheus发现规则
        if prometheus_source:
            prometheus_rule = await DiscoveryRule.get_or_none(
                name="prometheus_targets_discovery",
                discovery_source=prometheus_source
            )
            
            if not prometheus_rule:
                prometheus_rule = await DiscoveryRule.create(
                    name="prometheus_targets_discovery",
                    description="Prometheus监控目标自动发现",
                    discovery_source=prometheus_source,
                    rule_config={
                        "discovery_type": "targets",
                        "include_services": True,
                        "ci_type_mapping": {
                            "node-exporter": "PHYSICAL_SERVER",
                            "mysql-exporter": "DATABASE_SERVICE",
                            "redis-exporter": "CACHE_SERVICE",
                            "blackbox": "APPLICATION_SERVICE"
                        }
                    },
                    filter_config={
                        "exclude_jobs": ["prometheus"],
                        "health_status": ["up"]
                    },
                    mapping_config={
                        "hostname_field": "instance",
                        "name_template": "{job}-{instance}",
                        "tags_from_labels": True
                    },
                    schedule_type="interval",
                    schedule_config={
                        "interval_minutes": 30
                    },
                    priority=7,
                    sync_strategy="incremental",
                    conflict_resolution="source_wins",
                    is_enabled=True
                )
                created_rules.append(prometheus_rule)
                print(f"  ✅ 创建Prometheus发现规则: {prometheus_rule.name}")
        
        # 2. Zabbix发现规则
        if zabbix_source:
            zabbix_rule = await DiscoveryRule.get_or_none(
                name="zabbix_hosts_discovery",
                discovery_source=zabbix_source
            )
            
            if not zabbix_rule:
                zabbix_rule = await DiscoveryRule.create(
                    name="zabbix_hosts_discovery",
                    description="Zabbix主机自动发现",
                    discovery_source=zabbix_source,
                    rule_config={
                        "discovery_type": "hosts",
                        "include_items": True,
                        "include_triggers": True,
                        "ci_type_mapping": {
                            "Linux servers": "PHYSICAL_SERVER",
                            "Windows servers": "PHYSICAL_SERVER",
                            "Virtual machines": "VIRTUAL_SERVER",
                            "Network equipment": "NETWORK_DEVICE"
                        }
                    },
                    filter_config={
                        "enabled_groups": ["Linux servers", "Windows servers", "Network equipment"],
                        "exclude_hosts": ["test-host", "demo-host"],
                        "status": [0]  # 启用的主机
                    },
                    mapping_config={
                        "hostname_field": "host",
                        "name_template": "{name}",
                        "use_inventory": True,
                        "ip_from_interfaces": True
                    },
                    schedule_type="interval",
                    schedule_config={
                        "interval_minutes": 60
                    },
                    priority=8,
                    sync_strategy="full",
                    conflict_resolution="source_wins",
                    is_enabled=True
                )
                created_rules.append(zabbix_rule)
                print(f"  ✅ 创建Zabbix发现规则: {zabbix_rule.name}")
        
        print(f"\n📋 发现规则创建完成，共创建 {len(created_rules)} 个新规则")
        return created_rules
        
    except Exception as e:
        print(f"❌ 创建发现规则失败: {str(e)}")
        return []


async def test_data_source_connections():
    """测试数据源连接"""
    print("\n🔍 正在测试数据源连接...")
    
    try:
        config = get_discovery_config()
        engine = create_discovery_engine(config)
        
        connection_results = await engine.test_all_connections()
        
        for source_name, result in connection_results.items():
            if result.get("success", False):
                print(f"  ✅ {source_name}: 连接成功 - {result.get('message', '')}")
            else:
                print(f"  ❌ {source_name}: 连接失败 - {result.get('message', '')}")
        
        successful_connections = sum(1 for r in connection_results.values() if r.get("success", False))
        print(f"\n🔍 连接测试完成，{successful_connections}/{len(connection_results)} 个数据源连接成功")
        
        return connection_results
        
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        return {}


async def run_initial_discovery():
    """执行初始发现任务"""
    print("\n🚀 正在执行初始发现...")
    
    try:
        # 获取启用的数据源
        enabled_sources = await DiscoverySource.filter(is_enabled=True).all()
        
        if not enabled_sources:
            print("  ⚠️  没有启用的数据源，跳过初始发现")
            return []
        
        created_jobs = []
        
        for source in enabled_sources:
            # 创建初始发现任务
            job = await DiscoveryJob.create(
                job_name=f"初始发现 - {source.display_name}",
                description=f"系统初始化时的首次自动发现任务",
                discovery_source=source,
                job_config={
                    "initial_discovery": True,
                    "force_sync": True,
                    "created_at_init": True
                }
            )
            
            created_jobs.append(job)
            print(f"  📝 创建初始发现任务: {job.job_name} (ID: {job.id})")
        
        print(f"\n🚀 已创建 {len(created_jobs)} 个初始发现任务")
        print("  ℹ️  任务将在后台异步执行，可通过API查看执行状态")
        
        return created_jobs
        
    except Exception as e:
        print(f"❌ 创建初始发现任务失败: {str(e)}")
        return []


async def display_initialization_summary():
    """显示初始化摘要"""
    print("\n" + "=" * 80)
    print("🎉 CMDB发现系统初始化完成!")
    print("=" * 80)
    
    try:
        # 统计数据源
        total_sources = await DiscoverySource.all().count()
        enabled_sources = await DiscoverySource.filter(is_enabled=True).count()
        
        # 统计发现规则
        total_rules = await DiscoveryRule.all().count()
        enabled_rules = await DiscoveryRule.filter(is_enabled=True).count()
        
        # 统计任务
        total_jobs = await DiscoveryJob.all().count()
        pending_jobs = await DiscoveryJob.filter(status=JobStatusEnum.PENDING).count()
        
        print(f"\n📊 系统状态摘要:")
        print(f"  • 数据源: {enabled_sources}/{total_sources} 已启用")
        print(f"  • 发现规则: {enabled_rules}/{total_rules} 已启用")
        print(f"  • 发现任务: {total_jobs} 总数，{pending_jobs} 等待执行")
        
        print(f"\n🔧 下一步操作:")
        print(f"  1. 启动FastAPI应用:")
        print(f"     uvicorn app.run:app --host 0.0.0.0 --port 9999 --reload")
        
        print(f"\n  2. 启动Celery Worker (用于异步任务):")
        print(f"     celery -A app.core.celery_app worker --loglevel=info")
        
        print(f"\n  3. 启动Celery Beat (用于定时任务):")
        print(f"     celery -A app.core.celery_app beat --loglevel=info")
        
        print(f"\n  4. 访问API文档:")
        print(f"     http://localhost:9999/docs")
        
        print(f"\n  5. 测试发现API:")
        print(f"     curl http://localhost:9999/api/v1/discovery/health")
        
        print(f"\n📚 相关文档:")
        print(f"  • 配置文档: app/docs/06-environment-configuration.md")
        print(f"  • API文档: http://localhost:9999/docs")
        print(f"  • 发现引擎日志: app/logs/discovery.log")
        
    except Exception as e:
        print(f"❌ 获取摘要信息失败: {str(e)}")


async def main():
    """主函数"""
    print("🚀 CMDB发现系统初始化开始")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 初始化数据库
        if not await init_database():
            print("💥 数据库初始化失败，无法继续")
            return
        
        # 2. 创建默认数据源
        await create_default_data_sources()
        
        # 3. 创建默认发现规则
        await create_default_discovery_rules()
        
        # 4. 测试数据源连接
        await test_data_source_connections()
        
        # 5. 执行初始发现（可选）
        await run_initial_discovery()
        
        # 6. 显示初始化摘要
        await display_initialization_summary()
        
    except Exception as e:
        print(f"\n💥 初始化过程中发生错误: {str(e)}")
        logger.exception("初始化失败")
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()


if __name__ == "__main__":
    # 检查环境
    if not os.path.exists(".env"):
        print("⚠️  警告: 未找到 .env 文件，请确保环境配置正确")
        print("   可以运行: python app/scripts/setup_env.py --env development")
    
    # 运行初始化
    asyncio.run(main()) 