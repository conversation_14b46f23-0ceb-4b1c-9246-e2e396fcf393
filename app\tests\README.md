# 测试目录说明文档

本文档描述了Vue-FastAPI工单管理系统的测试结构和使用方法。

## 📁 测试目录结构

```
app/tests/
├── README.md                    # 本说明文档
├── conftest.py                  # pytest配置和公共fixtures
├── run_tests.py                 # 测试运行脚本
├── __init__.py                  # 包初始化文件
│
├── discovery_module/            # 🔍 发现模块测试
│   ├── test_zabbix_discovery.py           # Zabbix发现功能测试
│   ├── test_prometheus_discovery.py       # Prometheus发现功能测试  
│   ├── test_zabbix_data_debug.py          # Zabbix数据映射调试
│   └── test_discovery_direct.py           # 直接发现引擎测试
│
├── cmdb_module/                 # 🏗️ CMDB模块测试
│   └── test_clean_and_reimport.py         # 清理重新导入测试
│
├── ticket_module/               # 🎫 工单模块测试
│   └── test_tickets.py                    # 工单功能测试
│
└── [其他模块测试文件...]
```

## 🧪 测试分类说明

### 1. Discovery Module Tests (发现模块测试)

**位置**: `app/tests/discovery_module/`

#### `test_zabbix_discovery.py`
- **用途**: 专门测试Zabbix发现功能
- **功能**: 
  - 检查Zabbix数据源状态
  - 执行完整的Zabbix发现流程
  - 创建并跟踪发现任务
  - 统计发现结果和配置项
- **运行命令**: `python app/tests/discovery_module/test_zabbix_discovery.py`

#### `test_prometheus_discovery.py` 
- **用途**: 测试Prometheus发现功能
- **功能**:
  - 目标发现测试
  - 服务发现测试
  - 数据映射功能验证
- **运行命令**: `python app/tests/discovery_module/test_prometheus_discovery.py`

#### `test_zabbix_data_debug.py`
- **用途**: 调试Zabbix数据映射问题
- **功能**:
  - 分析原始Zabbix主机数据
  - 测试数据映射逻辑
  - 检查asset_tag、hostname、IP地址等字段
- **运行命令**: `python app/tests/discovery_module/test_zabbix_data_debug.py`

#### `test_discovery_direct.py`
- **用途**: 直接测试发现引擎，避免Celery异步问题
- **功能**:
  - 完整的发现引擎测试
  - 多数据源发现
  - 执行时间和结果统计
- **运行命令**: `python app/tests/discovery_module/test_discovery_direct.py`

### 2. CMDB Module Tests (CMDB模块测试)

**位置**: `app/tests/cmdb_module/`

#### `test_clean_and_reimport.py`
- **用途**: 清理和重新导入CMDB数据
- **功能**:
  - 删除现有发现的配置项
  - 重新执行发现流程
  - 数据质量检查（IP地址重复等）
  - 统计和分析结果
- **运行命令**: `python app/tests/cmdb_module/test_clean_and_reimport.py`

### 3. Ticket Module Tests (工单模块测试)

**位置**: `app/tests/ticket_module/`

#### `test_tickets.py`
- **用途**: 工单系统功能测试
- **功能**: 工单创建、更新、状态变更等测试

## 🚀 运行测试

### 单个测试文件运行
```bash
# 运行Zabbix发现测试
python app/tests/discovery_module/test_zabbix_discovery.py

# 运行Prometheus发现测试  
python app/tests/discovery_module/test_prometheus_discovery.py

# 运行数据清理重新导入测试
python app/tests/cmdb_module/test_clean_and_reimport.py
```

### 批量测试运行
```bash
# 使用pytest运行所有测试
pytest app/tests/

# 运行特定模块测试
pytest app/tests/discovery_module/
pytest app/tests/cmdb_module/
```

### 使用测试运行脚本
```bash
python app/tests/run_tests.py
```

## 🔧 环境配置

### 必要条件
1. **数据库连接**: 确保MySQL数据库可访问
2. **Redis连接**: Celery需要Redis作为broker
3. **外部服务**: 
   - Prometheus服务器 (http://*************:31891)
   - Zabbix服务器 (http://************/zabbix/api_jsonrpc.php)

### 环境变量
测试会自动加载 `app/settings/env.development` 配置文件。

## 📊 测试数据分析

### 数据质量问题检测
测试可以检测以下数据质量问题：
- IP地址重复
- Asset Tag重复
- 字段缺失（如operating_system）
- 数据映射错误

### 修复记录
最近修复的问题：
1. **Asset Tag重复**: 修改为 `zabbix_{hostid}_{uuid前8位}` 格式
2. **Hostname混乱**: 使用Zabbix的name字段而不是UUID格式的host字段
3. **操作系统字段**: 改进了从inventory获取OS信息的逻辑

## 🐛 问题排查

### 常见问题
1. **模块导入错误**: 确保项目根目录在Python路径中
2. **数据库连接失败**: 检查环境配置和数据库服务状态
3. **外部服务连接超时**: 验证Prometheus和Zabbix服务可达性

### 调试建议
1. 使用 `test_zabbix_data_debug.py` 调试Zabbix数据映射
2. 查看详细的日志输出了解错误原因
3. 使用 `test_discovery_direct.py` 绕过Celery进行测试

## 📝 测试报告

测试结果示例：
```
=== Zabbix发现测试 ===
✅ 找到Zabbix数据源: 生产Zabbix监控 (ZABBIX)
📊 任务执行前配置项数量: 25
📈 新增配置项: 177
🎉 测试完成!
```

## 🔄 持续集成

这些测试文件已经按照项目规范重新组织，支持：
- pytest框架集成
- CI/CD管道集成
- 模块化测试执行
- 详细的测试报告 