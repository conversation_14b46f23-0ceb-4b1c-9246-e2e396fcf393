# -*- coding: utf-8 -*-
"""
CMDB发现Celery任务

实现异步的数据发现、处理和同步任务
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from celery import Task
import hashlib
import json
import traceback

from app.core.celery_app import celery_app
from app.models.cmdb import (
    DiscoveryJob, DiscoverySource, DiscoveryRule, ConfigurationItem,
    DiscoveryAuditLog, JobStatusEnum, CITypeEnum, CIStatusEnum
)
from app.discovery.engine import CMDBDiscoveryEngine, create_discovery_engine
from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector
from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector
from app.settings.config import get_discovery_config
from tortoise import Tortoise
from app.settings.config import settings

logger = logging.getLogger(__name__)


class DiscoveryTask(Task):
    """
    发现任务基类
    
    提供任务执行的通用功能和错误处理
    """
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败时的回调"""
        logger.error(f"任务 {task_id} 执行失败: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功时的回调"""
        logger.info(f"任务 {task_id} 执行成功")


@celery_app.task(base=DiscoveryTask, bind=True)
def run_discovery_job(self, job_id: int, force_sync: bool = False):
    """执行发现任务"""
    
    async def _run_discovery():
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        try:
            job = await DiscoveryJob.get(id=job_id)
            discovery_source = await job.discovery_source
            
            logger.info(f"开始执行发现任务: {job.job_name} (ID: {job_id})")
            
            # 更新任务状态
            job.status = JobStatusEnum.RUNNING
            job.started_at = datetime.now()
            job.celery_task_id = self.request.id
            await job.save()
            
            # 创建发现引擎
            config = get_discovery_config()
            engine = create_discovery_engine(config)
            
            # 执行发现
            results = await engine.run_discovery(
                source_names=[discovery_source.name],
                job_id=job_id
            )
            
            # 处理结果
            source_result = results.get(discovery_source.name)
            if source_result:
                job.total_items = source_result.total_items
                job.processed_items = source_result.processed_items
                job.successful_items = source_result.successful_items
                job.failed_items = source_result.failed_items
                job.skipped_items = source_result.skipped_items
                job.result_summary = source_result.summary
                job.execution_duration = int(source_result.execution_time)
                
                if source_result.status.value == "已完成":
                    job.status = JobStatusEnum.COMPLETED
                else:
                    job.status = JobStatusEnum.FAILED
                    job.error_details = {"errors": source_result.errors}
            else:
                job.status = JobStatusEnum.FAILED
                job.error_details = {"error": "未找到发现结果"}
            
            job.completed_at = datetime.now()
            job.progress = 100
            await job.save()
            
            # 更新数据源统计
            discovery_source.total_discoveries += 1
            if job.status == JobStatusEnum.COMPLETED:
                discovery_source.successful_discoveries += 1
            discovery_source.last_discovery_at = datetime.now()
            await discovery_source.save()
            
            logger.info(f"发现任务 {job_id} 执行完成，状态: {job.status}")
            
            return {
                "job_id": job_id,
                "status": job.status.value,
                "total_items": job.total_items,
                "successful_items": job.successful_items,
                "failed_items": job.failed_items
            }
            
        except Exception as e:
            logger.error(f"发现任务 {job_id} 执行异常: {str(e)}")
            logger.error(traceback.format_exc())
            
            try:
                job = await DiscoveryJob.get(id=job_id)
                job.status = JobStatusEnum.FAILED
                job.completed_at = datetime.now()
                job.error_details = {
                    "error": str(e),
                    "traceback": traceback.format_exc()
                }
                await job.save()
            except:
                pass
            
            raise e
        
        finally:
            await Tortoise.close_connections()
    
    return asyncio.run(_run_discovery())


@celery_app.task(base=DiscoveryTask)
def run_prometheus_discovery():
    """定时执行Prometheus发现任务"""
    
    async def _run():
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        try:
            prometheus_source = await DiscoverySource.filter(
                source_type="PROMETHEUS", 
                is_enabled=True
            ).first()
            
            if not prometheus_source:
                logger.warning("未找到启用的Prometheus数据源")
                return
            
            job = await DiscoveryJob.create(
                job_name=f"定时Prometheus发现 - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                description="定时执行的Prometheus自动发现任务",
                discovery_source=prometheus_source,
                job_config={"scheduled": True, "auto_discovery": True}
            )
            
            run_discovery_job.delay(job.id)
            logger.info(f"已创建定时Prometheus发现任务: {job.id}")
            
        finally:
            await Tortoise.close_connections()
    
    return asyncio.run(_run())


@celery_app.task(base=DiscoveryTask)
def run_zabbix_discovery():
    """定时执行Zabbix发现任务"""
    
    async def _run():
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        try:
            zabbix_source = await DiscoverySource.filter(
                source_type="ZABBIX",
                is_enabled=True
            ).first()
            
            if not zabbix_source:
                logger.warning("未找到启用的Zabbix数据源")
                return
            
            job = await DiscoveryJob.create(
                job_name=f"定时Zabbix发现 - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
                description="定时执行的Zabbix自动发现任务",
                discovery_source=zabbix_source,
                job_config={"scheduled": True, "auto_discovery": True}
            )
            
            run_discovery_job.delay(job.id)
            logger.info(f"已创建定时Zabbix发现任务: {job.id}")
            
        finally:
            await Tortoise.close_connections()
    
    return asyncio.run(_run())


@celery_app.task(base=DiscoveryTask)
def health_check_task():
    """健康检查任务"""
    
    async def _check():
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        try:
            sources = await DiscoverySource.filter(is_enabled=True).all()
            
            for source in sources:
                try:
                    config = get_discovery_config()
                    
                    if source.source_type == "PROMETHEUS":
                        connector = PrometheusDiscoveryConnector(config["prometheus"])
                        result = await connector.test_connection()
                    elif source.source_type == "ZABBIX":
                        connector = ZabbixDiscoveryConnector(config["zabbix"])
                        result = await connector.test_connection()
                    else:
                        continue
                    
                    source.is_healthy = result.get("status") == "connected"
                    source.last_test_at = datetime.now()
                    if not source.is_healthy:
                        source.last_error = result.get("error", "未知错误")
                    else:
                        source.last_error = None
                    
                    await source.save()
                    
                except Exception as e:
                    source.is_healthy = False
                    source.last_test_at = datetime.now()
                    source.last_error = str(e)
                    await source.save()
                    logger.error(f"数据源 {source.name} 健康检查失败: {str(e)}")
            
        finally:
            await Tortoise.close_connections()
    
    return asyncio.run(_check())


@celery_app.task(base=DiscoveryTask)
def cleanup_old_jobs():
    """清理旧的任务记录"""
    
    async def _cleanup():
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        try:
            cutoff_date = datetime.now() - timedelta(days=30)
            
            deleted_jobs = await DiscoveryJob.filter(
                created_at__lt=cutoff_date
            ).delete()
            
            deleted_logs = await DiscoveryAuditLog.filter(
                created_at__lt=cutoff_date
            ).delete()
            
            logger.info(f"清理完成: 删除了 {deleted_jobs} 个任务记录和 {deleted_logs} 个审计日志")
            
            return {
                "deleted_jobs": deleted_jobs,
                "deleted_logs": deleted_logs
            }
            
        finally:
            await Tortoise.close_connections()
    
    return asyncio.run(_cleanup()) 