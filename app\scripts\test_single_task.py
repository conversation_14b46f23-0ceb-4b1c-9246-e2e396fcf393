#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试单个Celery任务执行
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))


async def test_run_discovery_job():
    """测试运行发现任务"""
    print("🚀 测试发现任务执行...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoveryJob, DiscoverySource, ConfigurationItem
        from app.settings.config import settings
        from app.discovery.tasks import run_discovery_job
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 查找可用的数据源
        prometheus_source = await DiscoverySource.filter(name="prometheus").first()
        zabbix_source = await DiscoverySource.filter(name="zabbix").first()
        
        if not prometheus_source and not zabbix_source:
            print("❌ 没有找到任何数据源")
            await Tortoise.close_connections()
            return False
        
        # 选择一个数据源进行测试
        source = prometheus_source if prometheus_source else zabbix_source
        
        print(f"📝 使用数据源: {source.name} ({source.source_type})")
        
        # 创建测试任务
        job = await DiscoveryJob.create(
            job_name=f"手动测试任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            description="手动执行的测试发现任务",
            discovery_source=source,
            job_config={"test": True, "manual": True}
        )
        
        print(f"📝 创建测试任务: {job.id} - {job.job_name}")
        
        # 查看任务前配置项数量
        ci_count_before = await ConfigurationItem.all().count()
        print(f"📊 任务执行前配置项数量: {ci_count_before}")
        
        # 模拟Celery任务请求
        class FakeRequest:
            def __init__(self):
                self.id = f"test_task_{job.id}"
        
        class FakeTask:
            def __init__(self):
                self.request = FakeRequest()
        
        fake_task = FakeTask()
        
        # 执行任务
        print("🔄 开始执行任务...")
        result = run_discovery_job(fake_task, job.id)
        
        if result:
            print("✅ 任务执行完成:")
            print(f"  状态: {result.get('status')}")
            print(f"  总项目: {result.get('total_items', 0)}")
            print(f"  成功项目: {result.get('successful_items', 0)}")
            print(f"  失败项目: {result.get('failed_items', 0)}")
            
            # 查看任务后配置项数量
            ci_count_after = await ConfigurationItem.all().count()
            print(f"📊 任务执行后配置项数量: {ci_count_after}")
            print(f"📈 新增配置项: {ci_count_after - ci_count_before}")
            
            # 显示最新的配置项
            if ci_count_after > ci_count_before:
                print("\n📋 新增的配置项:")
                new_items = await ConfigurationItem.all().limit(5).order_by('-created_at')
                for item in new_items:
                    print(f"  - {item.name} ({item.ci_type}) - {item.ip_addresses}")
        else:
            print("❌ 任务执行失败")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        try:
            await Tortoise.close_connections()
        except:
            pass
        return False


async def test_celery_tasks_directly():
    """直接测试Celery任务函数"""
    print("\n🧪 直接测试Celery任务函数...")
    
    try:
        from app.discovery.tasks import health_check_task, run_prometheus_discovery, run_zabbix_discovery
        
        # 测试健康检查任务
        print("🔧 执行健康检查任务...")
        health_result = health_check_task()
        print("✅ 健康检查任务完成")
        
        # 测试Prometheus发现任务
        print("🔧 执行Prometheus发现任务...")
        prometheus_result = run_prometheus_discovery()
        print("✅ Prometheus发现任务完成")
        
        # 测试Zabbix发现任务
        print("🔧 执行Zabbix发现任务...")
        zabbix_result = run_zabbix_discovery()
        print("✅ Zabbix发现任务完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主函数"""
    print("🧪 Celery任务单独测试")
    print("=" * 50)
    
    # 测试发现任务
    discovery_ok = await test_run_discovery_job()
    
    # 测试定时任务
    tasks_ok = await test_celery_tasks_directly()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"  发现任务测试: {'✅' if discovery_ok else '❌'}")
    print(f"  定时任务测试: {'✅' if tasks_ok else '❌'}")
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}") 