from fastapi import APIRouter

from .jobs import router as jobs_router
from .sources import router as sources_router
from .configuration_items import router as configuration_items_router
from .health import router as health_router
from .statistics import router as statistics_router

router = APIRouter()

# 包含各个子模块的路由
router.include_router(health_router, tags=["CMDB发现-健康检查"])
router.include_router(jobs_router, prefix="/jobs", tags=["CMDB发现-任务管理"])
router.include_router(sources_router, prefix="/sources", tags=["CMDB发现-数据源管理"])
router.include_router(configuration_items_router, prefix="/configuration-items", tags=["CMDB发现-配置项管理"])
router.include_router(statistics_router, prefix="/statistics", tags=["CMDB发现-统计分析"])

__all__ = ["router"] 