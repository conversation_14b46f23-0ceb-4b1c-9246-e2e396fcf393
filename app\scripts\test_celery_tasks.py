#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Celery任务测试脚本

用于测试各种Celery任务的执行情况
"""

import asyncio
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))


async def check_discovery_sources():
    """检查发现数据源"""
    print("\n🔍 检查发现数据源...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoverySource
        from app.settings.config import settings
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        sources = await DiscoverySource.all()
        print(f"📊 发现数据源总数: {len(sources)}")
        
        if sources:
            for source in sources:
                status = "启用" if source.is_enabled else "禁用"
                health = "健康" if source.is_healthy else "异常"
                print(f"  - {source.name} ({source.source_type}) - {status} - {health}")
                if source.last_error:
                    print(f"    最后错误: {source.last_error}")
        else:
            print("  ⚠️  没有找到任何数据源，需要先初始化")
        
        await Tortoise.close_connections()
        return len(sources) > 0
        
    except Exception as e:
        print(f"❌ 检查数据源失败: {str(e)}")
        return False


async def check_discovery_jobs():
    """检查发现任务"""
    print("\n📋 检查发现任务...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoveryJob
        from app.settings.config import settings
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 获取最近的任务
        jobs = await DiscoveryJob.all().limit(10).order_by('-created_at')
        print(f"📊 最近的 {len(jobs)} 个任务:")
        
        if jobs:
            for job in jobs:
                status_icon = {
                    "PENDING": "⏳",
                    "RUNNING": "🔄", 
                    "COMPLETED": "✅",
                    "FAILED": "❌",
                    "CANCELLED": "🚫"
                }.get(job.status.value, "❓")
                
                print(f"  {status_icon} {job.job_name} - {job.status.value} - {job.created_at}")
                if job.error_details:
                    print(f"    错误: {job.error_details}")
        else:
            print("  ℹ️  没有找到任何任务记录")
        
        await Tortoise.close_connections()
        return len(jobs)
        
    except Exception as e:
        print(f"❌ 检查任务失败: {str(e)}")
        return 0


async def test_prometheus_connection():
    """测试Prometheus连接"""
    print("\n🔗 测试Prometheus连接...")
    
    try:
        from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector
        from app.settings.config import get_prometheus_config
        
        config = get_prometheus_config()
        connector = PrometheusDiscoveryConnector(config)
        
        result = await connector.test_connection()
        
        if result.get("status") == "connected":
            print(f"  ✅ Prometheus连接成功")
            print(f"  📊 指标数量: {result.get('metrics_count', 0)}")
        else:
            print(f"  ❌ Prometheus连接失败: {result.get('error')}")
        
        return result.get("status") == "connected"
        
    except Exception as e:
        print(f"❌ Prometheus连接测试失败: {str(e)}")
        return False


async def test_zabbix_connection():
    """测试Zabbix连接"""
    print("\n🔗 测试Zabbix连接...")
    
    try:
        from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector
        from app.settings.config import get_zabbix_config
        
        config = get_zabbix_config()
        connector = ZabbixDiscoveryConnector(config)
        
        result = await connector.test_connection()
        
        if result.get("status") == "connected":
            print(f"  ✅ Zabbix连接成功")
            if "version" in result:
                print(f"  📊 获取到 {len(result['version'])} 个主机组")
        else:
            print(f"  ❌ Zabbix连接失败: {result.get('error')}")
        
        return result.get("status") == "connected"
        
    except Exception as e:
        print(f"❌ Zabbix连接测试失败: {str(e)}")
        return False


def test_celery_worker():
    """测试Celery Worker是否运行"""
    print("\n🔧 测试Celery Worker状态...")
    
    try:
        from app.core.celery_app import celery_app
        
        # 获取活跃的worker
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print(f"  ✅ 发现 {len(active_workers)} 个活跃的Worker:")
            for worker_name, tasks in active_workers.items():
                print(f"    - {worker_name}: {len(tasks)} 个活跃任务")
        else:
            print("  ⚠️  没有发现活跃的Celery Worker")
            print("  💡 请确保已启动: celery -A app.core.celery_app worker --loglevel=info")
        
        return bool(active_workers)
        
    except Exception as e:
        print(f"❌ Celery Worker检查失败: {str(e)}")
        return False


def test_celery_beat():
    """测试Celery Beat调度器"""
    print("\n⏰ 测试Celery Beat调度器...")
    
    try:
        from app.core.celery_app import celery_app
        
        # 获取调度任务
        beat_schedule = celery_app.conf.beat_schedule
        
        if beat_schedule:
            print(f"  ✅ 配置了 {len(beat_schedule)} 个定时任务:")
            for task_name, config in beat_schedule.items():
                print(f"    - {task_name}: {config['task']}")
                print(f"      调度: {config['schedule']}")
        else:
            print("  ⚠️  没有配置定时任务")
        
        return bool(beat_schedule)
        
    except Exception as e:
        print(f"❌ Celery Beat检查失败: {str(e)}")
        return False


async def run_test_discovery_job():
    """运行测试发现任务"""
    print("\n🚀 创建并执行测试发现任务...")
    
    try:
        from tortoise import Tortoise
        from app.models.cmdb import DiscoveryJob, DiscoverySource
        from app.settings.config import settings
        from app.discovery.tasks import run_discovery_job
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        
        # 查找可用的数据源
        source = await DiscoverySource.filter(is_enabled=True).first()
        
        if not source:
            print("  ❌ 没有找到启用的数据源")
            await Tortoise.close_connections()
            return False
        
        # 创建测试任务
        job = await DiscoveryJob.create(
            job_name=f"手动测试任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            description="手动执行的测试发现任务",
            discovery_source=source,
            job_config={"test": True, "manual": True}
        )
        
        print(f"  📝 创建了测试任务: {job.id} - {job.job_name}")
        
        # 同步执行任务（不使用Celery，直接调用）
        print("  🔄 开始执行任务...")
        
        # 使用FakeRequest模拟Celery任务请求
        class FakeRequest:
            def __init__(self):
                self.id = f"test_task_{job.id}"
        
        class FakeTask:
            def __init__(self):
                self.request = FakeRequest()
        
        fake_task = FakeTask()
        
        # 执行任务
        result = run_discovery_job(fake_task, job.id)
        
        if result:
            print(f"  ✅ 任务执行完成:")
            print(f"    状态: {result.get('status')}")
            print(f"    总项目: {result.get('total_items', 0)}")
            print(f"    成功项目: {result.get('successful_items', 0)}")
            print(f"    失败项目: {result.get('failed_items', 0)}")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"❌ 测试任务执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        try:
            await Tortoise.close_connections()
        except:
            pass
        return False


async def test_celery_tasks():
    """测试Celery定时任务"""
    print("\n📅 测试Celery定时任务...")
    
    try:
        from app.discovery.tasks import health_check_task, run_prometheus_discovery, run_zabbix_discovery
        
        # 测试健康检查任务
        print("  🔧 执行健康检查任务...")
        health_result = health_check_task()
        print(f"    ✅ 健康检查任务完成")
        
        # 测试Prometheus发现任务
        print("  🔧 执行Prometheus发现任务...")
        prometheus_result = run_prometheus_discovery()
        print(f"    ✅ Prometheus发现任务完成")
        
        # 测试Zabbix发现任务  
        print("  🔧 执行Zabbix发现任务...")
        zabbix_result = run_zabbix_discovery()
        print(f"    ✅ Zabbix发现任务完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 定时任务测试失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False


async def main():
    """主函数"""
    print("🧪 Celery任务系统测试")
    print("=" * 50)
    
    # 检查基础状态
    sources_ok = await check_discovery_sources()
    job_count = await check_discovery_jobs()
    
    # 检查连接
    prometheus_ok = await test_prometheus_connection()
    zabbix_ok = await test_zabbix_connection()
    
    # 检查Celery状态
    worker_ok = test_celery_worker()
    beat_ok = test_celery_beat()
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"  数据源状态: {'✅' if sources_ok else '❌'}")
    print(f"  任务记录: {job_count} 条")
    print(f"  Prometheus连接: {'✅' if prometheus_ok else '❌'}")
    print(f"  Zabbix连接: {'✅' if zabbix_ok else '❌'}")
    print(f"  Celery Worker: {'✅' if worker_ok else '❌'}")
    print(f"  Celery Beat: {'✅' if beat_ok else '❌'}")
    
    # 如果基础检查通过，执行更深入的测试
    if sources_ok and (prometheus_ok or zabbix_ok):
        print("\n🚀 开始执行深度测试...")
        
        # 测试定时任务
        tasks_ok = await test_celery_tasks()
        print(f"  定时任务测试: {'✅' if tasks_ok else '❌'}")
        
        # 运行完整的发现任务测试
        discovery_ok = await run_test_discovery_job()
        print(f"  发现任务测试: {'✅' if discovery_ok else '❌'}")
    else:
        print("\n⚠️  基础检查未通过，跳过深度测试")
        print("💡 建议:")
        if not sources_ok:
            print("  - 运行: python app/scripts/init_cmdb_discovery.py 初始化数据源")
        if not prometheus_ok:
            print("  - 检查Prometheus服务是否启动和配置是否正确")
        if not zabbix_ok:
            print("  - 检查Zabbix服务是否启动和配置是否正确")
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {str(e)}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}") 