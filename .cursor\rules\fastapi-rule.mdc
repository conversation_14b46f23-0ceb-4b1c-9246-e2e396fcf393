---
description: 
globs: 
alwaysApply: true
---
---
description: 使用 FastAPI、Tortoise ORM、Vue 3 (Vite、Pinia、Naive UI)、Docker 及相关技术的“工单全生命周期管理系统”开发最佳实践与模式。此规则集要求每次操作都同步生成/更新代码和文档。
globs:
  - "**/*.py"
  - "**/*.vue"
  - "**/*.ts"
  - "**/*.scss"
  - "pyproject.toml"
  - "pnpm-lock.yaml"
  - "*.md"
  - "Dockerfile"
  - "docker-compose.yml"
  - "**/aerich.ini"
---

# Work Order Full Lifecycle Management System: Development Rules (工单全生命周期管理系统：开发规则)

## ⓪ Core Principle: Mandated Synchronized Code & Documentation (核心原则：强制同步代码与文档)
- **Mandatory Initial Documentation (强制初始文档):** 所有初始生成的代码（模块、类、函数、API 端点、Vue 组件、Pinia store、工具函数）*必须*从一开始就附带清晰、简洁的文档。这包括：
    - Python: Docstrings (Google 风格)，复杂逻辑的内联注释。
    - Vue/TypeScript: 组件、props、函数、store 的 JSDoc/TSDoc 注释。
    - Project Level (项目层面): `README.md`，特定功能文档文件，或架构决策记录。
- **Action: Synchronized Documentation Updates with Every Change (行动：每次变更同步更新文档):** *至关重要的是，在您（Cursor）执行的每一次代码生成、修改、添加或重构操作时，您必须立即并同时更新所有相关文档。* 这包括但不限于 README 文件、特定功能文档、Python docstrings、Vue/TS JSDoc/TSDoc 注释以及 API 规范 (OpenAPI)。文档更新必须准确反映代码更改，并且是代码更改所在的同一响应或提交的一部分。目标是使文档成为代码库永久的、活生生的、准确的镜像。
- **API Documentation Integrity (API 文档完整性):** FastAPI 的 OpenAPI 文档 (`/docs`, `/redoc`) 必须随着 API 路由、模型或参数的任何更改而自动准确地更新，作为同步文档更新过程的一部分。

## ① Project Structure (项目结构)

- **Root Directory Layout (根目录布局):**
    - `app/`: 后端应用程序源代码。
    - `deploy/`: 部署相关文件和配置。
    - `web/`: 前端应用程序源代码和构建配置。
    - `tests/`: 后端测试，与 `app/` 平行。
    - `pyproject.toml`: Python 项目元数据和依赖 (Poetry)。
    - `package.json` & `pnpm-lock.yaml`: 前端项目元数据和依赖 (pnpm)。
    - `Dockerfile`, `docker-compose.yml`: 容器化设置。
    - `.env` files: 环境变量 (附带 `.env.example`)。
    - `aerich.ini` & `migrations/`: 数据库迁移配置和文件 (Aerich for Tortoise ORM)，通常位于根目录或与 `app/` 同级。

- **Backend (`app/` directory - FastAPI) (后端 (`app/` 目录 - FastAPI)):**
    - `run.py`: Main FastAPI application instance, startup events, and core application setup.
    - `app/api/v1/`: Version 1 API endpoints.
        - `app/api/v1/users/`: User-related API endpoint modules.
        - `app/api/v1/roles/`: Role-related API endpoint modules.
        - `app/api/v1/menus/`: Menu-related API endpoint modules.
        - `app/api/v1/apis/`: General/other API management endpoint modules.
        - `app/api/v1/base/`: Base or common information API endpoint modules.
    - `app/controllers/`: Business logic controllers or service handlers, invoked by API routers/endpoints. (Ensure clear separation from router definitions if used this way).
    - `app/core/`: Core application components (e.g., authentication logic, database connection management, core FastAPI dependencies, application factory).
    - `app/log/`: Directory for log file outputs (if Loguru is configured to write to files).
    - `app/models/`: Tortoise ORM database model definitions.
    - `app/schemas/`: Pydantic schemas for data validation, serialization (request/response models).
    - `app/settings/`: Configuration settings, including Pydantic settings models (e.g., loading from `.env` files).
    - `app/utils/`: Backend utility functions and helper modules.

- **Frontend (`web/` directory - Vue 3 with Vite) (前端 (`web/` 目录 - Vue 3 与 Vite)):**
    - `web/build/`: Vite build configurations and scripts.
        - `web/build/config/`: Build-specific configurations.
        - `web/build/plugin/`: Custom Vite plugins.
        - `web/build/script/`: Auxiliary build scripts.
    - `web/public/`: Publicly accessible static assets that are copied as-is to the build output.
        - `web/public/resource/`: Specific subdirectory for public resources.
    - `web/index.html`: Main HTML entry point for the Vite application.
    - `web/settings/`: Frontend project-specific configurations (e.g., theme settings, global constants).
    - `web/src/`: Main frontend source code.
        - `web/src/main.ts`: Main Vue application setup and initialization.
        - `web/src/api/`: Frontend API service definitions (Axios instances/wrappers for backend communication).
        - `web/src/assets/`: Static assets processed by Vite (e.g., images, fonts, global styles).
            - `web/src/assets/images/`: Image files.
            - `web/src/assets/js/`: Utility JavaScript files (if not fitting elsewhere like `utils`).
            - `web/src/assets/svg/`: SVG icon files (especially if not managed by `vite-plugin-svg-icons`).
        - `web/src/components/`: Reusable Vue components.
            - `web/src/components/common/`: General-purpose common components.
            - `web/src/components/icon/`: Icon-specific components.
            - `web/src/components/page/`: Components related to page structure or common page elements.
            - `web/src/components/query-bar/`: Query bar components.
            - `web/src/components/table/`: Table-related components.
        - `web/src/composables/`: Vue Composition API reusable functions (composables).
        - `web/src/directives/`: Custom Vue directives.
        - `web/src/layout/`: Application layout components.
            - `web/src/layout/components/`: Specific components used within layouts (e.g., sidebar, header, footer).
        - `web/src/router/`: Vue Router configuration.
            - `web/src/router/guard/`: Navigation guards.
            - `web/src/router/routes/`: Route definitions.
        - `web/src/store/`: Pinia state management.
            - `web/src/store/modules/`: Pinia store modules, organized by feature.
        - `web/src/styles/`: Global stylesheets, SCSS variables, mixins.
        - `web/src/utils/`: Frontend utility functions.
            - `web/src/utils/auth/`: Authentication-related utilities (e.g., token handling).
            - `web/src/utils/common/`: General common utilities.
            - `web/src/utils/http/`: HTTP client setup (e.g., Axios instance, interceptors).
            - `web/src/utils/storage/`: Wrappers for `localStorage` and `sessionStorage`.
        - `web/src/views/`: Page-level view components.
            - `web/src/views/error-page/`: Error display pages (e.g., 404, 500).
            - `web/src/views/login/`: Login page components.
            - `web/src/views/profile/`: User profile page.
            - `web/src/views/system/`: System administration related views/pages.
            - `web/src/views/workbench/`: Main user dashboard or workbench views.


- **Deployment (`deploy/` directory) (部署 (`deploy/` 目录)):**
    - `deploy/sample-picture/`: Example images or other deployment-related visual assets.
    - (This directory can also house deployment scripts, `k8s` manifests, Nginx configurations for Docker, etc. as the project evolves).

## ② Code Style & Linting (代码风格与 Linting)
- **Python (Backend) (Python (后端)):**
    - 遵循 **Black (v23.12.1)** 进行代码格式化。
    - 使用 **isort (v5.13.2)** 进行导入排序 (与 Black 兼容配置)。
    - 遵守 **Ruff (v0.0.281)** 进行 linting 和快速修复。
    - 遵循 PEP 8 命名约定: `snake_case` 用于函数/变量/模块，`PascalCase` 用于类，`UPPER_SNAKE_CASE` 用于常量。
    - 最大行长由 Black 配置。
- **TypeScript/Vue/SCSS (Frontend) (TypeScript/Vue/SCSS (前端)):**
    (此部分规则已根据 pnpm list 更新，保持不变)
    - Use **ESLint (v8.57.0)** with appropriate Vue 3 and TypeScript plugins (e.g. `@unocss/eslint-config@0.55.7`, `@zclzone/eslint-config@0.0.4`).
    - Use **Prettier** for code formatting (ensure ESLint compatibility).
    - Follow established Vue 3 and TypeScript best practices (e.g., `<script setup>`, Composition API).
    - Consistent naming conventions (`PascalCase` for Vue components, `camelCase` for TS variables/functions).

## ③ Type Hints & Data Validation (类型提示与数据验证)
- **Python (Backend) (Python (后端)):**
    - 对所有函数/方法参数和返回值使用 **Python 3.11+** 类型提示。
    - 广泛使用 **Pydantic (v2.11.4)** 进行请求/响应模型、数据验证，并使用 **pydantic-settings (v2.9.1)** 进行配置。
    - 使用 `typing` 模块中的类型 (例如 `typing_extensions (v4.13.2)`)。
- **TypeScript (Frontend) (TypeScript (前端)):**
    (此部分规则已根据 pnpm list 更新，保持不变)
    - Leverage **TypeScript (v5.5.4)** for strong typing.
    - Define interfaces and types for props, Pinia stores, API responses, complex objects.
    - Use `src/types/` or co-locate types.

## ④ FastAPI Backend Specifics (FastAPI 后端细节)
- **Async Operations (异步操作):** 优先为 I/O 密集型操作使用 `async` 和 `await` (Tortoise ORM, 外部 API)。
- **Dependency Injection (依赖注入):** 有效利用 FastAPI 的依赖注入系统。
- **Routers (路由器):** 按功能使用 `APIRouter` 组织 API 端点。
- **Error Handling (错误处理):** 实现自定义全局异常处理程序以提供标准化的 JSON 错误响应。
- **Logging (日志记录):** 使用 **Loguru (v0.7.2)** 进行结构化、可配置的日志记录。
- **Configuration (配置):** 使用 **pydantic-settings (v2.9.1)** 管理设置。
- **ASGI Server (ASGI 服务器):** 使用 **Uvicorn (v0.30.1)** 运行。
- **FastAPI Version (FastAPI 版本):** **FastAPI (v0.111.0)**。

## ⑤ Database & ORM (Tortoise ORM) (数据库与 ORM (Tortoise ORM))
- **Async ORM (异步 ORM):** 所有通过 **Tortoise ORM (v0.24.0)** 的数据库操作必须是异步的。
- **Models (模型):** 在 `app/models/` 中定义，继承 `tortoise.Model`，清晰定义字段、关系和索引。
- **Migrations (迁移):** 使用 **Aerich (v0.8.1)** (`aerich init-db`, `aerich migrate`, `aerich upgrade`)。记录复杂迁移。
- **Transactions (事务):** 使用原子事务 (`@atomic()`) 保证数据一致性。
- **Querying (查询):** 编写高效查询，使用 `prefetch_related` 和 `select_related` 避免 N+1 问题。
- **Database Compatibility (数据库兼容性):** 虽然开发环境使用 **Mysql (PyMySQL v1.1.1, aiomysql v0.2.0)**，但要确保 ORM 的使用与生产数据库兼容。

## ⑥ Authentication & Authorization (RBAC) (认证与授权 (RBAC))
- **JWT Authentication (JWT 认证):** 使用 **PyJWT (v2.8.0)** 的无状态认证 (用户 ID, 角色, 过期时间)。保护 JWT 密钥。
- **Password Hashing (密码哈希):** 使用 **Passlib (v1.7.4)** 与 **Argon2-cffi (v23.1.0)**。
- **Email Validation (邮箱验证):** 使用 **email-validator (v2.1.1)**。
- **RBAC Implementation (RBAC 实现):** 定义角色。通过 FastAPI 依赖项检查 JWT 和角色来保护 API 端点。支持基于角色的动态前端路由。

## ⑦ Vue 3 Frontend Specifics (Vue 3 前端细节)
- **Composition API (组合式 API):** 主要使用 Composition API 和 `<script setup>`。
- **Vue (v3.4.34)**
- **Vue Router (v4.4.0):** 客户端路由、导航守卫、动态路由支持。
- **Pinia (v2.2.0):** 在 `web/src/store/modules/` 中进行集中式状态管理。类型化的 state、getters、actions。
- **UI Framework (Naive UI v2.39.0):** 使用 Naive UI 组件。
- **Styling (UnoCSS & Sass):** 使用 **UnoCSS (v0.55.7)** 实现原子化 CSS，使用 **Sass (v1.77.8)** 实现全局样式/变量。
- **HTTP Client (Axios v1.7.2):** 带有拦截器的集中式 Axios 实例。
- **Utility Libraries (工具库):**
    - **@vueuse/core (v10.11.0)**
    - **dayjs (v1.11.12)**
    - **lodash-es (v4.17.21)** (选择性使用)
    - **vue-i18n (v9.13.1)** (如果适用)
    - **@iconify/json (v2.2.232)**
    - **@iconify/vue (v4.1.2)**
- **Build Tool (Vite v4.5.3):** 快速开发服务器、优化构建。相关 Vite 插件版本 (若在 `dependencies` 或 `devDependencies` 中明确列出):
    - **@vitejs/plugin-vue (v4.6.2)** (devDependency)
    - **unplugin-auto-import (v0.16.7)**
    - **unplugin-vue-components (v0.25.2)**
    - **unplugin-icons (v0.16.6)**
    - **vite-plugin-compression (v0.5.1)**
    - **vite-plugin-svg-icons (v2.0.1)**
    - **vite-plugin-html (v3.2.2)**
    - **rollup-plugin-visualizer (v5.12.0)** (用于分析构建包大小)
- **Other Development Dependencies (其他开发依赖):**
    - **eslint (v8.57.0)**
    - **typescript (v5.5.4)**
    - **@unocss/eslint-config (v0.55.7)**
    - **@zclzone/eslint-config (v0.0.4)**
    - **dotenv (v16.4.5)** (用于环境变量管理)

## ⑧ API Design (API 设计)
- **RESTful Principles (RESTful 原则):** 遵循 RESTful 设计。
- **Request/Response Models (请求/响应模型):** 在 FastAPI 中使用 Pydantic 模型。
- **HTTP Status Codes (HTTP 状态码):** 使用适当的状态码。
- **Error Responses (错误响应):** 一致、信息丰富的 JSON 错误。
- **OpenAPI Generation (OpenAPI 生成):** 确保 FastAPI 维护准确的 OpenAPI 规范，并在每次 API 更改时更新 (见规则 ⓪)。

## ⑨ Testing (测试)
- **Python (Backend - Pytest Recommended) (Python (后端 - 推荐 Pytest)):** 单元测试和集成测试 (FastAPI `TestClient`)。可使用 **pytest (v8.3.5)**。模拟依赖项。力求良好的覆盖率。
- **TypeScript/Vue (Frontend - Vitest/Vue Test Utils Recommended) (TypeScript/Vue (前端 - 推荐 Vitest/Vue Test Utils)):** 单元测试和组件测试。如果范围允许，考虑 E2E 测试 (Playwright/Cypress)。

## ⑩ Security (安全)
- **Input Validation (输入验证):** 严格的后端验证 (Pydantic, `email-validator`)。
- **HTTPS:** 通过 Nginx SSL 终止实现生产部署。
- **CORS:** 适当配置 FastAPI 的 `CORSMiddleware`。
- **Dependency Security (依赖安全):** 定期更新依赖项 (Poetry, pnpm) 并进行审计。
- **Secrets Management (密钥管理):** 通过环境变量 (非硬编码)。
- **Rate Limiting (速率限制):** 考虑用于敏感 API。
- **Security Headers (安全头部):** 通过 Nginx 或 FastAPI 中间件。

## ⑪ Deployment & DevOps (部署与 DevOps)
- **Containerization (Docker) (容器化 (Docker)):** 用于后端 (Python 3.11-slim) 和前端 (Nginx 提供静态文件) 的多阶段 `Dockerfile`。用于本地开发的 `docker-compose.yml`。
- **Web Server/Reverse Proxy (Nginx) (Web 服务器/反向代理 (Nginx)):** Uvicorn/FastAPI 的反向代理，提供前端静态资源、SSL、路由、安全头部。
- **Environment (环境):** 目标 **Linux (Debian-based)**。
- **CI/CD (Conceptual) (CI/CD (概念性)):** 结构对 CI/CD 友好。

## ⑫ Development Workflow (开发工作流)
- **Dependency Management (依赖管理):** 后端: **conda**。前端: **pnpm**。
- **Version Control (版本控制):** Git 及清晰的分支策略。
- **Code Formatting/Linting (代码格式化/Linting):** 如果可能，将工具集成到预提交钩子中。
- **Environment Variables (环境变量):** 本地开发使用 `.env` (在 `.gitignore` 中)，提供 `.env.example`。

## ⑬ Package Management & Dependencies (包管理与依赖)
- **Pinning (版本锁定):** 确保通过 `poetry.lock` 和 `pnpm-lock.yaml` 锁定依赖项。
- **Regular Updates (定期更新):** 定期审查和更新依赖项。
- **Minimize Dependencies (最小化依赖):** 仅包含必要的包。

## ⑭ Overriding Operational Directive for Cursor (覆盖 Cursor 操作指令)
**在您为此项目处理的每一次交互、编码任务或修改请求时，您 (Cursor) 必须：**
1.  **Implement Code Changes (执行代码更改):** 根据所有先前的规则执行请求的代码生成或修改。

2.  **Update Documentation Concurrently (同时更新文档):** *立即并在同一响应/操作中，* 生成或更新所有相关文档 (如此规则集中的规则 ⓪ 和各处所详述)，以准确反映您所做的代码更改。这不是可选的，并且是每个输出的核心要求。

需要注意：
* 所有回复都使用中文
* 我的系统为windows系统使用powershell需要注意
* `app/scripts/`: Stores tool scripts and command-line utilities. Allows for organization into subdirectories based on functionality or type (e.g., `app/scripts/data_processing/`, `app/scripts/deployment/`).
* `app/tests/`: Stores all test scripts. It is required that each unit or module under test has its own dedicated subdirectory for its related test files (e.g., `app/tests/user_module/`, `app/tests/product_api/`).
* `app/scripts/sql/`: Stores all SQL scripts for project initialization and initial data insertion. Allows for organization into subdirectories based on database version, module, or functionality (e.g., `app/scripts/sql/v1.0/`, `app/scripts/sql/user_data/`).
* `app/docs/`: Stores all project Markdown document files. Files should be organized and named using a numerical prefix to indicate their order or logical structure (e.g., app/docs/01-introduction.md, app/docs/02-setup-guide.md, app/docs/10-api-reference.md).
* fastapi后端启动命令为uvicorn.run("app:app", host="0.0.0.0", port=9999, reload=True, log_config="uvicorn_loggin_config.json")
---


