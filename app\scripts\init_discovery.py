#!/usr/bin/env python3
"""
CMDB发现功能初始化脚本

此脚本用于：
1. 验证配置和连接
2. 创建初始数据源
3. 设置发现规则
4. 运行第一次发现任务

使用方法:
    python app/scripts/init_discovery.py
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.settings.config import get_discovery_config, get_prometheus_config, get_zabbix_config


async def create_default_data_sources():
    """创建默认数据源配置"""
    print("=== 创建默认数据源 ===")
    
    # 这里模拟数据源创建，实际应该调用API或直接操作数据库
    prometheus_config = get_prometheus_config()
    zabbix_config = get_zabbix_config()
    
    data_sources = [
        {
            "name": "Production-Prometheus",
            "source_type": "prometheus",
            "connection_config": {
                "url": prometheus_config["url"],
                "timeout": prometheus_config["timeout"]
            },
            "discovery_rules": {
                "enabled_jobs": prometheus_config["enabled_jobs"],
                "excluded_jobs": prometheus_config["excluded_jobs"],
                "service_discovery_rules": prometheus_config["service_discovery_rules"]
            },
            "sync_interval": 1800,  # 30分钟
            "is_enabled": True,
            "description": "生产环境Prometheus监控数据源"
        },
        {
            "name": "Production-Zabbix",
            "source_type": "zabbix",
            "connection_config": {
                "url": zabbix_config["url"],
                "username": zabbix_config["username"],
                "password": zabbix_config["password"]
            },
            "discovery_rules": {
                "enabled_groups": zabbix_config["enabled_groups"],
                "excluded_hosts": zabbix_config["excluded_hosts"]
            },
            "sync_interval": 3600,  # 1小时
            "is_enabled": True,
            "description": "生产环境Zabbix监控数据源"
        }
    ]
    
    print("准备创建以下数据源:")
    for ds in data_sources:
        print(f"  - {ds['name']} ({ds['source_type']})")
    
    return data_sources


async def test_data_source_connections(data_sources):
    """测试数据源连接"""
    print("\n=== 测试数据源连接 ===")
    
    results = {}
    
    for ds in data_sources:
        source_name = ds["name"]
        source_type = ds["source_type"]
        
        print(f"\n测试 {source_name}...")
        
        try:
            if source_type == "prometheus":
                from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector
                connector = PrometheusDiscoveryConnector(ds["connection_config"])
                result = await connector.test_connection()
                
            elif source_type == "zabbix":
                from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector
                connector = ZabbixDiscoveryConnector(ds["connection_config"])
                result = await connector.test_connection()
                
            else:
                result = {"success": False, "message": f"不支持的数据源类型: {source_type}"}
            
            results[source_name] = result
            status = "✅ 成功" if result["success"] else "❌ 失败"
            message = result.get("message", "")
            print(f"  {status}: {message}")
            
        except Exception as e:
            results[source_name] = {"success": False, "message": str(e)}
            print(f"  ❌ 失败: {str(e)}")
    
    return results


async def run_initial_discovery():
    """运行初始发现任务"""
    print("\n=== 运行初始发现任务 ===")
    
    try:
        from app.discovery.engine import create_discovery_engine
        
        # 创建发现引擎
        engine = create_discovery_engine()
        
        print("发现引擎创建成功，开始运行发现...")
        
        # 运行发现
        discovery_results = await engine.run_discovery()
        
        print(f"\n发现完成，处理了 {len(discovery_results)} 个数据源:")
        
        for source_name, result in discovery_results.items():
            print(f"\n{source_name}:")
            print(f"  状态: {result.status.value}")
            print(f"  总项目数: {result.total_items}")
            print(f"  处理成功: {result.successful_items}")
            print(f"  处理失败: {result.failed_items}")
            print(f"  跳过项目: {result.skipped_items}")
            
            if result.errors:
                print(f"  错误信息: {'; '.join(result.errors[:3])}")  # 只显示前3个错误
        
        return True
        
    except Exception as e:
        print(f"❌ 发现任务失败: {str(e)}")
        return False


def create_discovery_rules():
    """创建发现规则示例"""
    print("\n=== 创建发现规则 ===")
    
    discovery_rules = [
        {
            "name": "Kubernetes Pod 发现",
            "source_type": "prometheus",
            "rule_config": {
                "job_pattern": "kubernetes-pods",
                "target_ci_type": "CONTAINER_SERVICE",
                "mapping_rules": {
                    "name": "labels.pod",
                    "namespace": "labels.namespace",
                    "container_name": "labels.container",
                    "node_name": "labels.node"
                }
            },
            "is_active": True
        },
        {
            "name": "Linux 服务器发现",
            "source_type": "zabbix",
            "rule_config": {
                "host_group_pattern": "Linux servers",
                "target_ci_type": "PHYSICAL_SERVER",
                "mapping_rules": {
                    "hostname": "host",
                    "ip_address": "interfaces[0].ip",
                    "os": "inventory.os",
                    "cpu": "inventory.hardware",
                    "memory": "inventory.memory"
                }
            },
            "is_active": True
        },
        {
            "name": "MySQL 数据库发现",
            "source_type": "prometheus",
            "rule_config": {
                "job_pattern": "mysql-exporter",
                "target_ci_type": "DATABASE_SERVICE",
                "mapping_rules": {
                    "instance": "labels.instance",
                    "version": "labels.version",
                    "port": "labels.port"
                }
            },
            "is_active": True
        }
    ]
    
    print("准备创建以下发现规则:")
    for rule in discovery_rules:
        print(f"  - {rule['name']} ({rule['source_type']})")
    
    return discovery_rules


def print_next_steps():
    """打印后续步骤指导"""
    print("\n" + "=" * 60)
    print("🎉 CMDB发现功能初始化完成!")
    print("=" * 60)
    
    print("\n📋 后续步骤:")
    print("1. 启动FastAPI应用:")
    print("   uvicorn app.run:app --host 0.0.0.0 --port 9999 --reload")
    
    print("\n2. 启动Celery Worker (用于异步任务):")
    print("   celery -A app.celery worker --loglevel=info")
    
    print("\n3. 访问API文档:")
    print("   http://localhost:9999/docs")
    
    print("\n4. 测试发现API:")
    print("   curl http://localhost:9999/api/v1/discovery/health")
    
    print("\n5. 创建定时发现任务:")
    print("   celery -A app.celery beat --loglevel=info")
    
    print("\n📊 监控和管理:")
    print("- 查看发现日志: tail -f app/logs/discovery.log")
    print("- 健康检查: http://localhost:9999/api/v1/discovery/health")
    print("- 统计信息: http://localhost:9999/api/v1/discovery/statistics")
    
    print("\n🔧 配置文件:")
    print("- 主配置: app/settings/config.py")
    print("- 环境变量模板: app/settings/env.example")
    print("- 文档: app/docs/03-cmdb-discovery-setup.md")


async def main():
    """主初始化函数"""
    print("CMDB发现功能初始化")
    print("=" * 50)
    
    # 1. 创建默认数据源
    data_sources = await create_default_data_sources()
    
    # 2. 测试连接
    connection_results = await test_data_source_connections(data_sources)
    
    # 3. 创建发现规则
    discovery_rules = create_discovery_rules()
    
    # 4. 运行初始发现
    print("\n是否运行初始发现任务? (输入 'y' 确认, 其他键跳过)")
    try:
        user_input = input().strip().lower()
        if user_input == 'y':
            discovery_success = await run_initial_discovery()
        else:
            print("跳过初始发现任务")
            discovery_success = True
    except KeyboardInterrupt:
        print("\n用户取消操作")
        discovery_success = False
    
    # 5. 总结和后续步骤
    print("\n" + "=" * 50)
    print("初始化总结:")
    
    successful_connections = sum(1 for r in connection_results.values() if r["success"])
    total_connections = len(connection_results)
    
    print(f"数据源连接: {successful_connections}/{total_connections} 成功")
    
    for name, result in connection_results.items():
        status = "✅" if result["success"] else "❌"
        print(f"  {status} {name}")
    
    print(f"发现规则: {len(discovery_rules)} 个已准备")
    
    if successful_connections > 0:
        print_next_steps()
        return 0
    else:
        print("\n❌ 没有可用的数据源连接，请检查配置后重试")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断操作，退出初始化")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n初始化过程中发生错误: {str(e)}")
        sys.exit(1) 