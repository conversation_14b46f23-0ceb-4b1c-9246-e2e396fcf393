# -*- coding: utf-8 -*-
"""
分页工具函数

提供通用的分页功能支持
"""

import math
from typing import Dict, Any, List
from tortoise.queryset import QuerySet


async def paginate(query: QuerySet, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
    """
    通用分页函数
    
    Args:
        query: Tortoise ORM 查询对象
        page: 页码，从1开始
        page_size: 每页记录数
        
    Returns:
        包含分页信息的字典
    """
    # 计算总记录数
    total = await query.count()
    
    # 计算总页数
    total_pages = math.ceil(total / page_size) if total > 0 else 1
    
    # 计算偏移量
    offset = (page - 1) * page_size
    
    # 获取当前页数据
    items = await query.offset(offset).limit(page_size)
    
    return {
        "items": items,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1
    }


def paginate_list(items: List[Any], page: int = 1, page_size: int = 20) -> Dict[str, Any]:
    """
    对Python列表进行分页
    
    Args:
        items: 要分页的列表
        page: 页码，从1开始
        page_size: 每页记录数
        
    Returns:
        包含分页信息的字典
    """
    total = len(items)
    total_pages = math.ceil(total / page_size) if total > 0 else 1
    
    # 计算起始和结束索引
    start_idx = (page - 1) * page_size
    end_idx = start_idx + page_size
    
    # 获取当前页数据
    page_items = items[start_idx:end_idx]
    
    return {
        "items": page_items,
        "total": total,
        "page": page,
        "page_size": page_size,
        "total_pages": total_pages,
        "has_next": page < total_pages,
        "has_prev": page > 1
    }


class PaginationParams:
    """分页参数类"""
    
    def __init__(self, page: int = 1, page_size: int = 20):
        self.page = max(1, page)  # 确保页码至少为1
        self.page_size = min(max(1, page_size), 100)  # 限制每页大小在1-100之间
        
    @property
    def offset(self) -> int:
        """计算偏移量"""
        return (self.page - 1) * self.page_size
        
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size 