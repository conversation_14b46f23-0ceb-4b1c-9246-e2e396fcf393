import sqlite3

# 连接到数据库
conn = sqlite3.connect('../../db.sqlite3')
cursor = conn.cursor()

print("检查菜单配置...")
# 查询所有菜单
cursor.execute("SELECT id, name, path, component, menu_type, parent_id FROM menu")
menus = cursor.fetchall()

print("菜单列表:")
for menu in menus:
    print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
          f"类型: {menu[4]}, 父级ID: {menu[5]}")

print("\n检查工单相关菜单...")
cursor.execute("SELECT id, name, path, component, menu_type, parent_id FROM menu WHERE name LIKE '%工单%'")
ticket_menus = cursor.fetchall()

print("工单相关菜单:")
for menu in ticket_menus:
    print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
          f"类型: {menu[4]}, 父级ID: {menu[5]}")

print("\n检查API表结构...")
# 获取API表的列名
cursor.execute("PRAGMA table_info(api)")
api_columns = cursor.fetchall()
print("API表列名:")
for col in api_columns:
    print(f"列名: {col[1]}, 类型: {col[2]}")

print("\n检查API配置...")
# 查询所有API
cursor.execute("SELECT * FROM api LIMIT 5")
apis = cursor.fetchall()

print("API样例数据:")
for api in apis:
    print(f"API数据: {api}")

print("\n检查工单相关API...")
cursor.execute("SELECT * FROM api WHERE path LIKE '%tickets%'")
ticket_apis = cursor.fetchall()

print("工单相关API:")
for api in ticket_apis:
    print(f"API数据: {api}")

# 关闭数据库连接
conn.close() 