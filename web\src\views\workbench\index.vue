<template>
  <AppPage :show-footer="false">
    <div class="dashboard-layout">
      <!-- 头部标题区域 -->
      <div class="dashboard-header">
        <div class="header-content">
          <h1 class="dashboard-title">运维监控中心</h1>
          <div class="header-actions">
            <n-space>
              <n-select
                v-model:value="selectedTimeRange"
                size="small"
                :options="timeRangeOptions"
                style="width: 120px"
              />
              <n-button size="small" type="primary" @click="refreshData" :loading="isRefreshing">
                <template #icon>
                  <n-icon><TrendingUp /></n-icon>
                </template>
                刷新数据
              </n-button>
            </n-space>
          </div>
        </div>
      </div>

      <!-- 网格布局主体 -->
      <div class="dashboard-grid">
        <!-- 第一行：核心指标 -->
        <div class="metrics-row">
          <div class="metric-card primary-metric" :style="{ 'animation-delay': '0ms' }">
            <div class="metric-icon-wrapper primary">
              <n-icon size="28"><Server /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">
                <n-skeleton v-if="loading.stats" width="60px" height="36px" />
                <n-number-animation v-else :from="0" :to="1234" :duration="2000" />
              </div>
              <div class="metric-label">总资产</div>
              <div class="metric-trend positive">
                <n-icon size="12"><TrendingUp /></n-icon>
                <span>+12.5%</span>
              </div>
            </div>
          </div>

          <div class="metric-card success-metric" :style="{ 'animation-delay': '100ms' }">
            <div class="metric-icon-wrapper success">
              <n-icon size="28"><Cpu /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">
                <n-skeleton v-if="loading.stats" width="60px" height="36px" />
                <n-number-animation v-else :from="0" :to="856" :duration="2000" />
              </div>
              <div class="metric-label">在线服务器</div>
              <div class="metric-trend positive">
                <span class="status-indicator online"></span>
                <span>98.5%</span>
              </div>
            </div>
          </div>

          <div class="metric-card warning-metric" :style="{ 'animation-delay': '200ms' }">
            <div class="metric-icon-wrapper warning">
              <n-icon size="28"><AlertTriangle /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">
                <n-skeleton v-if="loading.alertStats" width="60px" height="36px" />
                <n-number-animation v-else :from="0" :to="23" :duration="2000" />
              </div>
              <div class="metric-label">活跃告警</div>
              <div class="metric-trend negative">
                <n-icon size="12"><AlertTriangle /></n-icon>
                <span>需关注</span>
              </div>
            </div>
          </div>

          <div class="metric-card info-metric" :style="{ 'animation-delay': '300ms' }">
            <div class="metric-icon-wrapper info">
              <n-icon size="28"><Users /></n-icon>
            </div>
            <div class="metric-content">
              <div class="metric-number">
                <n-skeleton v-if="loading.stats" width="60px" height="36px" />
                <n-number-animation v-else :from="0" :to="45" :duration="2000" />
              </div>
              <div class="metric-label">在线用户</div>
              <div class="metric-trend neutral">
                <span class="user-dots">
                  <span v-for="i in 3" :key="i" class="dot" :style="{ 'animation-delay': `${i * 200}ms` }"></span>
                </span>
                <span>活跃中</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 第二行：监控面板 -->
        <div class="panels-row">
          <!-- 系统监控 -->
          <div class="panel-card monitoring-panel">
            <div class="panel-header">
              <h3 class="panel-title">系统监控</h3>
              <n-button-group size="tiny">
                <n-button>实时</n-button>
                <n-button type="primary">1小时</n-button>
                <n-button>1天</n-button>
              </n-button-group>
            </div>
            <div class="panel-content">
              <n-skeleton v-if="loading.systemStatus" height="200px" />
              <div v-else class="monitoring-grid">
                <div v-for="item in systemStatus" :key="item.name" class="monitor-item">
                  <div class="monitor-info">
                    <span class="monitor-name">{{ item.label }}</span>
                    <span class="monitor-value" :class="item.valueClass">
                      {{ item.value }}{{ item.unit }}
                    </span>
                  </div>
                  <div v-if="item.showProgress" class="monitor-progress">
                    <div class="progress-container">
                      <div 
                        class="progress-bar" 
                        :class="item.status"
                        :style="`width: ${item.percentage}%`"
                      ></div>
                    </div>
                    <div class="progress-label">{{ item.percentage }}%</div>
                  </div>
                  <div v-else class="monitor-detail">
                    <span class="detail-label">实时流量</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 性能图表 -->
          <div class="panel-card chart-panel">
            <div class="panel-header">
              <h3 class="panel-title">性能趋势</h3>
              <div class="chart-legend">
                <div class="legend-item">
                  <span class="legend-dot cpu"></span>
                  <span>CPU</span>
                </div>
                <div class="legend-item">
                  <span class="legend-dot memory"></span>
                  <span>内存</span>
                </div>
              </div>
            </div>
            <div class="panel-content">
              <n-skeleton v-if="loading.systemLoad" height="200px" />
              <div v-else class="chart-area">
                <!-- 简化的面积图 -->
                <div class="chart-wrapper">
                  <svg class="performance-chart" viewBox="0 0 300 120">
                    <!-- 网格线 -->
                    <defs>
                      <pattern id="grid" width="30" height="24" patternUnits="userSpaceOnUse">
                        <path d="M 30 0 L 0 0 0 24" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                      </pattern>
                      <!-- CPU渐变 -->
                      <linearGradient id="cpuGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.05" />
                      </linearGradient>
                      <!-- 内存渐变 -->
                      <linearGradient id="memoryGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                        <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.3" />
                        <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.05" />
                      </linearGradient>
                    </defs>
                    
                    <!-- 背景网格 -->
                    <rect width="300" height="120" fill="url(#grid)" />
                    
                    <!-- CPU曲线 -->
                    <path :d="cpuPath" fill="url(#cpuGradient)" />
                    <path :d="cpuLinePath" stroke="#3b82f6" stroke-width="2" fill="none" />
                    
                    <!-- 内存曲线 -->
                    <path :d="memoryPath" fill="url(#memoryGradient)" />
                    <path :d="memoryLinePath" stroke="#10b981" stroke-width="2" fill="none" />
                    
                    <!-- 数据点 -->
                    <circle v-for="(point, index) in cpuPoints" :key="`cpu-${index}`"
                            :cx="point.x" :cy="point.y" r="3" fill="#3b82f6"
                            class="chart-point" :style="`animation-delay: ${index * 100}ms`" />
                  </svg>
                </div>
                <div class="chart-x-axis">
                  <span v-for="i in 6" :key="i">{{ (i - 1) * 2 }}h</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 第三行：活动和任务 -->
        <div class="activities-row">
          <!-- 最新活动 -->
          <div class="panel-card activity-panel">
            <div class="panel-header">
              <h3 class="panel-title">最新活动</h3>
              <n-switch v-model:value="autoRefresh" size="small">
                <template #checked>自动</template>
                <template #unchecked>手动</template>
              </n-switch>
            </div>
            <div class="panel-content">
              <n-skeleton v-if="loading.activities" height="240px" />
              <div v-else class="activity-list">
                <div v-for="(activity, index) in latestActivities" :key="activity.id" 
                     class="activity-item" :style="`animation-delay: ${index * 100}ms`">
                  <div class="activity-indicator" :class="activity.type"></div>
                  <div class="activity-main">
                    <div class="activity-title">{{ activity.title }}</div>
                    <div class="activity-description">{{ activity.content }}</div>
                  </div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 任务队列 -->
          <div class="panel-card tasks-panel">
            <div class="panel-header">
              <h3 class="panel-title">任务队列</h3>
              <n-badge :value="recentTasks.length" type="info" show-zero>
                <n-icon size="18"><Tool /></n-icon>
              </n-badge>
            </div>
            <div class="panel-content">
              <n-skeleton v-if="loading.recentTasks" height="240px" />
              <div v-else class="tasks-list">
                <div v-for="(task, index) in recentTasks" :key="task.id" 
                     class="task-item" :style="`animation-delay: ${index * 80}ms`">
                  <div class="task-status" :class="task.statusType">
                    <div class="status-dot"></div>
                  </div>
                  <div class="task-main">
                    <div class="task-name">{{ task.name }}</div>
                    <div class="task-time">{{ task.time }}</div>
                  </div>
                  <div class="task-badge">
                    <n-tag :type="task.statusType" size="small" round>{{ task.status }}</n-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="panel-card actions-panel">
            <div class="panel-header">
              <h3 class="panel-title">快速操作</h3>
            </div>
            <div class="panel-content">
              <div class="quick-actions">
                <div class="action-item" v-for="action in quickActions" :key="action.key">
                  <div class="action-icon" :class="action.type">
                    <n-icon size="20"><component :is="action.icon" /></n-icon>
                  </div>
                  <div class="action-content">
                    <div class="action-label">{{ action.label }}</div>
                    <div class="action-description">{{ action.description }}</div>
                  </div>
                  <n-button size="small" quaternary>执行</n-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppPage>
</template>

<script setup>
import { useUserStore } from '@/store'
import { useI18n } from 'vue-i18n'
import { Activity, Tool, TrendingUp, AlertTriangle, Server, Users, Shield, Cpu } from '@vicons/tabler'
import api from '@/api'

const { t } = useI18n({ useScope: 'global' })
const userStore = useUserStore()

// 响应式数据
const loading = reactive({
  stats: true,
  systemStatus: true,
  recentTasks: true,
  systemLoad: true,
  alertStats: true,
  activities: true,
})

const selectedTimeRange = ref('24h')
const autoRefresh = ref(true)
const isRefreshing = ref(false)
const systemStatus = ref([])
const recentTasks = ref([])
const chartData = ref([])
const latestActivities = ref([])

// 时间范围选项
const timeRangeOptions = [
  { label: '1小时', value: '1h' },
  { label: '24小时', value: '24h' },
  { label: '7天', value: '7d' },
  { label: '30天', value: '30d' }
]

// 快速操作
const quickActions = ref([
  { key: 'backup', label: '数据备份', description: '执行系统备份', icon: Shield, type: 'primary' },
  { key: 'restart', label: '重启服务', description: '重启核心服务', icon: Activity, type: 'warning' },
  { key: 'cleanup', label: '清理缓存', description: '清理系统缓存', icon: Tool, type: 'info' },
  { key: 'update', label: '系统更新', description: '检查更新包', icon: TrendingUp, type: 'success' }
])

// 计算属性 - 图表数据
const cpuData = computed(() => {
  const data = [45, 52, 48, 61, 55, 67, 59, 72, 64, 58]
  return data.map((value, index) => ({
    x: (index / (data.length - 1)) * 300,
    y: 120 - (value / 100) * 100
  }))
})

const memoryData = computed(() => {
  const data = [38, 45, 42, 48, 52, 49, 56, 53, 47, 51]
  return data.map((value, index) => ({
    x: (index / (data.length - 1)) * 300,
    y: 120 - (value / 100) * 100
  }))
})

const cpuPoints = computed(() => cpuData.value)

const cpuLinePath = computed(() => {
  if (!cpuData.value.length) return ''
  return `M ${cpuData.value.map(p => `${p.x} ${p.y}`).join(' L ')}`
})

const cpuPath = computed(() => {
  if (!cpuData.value.length) return ''
  const points = cpuData.value
  return `M 0 120 L ${points.map(p => `${p.x} ${p.y}`).join(' L ')} L 300 120 Z`
})

const memoryLinePath = computed(() => {
  if (!memoryData.value.length) return ''
  return `M ${memoryData.value.map(p => `${p.x} ${p.y}`).join(' L ')}`
})

const memoryPath = computed(() => {
  if (!memoryData.value.length) return ''
  const points = memoryData.value
  return `M 0 120 L ${points.map(p => `${p.x} ${p.y}`).join(' L ')} L 300 120 Z`
})

// 模拟数据生成函数
function generateMockSystemStatus() {
  return [
    {
      name: 'cpu',
      label: 'CPU使用率',
      value: 45,
      unit: '%',
      percentage: 45,
      status: 'success',
      valueClass: 'value-success',
      showProgress: true
    },
    {
      name: 'memory',
      label: '内存使用率',
      value: 72,
      unit: '%',
      percentage: 72,
      status: 'warning',
      valueClass: 'value-warning',
      showProgress: true
    },
    {
      name: 'disk',
      label: '磁盘使用率',
      value: 38,
      unit: '%',
      percentage: 38,
      status: 'success',
      valueClass: 'value-success',
      showProgress: true
    },
    {
      name: 'network',
      label: '网络带宽',
      value: '1.2GB/s',
      unit: '',
      valueClass: 'value-info',
      showProgress: false
    }
  ]
}

function generateMockRecentTasks() {
  return [
    { id: 1, name: '数据库备份', status: '完成', statusType: 'success', time: '2分钟前' },
    { id: 2, name: '应用部署', status: '进行中', statusType: 'info', time: '5分钟前' },
    { id: 3, name: '系统更新', status: '等待中', statusType: 'warning', time: '10分钟前' },
    { id: 4, name: '日志清理', status: '完成', statusType: 'success', time: '15分钟前' },
    { id: 5, name: '安全扫描', status: '进行中', statusType: 'info', time: '20分钟前' }
  ]
}

function generateMockActivities() {
  return [
    {
      id: 1,
      type: 'success',
      title: '备份任务完成',
      content: '数据库备份已完成，文件大小2.3GB',
      time: '2分钟前'
    },
    {
      id: 2,
      type: 'warning',
      title: 'CPU使用率告警',
      content: 'Web服务器-01 CPU使用率达到85%',
      time: '5分钟前'
    },
    {
      id: 3,
      type: 'info',
      title: '新用户注册',
      content: '用户"张三"已成功注册系统',
      time: '10分钟前'
    },
    {
      id: 4,
      type: 'success',
      title: '系统优化完成',
      content: '数据库索引优化已完成，性能提升15%',
      time: '30分钟前'
    },
    {
      id: 5,
      type: 'info',
      title: '定时任务执行',
      content: '日志清理任务已自动执行',
      time: '1小时前'
    }
  ]
}

// 加载数据函数
async function loadDashboardData() {
  try {
    setTimeout(() => {
      systemStatus.value = generateMockSystemStatus()
      loading.systemStatus = false
      loading.stats = false
    }, 300)

    setTimeout(() => {
      recentTasks.value = generateMockRecentTasks()
      loading.recentTasks = false
    }, 500)

    setTimeout(() => {
      loading.alertStats = false
    }, 700)

    setTimeout(() => {
      latestActivities.value = generateMockActivities()
      loading.activities = false
    }, 900)

    setTimeout(() => {
      loading.systemLoad = false
    }, 1100)

  } catch (error) {
    console.error('加载仪表盘数据失败:', error)
    window.$message?.error('加载仪表盘数据失败')
  }
}

async function refreshData() {
  isRefreshing.value = true
  await loadDashboardData()
  setTimeout(() => {
    isRefreshing.value = false
  }, 1000)
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard-layout {
  min-height: 100vh;
  background: #f8fafc;
  padding: 24px;
}

/* 头部区域 */
.dashboard-header {
  margin-bottom: 32px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

/* 网格布局 */
.dashboard-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* 指标行 */
.metrics-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(20px);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-icon-wrapper {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.metric-icon-wrapper.primary { background: rgba(59, 130, 246, 0.1); color: #3b82f6; }
.metric-icon-wrapper.success { background: rgba(16, 185, 129, 0.1); color: #10b981; }
.metric-icon-wrapper.warning { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.metric-icon-wrapper.info { background: rgba(139, 92, 246, 0.1); color: #8b5cf6; }

.metric-content {
  flex: 1;
  min-width: 0;
}

.metric-number {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
  color: #1a202c;
}

.metric-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-trend.positive { color: #10b981; }
.metric-trend.negative { color: #ef4444; }
.metric-trend.neutral { color: #6b7280; }

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.online {
  background: #10b981;
  animation: pulse 2s infinite;
}

.user-dots {
  display: flex;
  gap: 2px;
}

.user-dots .dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: #8b5cf6;
  animation: bounce 1.5s infinite;
}

/* 面板行 */
.panels-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.activities-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
}

/* 面板卡片 */
.panel-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.panel-content {
  padding: 20px 24px 24px;
}

/* 监控面板 */
.monitoring-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.monitor-item {
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.monitor-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.monitor-name {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.monitor-value {
  font-size: 18px;
  font-weight: 600;
}

.value-success { color: #10b981; }
.value-warning { color: #f59e0b; }
.value-info { color: #3b82f6; }

.monitor-progress {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-container {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 1s ease-out;
}

.progress-bar.success { background: linear-gradient(90deg, #10b981, #059669); }
.progress-bar.warning { background: linear-gradient(90deg, #f59e0b, #d97706); }

.progress-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  min-width: 32px;
}

.monitor-detail {
  display: flex;
  justify-content: center;
}

.detail-label {
  font-size: 12px;
  color: #64748b;
  background: #e2e8f0;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 图表面板 */
.chart-legend {
  display: flex;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.legend-dot.cpu { background: #3b82f6; }
.legend-dot.memory { background: #10b981; }

.chart-area {
  height: 200px;
}

.chart-wrapper {
  height: 160px;
  margin-bottom: 12px;
}

.performance-chart {
  width: 100%;
  height: 100%;
}

.chart-point {
  animation: pointFadeIn 0.6s ease-out forwards;
  opacity: 0;
}

.chart-x-axis {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  font-size: 12px;
  color: #94a3b8;
}

/* 活动面板 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 240px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  animation: slideInLeft 0.5s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

.activity-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-top: 6px;
  flex-shrink: 0;
}

.activity-indicator.success { background: #10b981; }
.activity-indicator.warning { background: #f59e0b; }
.activity-indicator.info { background: #3b82f6; }

.activity-main {
  flex: 1;
  min-width: 0;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 4px;
}

.activity-description {
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

.activity-time {
  font-size: 12px;
  color: #94a3b8;
  white-space: nowrap;
}

/* 任务面板 */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 240px;
  overflow-y: auto;
}

.task-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  animation: slideInRight 0.5s ease-out forwards;
  opacity: 0;
  transform: translateX(20px);
}

.task-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  flex-shrink: 0;
}

.task-status.success { background: rgba(16, 185, 129, 0.1); }
.task-status.info { background: rgba(59, 130, 246, 0.1); }
.task-status.warning { background: rgba(245, 158, 11, 0.1); }

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
}

.task-status.success .status-dot { background: #10b981; }
.task-status.info .status-dot { background: #3b82f6; }
.task-status.warning .status-dot { background: #f59e0b; }

.task-main {
  flex: 1;
  min-width: 0;
}

.task-name {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 2px;
}

.task-time {
  font-size: 12px;
  color: #64748b;
}

/* 快速操作面板 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.action-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-icon.primary { background: rgba(59, 130, 246, 0.1); color: #3b82f6; }
.action-icon.warning { background: rgba(245, 158, 11, 0.1); color: #f59e0b; }
.action-icon.info { background: rgba(139, 92, 246, 0.1); color: #8b5cf6; }
.action-icon.success { background: rgba(16, 185, 129, 0.1); color: #10b981; }

.action-content {
  flex: 1;
  min-width: 0;
}

.action-label {
  font-size: 14px;
  font-weight: 500;
  color: #1a202c;
  margin-bottom: 2px;
}

.action-description {
  font-size: 12px;
  color: #64748b;
}

/* 动画 */
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pointFadeIn {
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .activities-row {
    grid-template-columns: 1fr 1fr;
  }
  
  .actions-panel {
    grid-column: 1 / -1;
  }
}

@media (max-width: 1024px) {
  .panels-row {
    grid-template-columns: 1fr;
  }
  
  .activities-row {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .metrics-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-layout {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .metrics-row {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    gap: 16px;
  }
}
</style>

