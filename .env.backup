# Vue FastAPI Admin - 开发/测试环境配置
# 此配置用于开发环境和测试环境
# 基于 env.example 中的实际配置，适配开发和测试环境

# =================
# 环境标识
# =================
ENVIRONMENT=development

# =================
# 数据库配置 - 使用 env.example 中的配置
# =================
DB_HOST=************
DB_PORT=3306
DB_USER=backup_user
DB_PASSWORD=v+SCbs/6LsYNovFngEY=
DB_NAME=fastapi_user
DB_CHARSET=utf8mb4

# =================
# 安全配置 - 开发/测试环境 (使用 env.example 中的配置)
# =================
SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=10080
DEBUG=true

# =================
# 应用配置 - 开发/测试环境
# =================
APP_TITLE=自动化运维系统
PROJECT_NAME=自动化运维系统
APP_DESCRIPTION=Development Environment
CORS_ORIGINS=*
CORS_ALLOW_CREDENTIALS=true

# =================
# CMDB发现配置 - 使用 env.example 中的配置
# =================

# Prometheus配置
PROMETHEUS_URL=http://*************:31891
PROMETHEUS_TIMEOUT=30

# Zabbix配置
ZABBIX_URL=http://************/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=niuyeji
ZABBIX_PASSWORD=Changjiu123!

# Redis配置 (用于Celery任务队列)
REDIS_URL=redis://:CjK1rry1as@************:6379/0
CELERY_BROKER_URL=redis://:CjK1rry1as@************:6379/0
CELERY_RESULT_BACKEND=redis://:CjK1rry1as@************:6379/0

# =================
# 发现引擎配置 - 使用 env.example 中的配置
# =================
DISCOVERY_ENABLED=true
DISCOVERY_MAX_CONCURRENT_JOBS=5
DISCOVERY_JOB_TIMEOUT=1800
DISCOVERY_RETRY_ATTEMPTS=3
DISCOVERY_RETRY_DELAY=60

# =================
# 日志配置 - 使用 env.example 中的配置
# =================
DISCOVERY_LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# =================
# 监控和告警配置 - 使用 env.example 中的配置
# =================
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_INTERVAL=300
ALERT_ON_FAILURE=true
ALERT_FAILURE_THRESHOLD=3
ALERT_WEBHOOK_URL=

# =================
# 同步间隔配置 - 使用 env.example 中的配置
# =================
PROMETHEUS_SYNC_INTERVAL=1800
ZABBIX_SYNC_INTERVAL=3600 
# Environment set by setup script
ENV=development
