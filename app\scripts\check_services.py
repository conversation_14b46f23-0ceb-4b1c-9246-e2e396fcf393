import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from tortoise import Tortoise, run_async

# MySQL 配置
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.mysql",
            "credentials": {
                "host": "************",
                "port": "3306",
                "user": "backup_user",
                "password": "v+SCbs/6LsYNovFngEY=",
                "database": "fastapi_user",
                "charset": "utf8mb4"
            }
        }
    },
    "apps": {
        "models": {
            "models": ["app.models.admin", "app.models.ticket", "aerich.models"],
            "default_connection": "default",
        }
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai",
}

async def check_services():
    """检查服务目录数据"""
    print("正在连接数据库...")
    
    try:
        # 初始化Tortoise ORM
        await Tortoise.init(config=TORTOISE_ORM)
        print("数据库连接成功")
        
        # 导入模型
        from app.models.ticket import ServiceCatalog
        
        # 检查服务目录
        print("\n=== 服务目录 ===")
        services = await ServiceCatalog.all()
        print(f"找到 {len(services)} 条服务目录记录")
        
        if services:
            for service in services:
                print(f"ID: {service.id}, 名称: {service.name}")
        else:
            print("没有服务目录数据")
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭连接
        await Tortoise.close_connections()
        print("数据库连接已关闭")

if __name__ == "__main__":
    print("开始检查服务目录数据...")
    run_async(check_services())
    print("检查完成") 