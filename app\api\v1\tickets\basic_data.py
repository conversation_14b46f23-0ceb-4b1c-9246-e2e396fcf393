from fastapi import APIRouter, Depends
from typing import List
from app.models.ticket import TicketCategory, TicketPriority, TicketStatus, ServiceCatalog
from app.models.admin import User
from app.core.dependency import AuthControl, DependPermisson

router = APIRouter()

@router.get("/categories", summary="获取工单分类列表", dependencies=[DependPermisson])
async def get_ticket_categories(
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取工单分类列表"""
    categories = await TicketCategory.filter(is_active=True).order_by('order')
    return {
        "data": [
            {
                "id": category.id,
                "name": category.name,
                "desc": category.desc,
                "order": category.order
            }
            for category in categories
        ]
    }

@router.get("/priorities", summary="获取工单优先级列表", dependencies=[DependPermisson])
async def get_ticket_priorities(
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取工单优先级列表"""
    priorities = await TicketPriority.all().order_by('level')
    return {
        "data": [
            {
                "id": priority.id,
                "name": priority.name,
                "level": priority.level,
                "color": priority.color,
                "sla_hours": priority.sla_hours
            }
            for priority in priorities
        ]
    }

@router.get("/statuses", summary="获取工单状态列表", dependencies=[DependPermisson])
async def get_ticket_statuses(
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取工单状态列表"""
    statuses = await TicketStatus.all().order_by('order')
    return {
        "data": [
            {
                "id": status.id,
                "name": status.name,
                "code": status.code,
                "color": status.color,
                "is_final": status.is_final,
                "order": status.order
            }
            for status in statuses
        ]
    }

@router.get("/service-catalogs", summary="获取服务目录列表", dependencies=[DependPermisson])
async def get_service_catalogs(
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取服务目录列表"""
    services = await ServiceCatalog.filter(is_active=True).select_related('category_id', 'sla_priority_id')
    return {
        "data": [
            {
                "id": service.id,
                "name": service.name,
                "desc": service.desc,
                "category_id": service.category_id.id if hasattr(service.category_id, 'id') else service.category_id,
                "sla_priority_id": service.sla_priority_id.id if hasattr(service.sla_priority_id, 'id') else service.sla_priority_id,
                "is_active": service.is_active
            }
            for service in services
        ]
    }

@router.get("/stats", summary="获取工单统计数据", dependencies=[DependPermisson])
async def get_ticket_stats(
    current_user: User = Depends(AuthControl.is_authed)
):
    """获取工单统计数据"""
    from app.models.ticket import Ticket
    
    # 统计总数
    total = await Ticket.all().count()
    
    # 按状态统计（简化实现，实际应该根据状态代码统计）
    pending = await Ticket.all().count() // 4  # 示例数据
    processing = await Ticket.all().count() // 4
    resolved = await Ticket.all().count() // 4
    closed = await Ticket.all().count() // 4
    
    return {
        "data": {
            "total": total,
            "pending": pending,
            "processing": processing,
            "resolved": resolved,
            "closed": closed
        }
    } 