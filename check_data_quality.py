import asyncio
import sys
import json
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from tortoise import Tortoise

async def check_data_quality():
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    # 查询最新的5个Zabbix配置项
    print('=== 最新5个Zabbix配置项详细信息 ===')
    zabbix_items = await ConfigurationItem.filter(data_source='zabbix').order_by('-created_at').limit(5)
    
    for i, item in enumerate(zabbix_items, 1):
        print(f'{i}. Asset Tag: {item.asset_tag}')
        print(f'   Name: {item.name}')
        print(f'   Hostname: {item.hostname}')
        print(f'   IP Addresses: {item.ip_addresses}')
        print(f'   Operating System: {item.operating_system}')
        print(f'   External ID: {item.external_id}')
        print(f'   Custom Attributes Keys: {list(item.custom_attributes.keys()) if item.custom_attributes else "None"}')
        if item.custom_attributes and 'zabbix_host_uuid' in item.custom_attributes:
            print(f'   Zabbix Host UUID: {item.custom_attributes["zabbix_host_uuid"]}')
        if item.custom_attributes and 'interfaces' in item.custom_attributes:
            print(f'   Interfaces Count: {len(item.custom_attributes["interfaces"])}')
        print()
    
    # 查询最新的5个Prometheus配置项
    print('=== 最新5个Prometheus配置项详细信息 ===')
    prometheus_items = await ConfigurationItem.filter(data_source='prometheus').order_by('-created_at').limit(5)
    
    for i, item in enumerate(prometheus_items, 1):
        print(f'{i}. Asset Tag: {item.asset_tag}')
        print(f'   Name: {item.name}')
        print(f'   Hostname: {item.hostname}')
        print(f'   IP Addresses: {item.ip_addresses}')
        print(f'   Service Port: {item.service_port}')
        print(f'   External ID: {item.external_id}')
        print(f'   Custom Attributes Keys: {list(item.custom_attributes.keys()) if item.custom_attributes else "None"}')
        print()
    
    # 统计空字段情况
    print('=== 数据质量统计 ===')
    
    # Zabbix数据统计
    total_zabbix = await ConfigurationItem.filter(data_source='zabbix').count()
    zabbix_empty_os = await ConfigurationItem.filter(data_source='zabbix', operating_system__isnull=True).count()
    zabbix_empty_external_id = await ConfigurationItem.filter(data_source='zabbix', external_id__isnull=True).count()
    zabbix_empty_hostname = await ConfigurationItem.filter(data_source='zabbix', hostname__isnull=True).count()
    
    print(f'Zabbix配置项总数: {total_zabbix}')
    print(f'  - 操作系统为空: {zabbix_empty_os} ({zabbix_empty_os/total_zabbix*100:.1f}%)')
    print(f'  - 外部ID为空: {zabbix_empty_external_id} ({zabbix_empty_external_id/total_zabbix*100:.1f}%)')
    print(f'  - 主机名为空: {zabbix_empty_hostname} ({zabbix_empty_hostname/total_zabbix*100:.1f}%)')
    
    # Prometheus数据统计
    total_prometheus = await ConfigurationItem.filter(data_source='prometheus').count()
    prometheus_empty_external_id = await ConfigurationItem.filter(data_source='prometheus', external_id__isnull=True).count()
    prometheus_empty_hostname = await ConfigurationItem.filter(data_source='prometheus', hostname__isnull=True).count()
    
    print(f'\nPrometheus配置项总数: {total_prometheus}')
    print(f'  - 外部ID为空: {prometheus_empty_external_id} ({prometheus_empty_external_id/total_prometheus*100:.1f}%)')
    print(f'  - 主机名为空: {prometheus_empty_hostname} ({prometheus_empty_hostname/total_prometheus*100:.1f}%)')
    
    # 检查IP地址分布
    print('\n=== IP地址分布情况 ===')
    all_zabbix_items = await ConfigurationItem.filter(data_source='zabbix').all()
    ip_count = {}
    for item in all_zabbix_items:
        ip_str = str(item.ip_addresses)
        ip_count[ip_str] = ip_count.get(ip_str, 0) + 1
    
    print('Zabbix IP地址分布 (前10个):')
    for ip, count in sorted(ip_count.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f'  {ip}: {count} 个配置项')
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(check_data_quality())
