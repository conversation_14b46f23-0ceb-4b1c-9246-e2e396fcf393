#!/usr/bin/env python3
"""
测试运行脚本
用于运行所有工单相关的测试
"""
import sys
import subprocess
import asyncio
from pathlib import Path


def run_tests():
    """运行测试"""
    print("🚀 开始运行工单接口测试...")
    print("📊 注意：现在使用真实数据库，测试数据会自动清理")
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    
    # 运行pytest
    cmd = [
        sys.executable, "-m", "pytest",
        "app/tests/test_tickets.py",
        "-v",
        "--tb=short",
        "--color=yes",
        "--asyncio-mode=auto"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True)
        print("-" * 60)
        print("✅ 所有测试通过!")
        return True
    except subprocess.CalledProcessError as e:
        print("-" * 60)
        print(f"❌ 测试失败，退出代码: {e.returncode}")
        return False


def run_specific_test(test_name: str):
    """运行特定的测试"""
    print(f"🎯 运行特定测试: {test_name}")
    
    project_root = Path(__file__).parent.parent.parent
    
    cmd = [
        sys.executable, "-m", "pytest",
        f"app/tests/test_tickets.py::{test_name}",
        "-v",
        "--tb=short",
        "--color=yes",
        "--asyncio-mode=auto"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ 测试通过!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试失败，退出代码: {e.returncode}")
        return False


def manage_test_data():
    """运行测试数据管理器"""
    print("🔧 启动测试数据管理器...")
    
    project_root = Path(__file__).parent.parent.parent
    
    cmd = [
        sys.executable, "app/tests/test_data_generator.py"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 数据管理器运行失败，退出代码: {e.returncode}")
        return False


def run_with_coverage():
    """运行测试并生成覆盖率报告"""
    print("📊 运行测试并生成覆盖率报告...")
    print("注意：需要安装 pytest-cov: pip install pytest-cov")
    
    project_root = Path(__file__).parent.parent.parent
    
    cmd = [
        sys.executable, "-m", "pytest",
        "app/tests/test_tickets.py",
        "--cov=app.api.v1.tickets",
        "--cov-report=html",
        "--cov-report=term",
        "-v",
        "--asyncio-mode=auto"
    ]
    
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True)
        print("✅ 测试完成，覆盖率报告已生成在 htmlcov/ 目录")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试失败，退出代码: {e.returncode}")
        return False


def clean_test_data():
    """快速清理测试数据"""
    print("🧹 清理测试数据...")
    
    # 导入必要的模块
    sys.path.append(str(Path(__file__).parent.parent.parent))
    
    async def cleanup():
        from tortoise import Tortoise
        from app.settings.config import settings
        from app.tests.test_data_generator import TestDataGenerator
        
        await Tortoise.init(config=settings.TORTOISE_ORM)
        await TestDataGenerator.cleanup_test_dataset()
        await Tortoise.close_connections()
    
    try:
        asyncio.run(cleanup())
        print("✅ 测试数据清理完成!")
        return True
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False


def show_help():
    """显示帮助信息"""
    print("""
工单接口测试运行器

用法:
    python app/tests/run_tests.py [选项]

选项:
    无参数          - 运行所有工单测试
    --help         - 显示此帮助信息
    --list         - 列出所有可用的测试
    --test <name>  - 运行特定的测试类
    --coverage     - 运行测试并生成覆盖率报告
    --data         - 管理测试数据（创建/清理/重建）
    --clean        - 快速清理测试数据

数据库说明:
    🏠 现在使用真实的MySQL数据库进行测试
    🏷️ 所有测试数据都有 "TEST_" 前缀标识
    🧹 测试结束后会自动清理测试数据
    💾 不会影响现有的正常业务数据

示例:
    python app/tests/run_tests.py
    python app/tests/run_tests.py --test TestTicketsList
    python app/tests/run_tests.py --coverage
    python app/tests/run_tests.py --data
    python app/tests/run_tests.py --clean
    """)


def list_tests():
    """列出所有测试"""
    print("📋 可用的测试类:")
    print("  - TestTicketsList      # 工单列表接口测试")
    print("  - TestCreateTicket     # 创建工单接口测试")
    print("  - TestGetTicketDetail  # 工单详情接口测试")
    print("  - TestUpdateTicket     # 更新工单接口测试")
    print("  - TestDeleteTicket     # 删除工单接口测试")
    print("  - TestTicketsIntegration # 集成测试")
    print()
    print("📊 数据库信息:")
    print("  - 使用真实MySQL数据库")
    print("  - 测试数据前缀: TEST_")
    print("  - 自动数据清理机制")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            show_help()
        elif sys.argv[1] == "--list":
            list_tests()
        elif sys.argv[1] == "--test" and len(sys.argv) > 2:
            run_specific_test(sys.argv[2])
        elif sys.argv[1] == "--coverage":
            run_with_coverage()
        elif sys.argv[1] == "--data":
            manage_test_data()
        elif sys.argv[1] == "--clean":
            clean_test_data()
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
            sys.exit(1)
    else:
        success = run_tests()
        sys.exit(0 if success else 1) 