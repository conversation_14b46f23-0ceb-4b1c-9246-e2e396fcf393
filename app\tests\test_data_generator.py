"""
测试数据生成器
用于生成各种测试场景需要的数据
"""
import sys
import os
import asyncio
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.models.admin import User, Role
from app.models.ticket import (
    Ticket, TicketCategory, TicketPriority, 
    TicketStatus, ServiceCatalog
)
from app.settings.config import settings

# 测试数据前缀
TEST_DATA_PREFIX = "TEST_DATA_"


class TestDataGenerator:
    """测试数据生成器"""
    
    @staticmethod
    async def create_users(count: int = 5) -> List[User]:
        """创建多个测试用户"""
        users = []
        for i in range(count):
            user = await User.create(
                username=f"{TEST_DATA_PREFIX.lower()}user{i+1}",
                email=f"testdata{i+1}@example.com",
                password="hashedpassword",
                is_active=True,
                is_superuser=(i == 0)  # 第一个用户为管理员
            )
            users.append(user)
        return users
    
    @staticmethod
    async def create_ticket_categories(count: int = 3) -> List[TicketCategory]:
        """创建工单分类"""
        categories_data = [
            {"name": "技术支持", "desc": "技术问题和故障处理", "order": 1001},
            {"name": "服务请求", "desc": "各类服务申请", "order": 1002},
            {"name": "变更管理", "desc": "系统变更和升级", "order": 1003},
        ]
        
        categories = []
        for i, data in enumerate(categories_data[:count]):
            category = await TicketCategory.create(
                name=f"{TEST_DATA_PREFIX}{data['name']}",
                desc=data["desc"],
                is_active=True,
                order=data["order"]
            )
            categories.append(category)
        return categories
    
    @staticmethod
    async def create_ticket_priorities(count: int = 4) -> List[TicketPriority]:
        """创建工单优先级"""
        priorities_data = [
            {"name": "紧急", "level": 1001, "color": "#ff0000", "sla_hours": 4},
            {"name": "高", "level": 1002, "color": "#ff8c00", "sla_hours": 8},
            {"name": "中", "level": 1003, "color": "#ffd700", "sla_hours": 24},
            {"name": "低", "level": 1004, "color": "#32cd32", "sla_hours": 72},
        ]
        
        priorities = []
        for i, data in enumerate(priorities_data[:count]):
            priority = await TicketPriority.create(
                name=f"{TEST_DATA_PREFIX}{data['name']}",
                level=data["level"],
                color=data["color"],
                sla_hours=data["sla_hours"]
            )
            priorities.append(priority)
        return priorities
    
    @staticmethod
    async def create_ticket_statuses(count: int = 5) -> List[TicketStatus]:
        """创建工单状态"""
        statuses_data = [
            {"name": "待处理", "code": "pending", "color": "#ffa500", "is_final": False, "order": 1001},
            {"name": "处理中", "code": "in_progress", "color": "#1e90ff", "is_final": False, "order": 1002},
            {"name": "等待回复", "code": "waiting", "color": "#9370db", "is_final": False, "order": 1003},
            {"name": "已解决", "code": "resolved", "color": "#32cd32", "is_final": True, "order": 1004},
            {"name": "已关闭", "code": "closed", "color": "#808080", "is_final": True, "order": 1005},
        ]
        
        statuses = []
        for i, data in enumerate(statuses_data[:count]):
            status = await TicketStatus.create(
                name=f"{TEST_DATA_PREFIX}{data['name']}",
                code=f"{TEST_DATA_PREFIX.lower()}{data['code']}",
                color=data["color"],
                is_final=data["is_final"],
                order=data["order"]
            )
            statuses.append(status)
        return statuses
    
    @staticmethod
    async def create_service_catalogs(
        categories: List[TicketCategory], 
        priorities: List[TicketPriority], 
        count: int = 5
    ) -> List[ServiceCatalog]:
        """创建服务目录"""
        services_data = [
            {"name": "服务器维护", "desc": "服务器硬件和软件维护服务"},
            {"name": "网络故障", "desc": "网络连接和配置问题处理"},
            {"name": "软件安装", "desc": "各类软件安装和配置服务"},
            {"name": "数据库服务", "desc": "数据库维护和优化服务"},
            {"name": "安全服务", "desc": "系统安全检查和防护服务"},
        ]
        
        services = []
        for i, data in enumerate(services_data[:count]):
            service = await ServiceCatalog.create(
                name=f"{TEST_DATA_PREFIX}{data['name']}",
                desc=data["desc"],
                category_id=categories[i % len(categories)].id,
                sla_priority_id=priorities[i % len(priorities)].id,
                is_active=True
            )
            services.append(service)
        return services
    
    @staticmethod
    async def create_tickets(
        users: List[User],
        categories: List[TicketCategory],
        priorities: List[TicketPriority],
        statuses: List[TicketStatus],
        services: List[ServiceCatalog],
        count: int = 10
    ) -> List[Ticket]:
        """创建测试工单"""
        tickets_data = [
            {"title": "服务器CPU使用率过高", "description": "生产服务器CPU持续100%使用率，需要紧急处理"},
            {"title": "网络连接不稳定", "description": "办公网络经常断线，影响正常工作"},
            {"title": "邮件系统故障", "description": "邮件服务器无法发送邮件，收件正常"},
            {"title": "数据库性能优化", "description": "数据库查询速度较慢，需要优化"},
            {"title": "新员工账号申请", "description": "为新入职员工申请系统访问账号"},
            {"title": "软件许可证到期", "description": "办公软件许可证即将到期，需要续费"},
            {"title": "安全漏洞修复", "description": "系统检测到安全漏洞，需要及时修复"},
            {"title": "备份系统检查", "description": "定期检查数据备份系统运行状态"},
            {"title": "VPN连接问题", "description": "远程办公VPN无法正常连接"},
            {"title": "打印机维护", "description": "办公室打印机需要定期维护保养"},
        ]
        
        tickets = []
        for i in range(min(count, len(tickets_data))):
            data = tickets_data[i]
            ticket = await Ticket.create(
                ticket_no=f"{TEST_DATA_PREFIX}{202501000 + i + 1}",
                title=f"{TEST_DATA_PREFIX}{data['title']}",
                description=data["description"],
                category_id=categories[i % len(categories)].id,
                service_id=services[i % len(services)].id,
                priority_id=priorities[i % len(priorities)].id,
                status_id=statuses[i % len(statuses)].id,
                creator_id=users[i % len(users)].id,
                assignee_id=users[(i + 1) % len(users)].id if len(users) > 1 else users[0].id
            )
            tickets.append(ticket)
        return tickets
    
    @classmethod
    async def create_full_test_dataset(cls) -> Dict[str, Any]:
        """创建完整的测试数据集"""
        print("开始创建测试数据...")
        
        # 创建用户
        users = await cls.create_users(5)
        print(f"创建了 {len(users)} 个用户")
        
        # 创建工单分类
        categories = await cls.create_ticket_categories(3)
        print(f"创建了 {len(categories)} 个工单分类")
        
        # 创建优先级
        priorities = await cls.create_ticket_priorities(4)
        print(f"创建了 {len(priorities)} 个优先级")
        
        # 创建状态
        statuses = await cls.create_ticket_statuses(5)
        print(f"创建了 {len(statuses)} 个状态")
        
        # 创建服务目录
        services = await cls.create_service_catalogs(categories, priorities, 5)
        print(f"创建了 {len(services)} 个服务目录")
        
        # 创建工单
        tickets = await cls.create_tickets(users, categories, priorities, statuses, services, 10)
        print(f"创建了 {len(tickets)} 个工单")
        
        print("测试数据创建完成！")
        
        return {
            "users": users,
            "categories": categories,
            "priorities": priorities,
            "statuses": statuses,
            "services": services,
            "tickets": tickets
        }
    
    @classmethod
    async def cleanup_test_dataset(cls):
        """清理所有测试数据"""
        print("开始清理测试数据...")
        
        try:
            # 删除测试工单（包含外键关系，需要先删除）
            deleted_tickets = await Ticket.filter(ticket_no__startswith=TEST_DATA_PREFIX).delete()
            print(f"删除了 {deleted_tickets} 个测试工单")
            
            # 删除测试服务目录
            deleted_services = await ServiceCatalog.filter(name__startswith=TEST_DATA_PREFIX).delete()
            print(f"删除了 {deleted_services} 个测试服务目录")
            
            # 删除测试工单分类、优先级、状态
            deleted_categories = await TicketCategory.filter(name__startswith=TEST_DATA_PREFIX).delete()
            deleted_priorities = await TicketPriority.filter(name__startswith=TEST_DATA_PREFIX).delete()
            deleted_statuses = await TicketStatus.filter(name__startswith=TEST_DATA_PREFIX).delete()
            print(f"删除了 {deleted_categories} 个分类, {deleted_priorities} 个优先级, {deleted_statuses} 个状态")
            
            # 删除测试用户（最后删除，因为其他表可能引用）
            deleted_users = await User.filter(username__startswith=TEST_DATA_PREFIX.lower()).delete()
            print(f"删除了 {deleted_users} 个测试用户")
            
            print("测试数据清理完成！")
            
        except Exception as e:
            print(f"清理测试数据时出错: {e}")


async def main():
    """运行数据生成器（用于开发测试）"""
    from tortoise import Tortoise
    
    # 初始化数据库
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 工单测试数据生成器 ===")
    print("1. 创建测试数据")
    print("2. 清理测试数据")
    print("3. 重新创建测试数据")
    
    choice = input("请选择操作 (1/2/3): ").strip()
    
    if choice == "1":
        # 创建测试数据
        dataset = await TestDataGenerator.create_full_test_dataset()
        print("\n=== 数据统计 ===")
        for key, value in dataset.items():
            print(f"{key}: {len(value)} 条记录")
    
    elif choice == "2":
        # 清理测试数据
        await TestDataGenerator.cleanup_test_dataset()
    
    elif choice == "3":
        # 先清理再创建
        await TestDataGenerator.cleanup_test_dataset()
        print()
        dataset = await TestDataGenerator.create_full_test_dataset()
        print("\n=== 数据统计 ===")
        for key, value in dataset.items():
            print(f"{key}: {len(value)} 条记录")
    
    else:
        print("无效选择")
    
    await Tortoise.close_connections()


if __name__ == "__main__":
    asyncio.run(main()) 