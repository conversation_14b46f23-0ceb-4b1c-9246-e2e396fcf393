import asyncio
import json
import sys
import os

# 将父目录添加到sys.path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.models.admin import Menu, Api

async def check_menus():
    """检查菜单配置"""
    # 查询所有菜单
    menus = await Menu.all()
    
    print("菜单列表:")
    for menu in menus:
        print(f"ID: {menu.id}, 名称: {menu.name}, 路径: {menu.path}, 组件: {menu.component}, "
              f"类型: {menu.menu_type}, 父级ID: {menu.parent_id}")
    
    # 查找工单相关菜单
    ticket_menus = await Menu.filter(name__contains="工单").all()
    print("\n工单相关菜单:")
    for menu in ticket_menus:
        print(f"ID: {menu.id}, 名称: {menu.name}, 路径: {menu.path}, 组件: {menu.component}, "
              f"类型: {menu.menu_type}, 父级ID: {menu.parent_id}")

async def check_apis():
    """检查API配置"""
    # 查询所有API
    apis = await Api.all()
    
    print("\nAPI列表:")
    for api in apis:
        print(f"ID: {api.id}, 路径: {api.path}, 方法: {api.method}, 名称: {api.name}")
    
    # 查找工单相关API
    ticket_apis = await Api.filter(path__contains="tickets").all()
    print("\n工单相关API:")
    for api in ticket_apis:
        print(f"ID: {api.id}, 路径: {api.path}, 方法: {api.method}, 名称: {api.name}")

async def main():
    from tortoise import Tortoise
    try:
        from app.settings.config import settings
    except ImportError:
        print("无法导入配置")
        return
    
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    await check_menus()
    await check_apis()
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(main()) 