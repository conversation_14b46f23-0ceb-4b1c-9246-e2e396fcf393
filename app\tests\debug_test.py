#!/usr/bin/env python3
import asyncio
import sys
import time
from pathlib import Path

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

async def test():
    from app.models.ticket import TicketCategory, TicketPriority, ServiceCatalog
    from app.models.admin import User
    from app.settings.config import settings
    from tortoise import Tortoise
    
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        # 先清理测试数据
        await TicketCategory.filter(name__startswith='TEST_').delete()
        await TicketPriority.filter(name__startswith='TEST_').delete()
        await ServiceCatalog.filter(name__startswith='TEST_').delete()
        print("清理完成")
        
        timestamp = int(time.time())
        
        print("创建分类...")
        category = await TicketCategory.create(
            name=f'TEST_简单测试{timestamp}',
            desc='测试分类',
            is_active=True,
            order=9999
        )
        print(f'Category created: {category.id}, type: {type(category.id)}')
        
        print("创建优先级...")
        priority = await TicketPriority.create(
            name=f'TEST_简单优先级{timestamp}',
            level=9999,
            color='#ff0000',
            sla_hours=24
        )
        print(f'Priority created: {priority.id}, type: {type(priority.id)}')
        
        print(f"准备创建服务目录，category_id={category.id}, sla_priority_id={priority.id}")
        
        # 尝试先创建空对象，然后设置属性
        service = ServiceCatalog()
        service.name = f'TEST_服务器维护{timestamp}'
        service.desc = '服务器相关维护服务'
        service.is_active = True
        # 尝试使用外键对象而不是ID
        service.category_id = category
        service.sla_priority_id = priority
        await service.save()
        print(f'Service created: {service.id}')
        
        # 清理创建的数据
        await ServiceCatalog.filter(id=service.id).delete()
        await TicketPriority.filter(id=priority.id).delete()
        await TicketCategory.filter(id=category.id).delete()
        print("清理创建的测试数据完成")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test()) 