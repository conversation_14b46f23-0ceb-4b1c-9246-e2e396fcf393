import asyncio
import sys
import json
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from tortoise import Tortoise

async def debug_existing_data():
    """调试现有数据结构"""
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 调试现有数据结构 ===")
    
    # 获取一个Zabbix配置项样本
    zabbix_sample = await ConfigurationItem.filter(data_source='zabbix').first()
    if zabbix_sample:
        print("Zabbix样本数据:")
        print(f"  Asset Tag: {zabbix_sample.asset_tag}")
        print(f"  External ID: {zabbix_sample.external_id}")
        print(f"  Hostname: {zabbix_sample.hostname}")
        print(f"  IP Addresses: {zabbix_sample.ip_addresses}")
        print(f"  Operating System: {zabbix_sample.operating_system}")
        print(f"  Custom Attributes Keys: {list(zabbix_sample.custom_attributes.keys()) if zabbix_sample.custom_attributes else 'None'}")
        
        if zabbix_sample.custom_attributes:
            print("  Custom Attributes详情:")
            for key, value in zabbix_sample.custom_attributes.items():
                if key == 'inventory':
                    print(f"    {key}: {json.dumps(value, indent=6, ensure_ascii=False)}")
                elif key == 'interfaces':
                    print(f"    {key}: {json.dumps(value, indent=6, ensure_ascii=False)}")
                else:
                    print(f"    {key}: {value}")
        print()
    
    # 获取一个Prometheus配置项样本
    prometheus_sample = await ConfigurationItem.filter(data_source='prometheus').first()
    if prometheus_sample:
        print("Prometheus样本数据:")
        print(f"  Asset Tag: {prometheus_sample.asset_tag}")
        print(f"  External ID: {prometheus_sample.external_id}")
        print(f"  Hostname: {prometheus_sample.hostname}")
        print(f"  IP Addresses: {prometheus_sample.ip_addresses}")
        print(f"  Service Name: {prometheus_sample.service_name}")
        print(f"  Custom Attributes Keys: {list(prometheus_sample.custom_attributes.keys()) if prometheus_sample.custom_attributes else 'None'}")
        
        if prometheus_sample.custom_attributes:
            print("  Custom Attributes详情:")
            for key, value in prometheus_sample.custom_attributes.items():
                print(f"    {key}: {value}")
        print()
    
    await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(debug_existing_data())
