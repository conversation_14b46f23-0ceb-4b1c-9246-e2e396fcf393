// 用于手动清理前端缓存的代码片段
// 可以在前端控制台中执行

// 清除localStorage中的缓存
function clearFrontendCache() {
  console.log('清理前端缓存...');
  
  // 清除localStorage中存储的路由和权限相关缓存
  const keysToRemove = [
    'token',
    'vue-admin-permission-routes',
    'vue-admin-access-routes',
    'vue-admin-access-apis',
    'vue-admin-menus'
  ];
  
  let removedCount = 0;
  keysToRemove.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log(`已删除缓存: ${key}`);
      removedCount++;
    }
  });
  
  console.log(`共清理了 ${removedCount} 项缓存数据`);
  console.log('缓存清理完成! 请刷新页面后重新登录.');
}

// 执行清理
clearFrontendCache();

// 使用说明:
// 1. 打开浏览器开发者工具(F12)
// 2. 切换到控制台(Console)选项卡
// 3. 复制此文件的全部内容并粘贴到控制台中
// 4. 按Enter键执行
// 5. 刷新页面并重新登录 