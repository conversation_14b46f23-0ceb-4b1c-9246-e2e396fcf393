#!/usr/bin/env python3
"""
调试配置项创建过程中的数据类型问题
Debug CI creation data type issues
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from tortoise import Tortoise
from loguru import logger
from datetime import datetime
import json

from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector
from app.discovery.engine import CMDBDiscoveryEngine

def serialize_for_debug(obj):
    """序列化对象用于调试显示"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {k: serialize_for_debug(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [serialize_for_debug(item) for item in obj]
    else:
        return obj

async def debug_ci_creation():
    """调试配置项创建过程"""
    try:
        # 加载环境配置
        logger.info("加载环境配置...")
        init_environment()
        
        # 初始化数据库连接
        logger.info("初始化数据库连接...")
        await Tortoise.init(
            db_url="mysql://root:123456@10.253.96.58:3306/fastapi_user",
            modules={"models": ["app.models"]}
        )
        
        # 测试Prometheus连接器
        logger.info("测试Prometheus连接器...")
        prometheus_config = {
            "url": "http://10.253.96.111:31891",
            "timeout": 30
        }
        prometheus_connector = PrometheusDiscoveryConnector(prometheus_config)
        
        # 获取一些目标数据
        targets = await prometheus_connector.discover_targets()
        if not targets:
            logger.warning("没有发现任何目标")
            return
            
        logger.info(f"发现 {len(targets)} 个目标")
        
        # 取第一个目标进行详细调试
        target = targets[0]
        logger.info("目标数据结构:")
        logger.info(f"类型: {type(target)}")
        logger.info(f"scrape_url: {target.scrape_url}")
        logger.info(f"instance: {target.instance}")
        logger.info(f"job: {target.job}")
        logger.info(f"labels: {target.labels}")
        logger.info(f"health: {target.health}")
        
        # 模拟发现引擎的映射过程
        logger.info("\n=== 开始映射过程调试 ===")
        
        # 使用数据映射器
        from app.discovery.connectors.prometheus_connector import PrometheusDataMapper
        target_dict = PrometheusDataMapper.map_target_to_ci(target)
        
        logger.info("映射后的目标数据:")
        logger.info(json.dumps(serialize_for_debug(target_dict), indent=2, ensure_ascii=False))
        
        # 检查每个字段的类型
        for field_name, field_value in target_dict.items():
            logger.info(f"字段 {field_name}: 类型={type(field_value)}, 值={field_value}")
            
        # 尝试创建配置项数据
        logger.info("\n=== 准备创建配置项数据 ===")
        
        ci_data = {
            "asset_tag": target_dict.get("asset_tag", ""),
            "name": target_dict.get("name", ""),
            "ip_address": target_dict.get("ip_address", ""),
            "hostname": target_dict.get("hostname", ""),
            "ci_type": target_dict.get("ci_type", "SERVER"),
            "status": target_dict.get("status", "ACTIVE"),
            "location": target_dict.get("location", ""),
            "description": target_dict.get("description", ""),
            "properties": target_dict.get("properties", {}),
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        
        logger.info("准备保存的CI数据:")
        logger.info(json.dumps(serialize_for_debug(ci_data), indent=2, ensure_ascii=False))
        
        # 检查每个字段的类型
        logger.info("\n=== CI数据字段类型检查 ===")
        for field_name, field_value in ci_data.items():
            logger.info(f"CI字段 {field_name}: 类型={type(field_value)}, 值={field_value}")
            
        # 尝试创建配置项
        logger.info("\n=== 尝试创建配置项 ===")
        try:
            # 先检查是否已存在
            existing_ci = await ConfigurationItem.filter(asset_tag=ci_data["asset_tag"]).first()
            if existing_ci:
                logger.info(f"配置项已存在: {existing_ci.asset_tag}")
                return
                
            # 创建新的配置项
            ci = await ConfigurationItem.create(**ci_data)
            logger.success(f"成功创建配置项: {ci.asset_tag}")
            
        except Exception as create_error:
            logger.error(f"创建配置项失败: {create_error}")
            logger.error(f"错误类型: {type(create_error)}")
            
            # 逐个字段测试
            logger.info("\n=== 逐个字段测试 ===")
            for field_name, field_value in ci_data.items():
                try:
                    test_data = {"asset_tag": f"test_{field_name}"}
                    test_data[field_name] = field_value
                    
                    logger.info(f"测试字段 {field_name}: {test_data}")
                    # 不实际创建，只是验证数据
                    
                except Exception as field_error:
                    logger.error(f"字段 {field_name} 有问题: {field_error}")
        
    except Exception as e:
        logger.error(f"调试过程中发生错误: {e}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(debug_ci_creation()) 