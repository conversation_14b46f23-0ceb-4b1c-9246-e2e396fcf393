#!/usr/bin/env python3
"""
简化的工单接口测试
避免复杂的pytest配置问题
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from httpx import AsyncClient, ASGITransport
from tortoise import Tortoise, connections
from app import app
from app.models.admin import User
from app.models.ticket import (
    Ticket, TicketCategory, TicketPriority, 
    TicketStatus, ServiceCatalog
)
from app.settings.config import settings

# 测试数据前缀
TEST_DATA_PREFIX = "TEST_"


async def cleanup_test_data():
    """清理所有测试数据"""
    try:
        print("清理测试数据...")
        await Ticket.filter(ticket_no__startswith=TEST_DATA_PREFIX).delete()
        await ServiceCatalog.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await TicketCategory.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await TicketPriority.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await TicketStatus.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await User.filter(username__startswith=TEST_DATA_PREFIX.lower()).delete()
        print("测试数据清理完成")
    except Exception as e:
        print(f"清理测试数据时出错: {e}")


async def setup_test_data():
    """创建测试数据"""
    print("创建测试数据...")
    
    # 创建测试用户
    user = await User.create(
        username=f"{TEST_DATA_PREFIX.lower()}user",
        email="<EMAIL>",
        password="hashedpassword",
        is_active=True,
        is_superuser=False
    )
    
    admin = await User.create(
        username=f"{TEST_DATA_PREFIX.lower()}admin",
        email="<EMAIL>", 
        password="hashedpassword",
        is_active=True,
        is_superuser=True
    )
    
    # 创建工单分类
    category = await TicketCategory.create(
        name=f"{TEST_DATA_PREFIX}技术支持",
        desc="技术问题和故障处理",
        is_active=True,
        order=999
    )
    
    # 创建优先级
    priority = await TicketPriority.create(
        name=f"{TEST_DATA_PREFIX}高优先级",
        level=999,
        color="#ff0000",
        sla_hours=24
    )
    
    # 创建状态
    status = await TicketStatus.create(
        name=f"{TEST_DATA_PREFIX}待处理",
        code=f"{TEST_DATA_PREFIX.lower()}pending",
        color="#ffa500",
        is_final=False,
        order=999
    )
    
    # 重新加载对象以确保正确保存
    category = await TicketCategory.get(id=category.id)
    priority = await TicketPriority.get(id=priority.id)
    
    # 创建服务目录
    service = ServiceCatalog()
    service.name = f"{TEST_DATA_PREFIX}服务器维护"
    service.desc = "服务器相关维护服务"
    service.is_active = True
    service.category_id = category
    service.sla_priority_id = priority
    await service.save()
    
    # 创建测试工单
    ticket = Ticket()
    ticket.ticket_no = f"{TEST_DATA_PREFIX}202501001"
    ticket.title = f"{TEST_DATA_PREFIX}测试工单"
    ticket.description = "这是一个测试工单的详细描述"
    ticket.category_id = category
    ticket.service_id = service
    ticket.priority_id = priority
    ticket.status_id = status
    ticket.creator_id = user
    ticket.assignee_id = admin
    await ticket.save()
    
    print("测试数据创建完成")
    return {
        "user": user,
        "admin": admin,
        "category": category,
        "priority": priority,
        "status": status,
        "service": service,
        "ticket": ticket
    }


def get_auth_headers() -> dict:
    """获取认证头"""
    return {"token": "dev"}


async def test_get_tickets_list():
    """测试获取工单列表"""
    print("\n🧪 测试：获取工单列表")
    
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://127.0.0.1:9999") as client:
        response = await client.get(
            "/api/v1/tickets",
            headers=get_auth_headers()
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"总数: {data.get('total', 0)}")
            print(f"项目数: {len(data.get('items', []))}")
            print("✅ 测试通过")
        else:
            print(f"❌ 测试失败: {response.text}")
            return False
    
    return True


async def test_get_ticket_detail(ticket_id: int):
    """测试获取工单详情"""
    print(f"\n🧪 测试：获取工单详情 (ID: {ticket_id})")
    
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        response = await client.get(
            f"/api/v1/tickets/{ticket_id}",
            headers=get_auth_headers()
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"工单标题: {data.get('title')}")
            print(f"工单描述: {data.get('description')}")
            print("✅ 测试通过")
        else:
            print(f"❌ 测试失败: {response.text}")
            return False
    
    return True


async def test_create_ticket(test_data):
    """测试创建工单"""
    print("\n🧪 测试：创建工单")
    
    ticket_data = {
        "title": f"{TEST_DATA_PREFIX}新建测试工单",
        "description": "这是通过API创建的测试工单",
        "category_id": test_data["category"].id,
        "priority_id": test_data["priority"].id,
        "service_id": test_data["service"].id,
        "creator_id": test_data["user"].id,
        "status_id": test_data["status"].id
    }
    
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        response = await client.post(
            "/api/v1/tickets",
            json=ticket_data,
            headers=get_auth_headers()
        )
        
        print(f"状态码: {response.status_code}")
        if response.status_code == 201:
            data = response.json()
            print(f"创建的工单ID: {data.get('id')}")
            print(f"工单标题: {data.get('title')}")
            print("✅ 测试通过")
            
            # 清理创建的工单
            if data.get('id'):
                await Ticket.filter(id=data['id']).delete()
                print("已清理创建的测试工单")
        else:
            print(f"❌ 测试失败: {response.text}")
            return False
    
    return True


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行简化版工单接口测试")
    print("=" * 50)
    
    # 初始化数据库
    print("初始化数据库连接...")
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    try:
        # 清理旧数据
        await cleanup_test_data()
        
        # 创建测试数据
        test_data = await setup_test_data()
        
        # 运行测试
        results = []
        
        # 测试1：获取工单列表
        results.append(await test_get_tickets_list())
        
        # 测试2：获取工单详情
        results.append(await test_get_ticket_detail(test_data["ticket"].id))
        
        # 测试3：创建工单
        results.append(await test_create_ticket(test_data))
        
        # 清理测试数据
        await cleanup_test_data()
        
        # 统计结果
        passed = sum(results)
        total = len(results)
        
        print("\n" + "=" * 50)
        print(f"测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试都通过了！")
            return True
        else:
            print("❌ 部分测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 关闭数据库连接
        await connections.close_all()


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1) 