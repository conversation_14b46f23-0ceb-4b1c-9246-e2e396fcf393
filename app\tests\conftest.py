import asyncio
import pytest
import pytest_asyncio
from typing import AsyncGenerator, List
from fastapi.testclient import TestClient
from httpx import AsyncClient, ASGITransport
from tortoise import Tortoise, connections

from app import app
from app.models.admin import User, Role
from app.models.ticket import (
    Ticket, TicketCategory, TicketPriority, 
    TicketStatus, ServiceCatalog
)
from app.settings.config import settings


# 测试数据前缀，用于标识测试数据
TEST_DATA_PREFIX = "TEST_"


@pytest.fixture(scope="session")
def event_loop():
    """创建会话级事件循环"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_database():
    """设置数据库连接，整个测试会话共享"""
    # 初始化Tortoise ORM
    await Tortoise.init(config=settings.TORTOISE_ORM)
    await Tortoise.generate_schemas()
    yield
    # 清理资源
    await connections.close_all()


async def cleanup_test_data():
    """清理所有测试数据"""
    try:
        # 按顺序删除，避免外键约束问题
        await Ticket.filter(ticket_no__startswith=TEST_DATA_PREFIX).delete()
        await ServiceCatalog.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await TicketCategory.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await TicketPriority.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await TicketStatus.filter(name__startswith=TEST_DATA_PREFIX).delete()
        await User.filter(username__startswith=TEST_DATA_PREFIX.lower()).delete()
    except Exception as e:
        print(f"清理测试数据时出错: {e}")


@pytest_asyncio.fixture(autouse=True)
async def cleanup_data():
    """每个测试前后清理数据"""
    # 测试前清理
    await cleanup_test_data()
    yield
    # 测试后清理
    await cleanup_test_data()


@pytest_asyncio.fixture
async def client() -> AsyncGenerator[AsyncClient, None]:
    """创建测试客户端"""
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        yield ac


@pytest_asyncio.fixture
async def test_user() -> User:
    """创建测试用户"""
    user = await User.create(
        username=f"{TEST_DATA_PREFIX.lower()}user",
        email="<EMAIL>",
        password="hashedpassword",
        is_active=True,
        is_superuser=False
    )
    return user


@pytest_asyncio.fixture
async def admin_user() -> User:
    """创建管理员用户"""
    admin = await User.create(
        username=f"{TEST_DATA_PREFIX.lower()}admin",
        email="<EMAIL>", 
        password="hashedpassword",
        is_active=True,
        is_superuser=True
    )
    return admin


@pytest_asyncio.fixture
async def ticket_category() -> TicketCategory:
    """创建工单分类"""
    category = await TicketCategory.create(
        name=f"{TEST_DATA_PREFIX}技术支持",
        desc="技术问题和故障处理",
        is_active=True,
        order=999
    )
    return category


@pytest_asyncio.fixture
async def ticket_priority() -> TicketPriority:
    """创建工单优先级"""
    priority = await TicketPriority.create(
        name=f"{TEST_DATA_PREFIX}高优先级",
        level=999,
        color="#ff0000",
        sla_hours=24
    )
    return priority


@pytest_asyncio.fixture
async def ticket_status() -> TicketStatus:
    """创建工单状态"""
    status = await TicketStatus.create(
        name=f"{TEST_DATA_PREFIX}待处理",
        code=f"{TEST_DATA_PREFIX.lower()}pending",
        color="#ffa500",
        is_final=False,
        order=999
    )
    return status


@pytest_asyncio.fixture
async def service_catalog(ticket_category, ticket_priority) -> ServiceCatalog:
    """创建服务目录"""
    service = await ServiceCatalog.create(
        name=f"{TEST_DATA_PREFIX}服务器维护",
        desc="服务器相关维护服务",
        category_id=ticket_category.id,
        sla_priority_id=ticket_priority.id,
        is_active=True
    )
    return service


@pytest_asyncio.fixture
async def test_ticket(test_user, admin_user, ticket_category, ticket_priority, ticket_status, service_catalog) -> Ticket:
    """创建测试工单"""
    ticket = await Ticket.create(
        ticket_no=f"{TEST_DATA_PREFIX}202501001",
        title=f"{TEST_DATA_PREFIX}测试工单",
        description="这是一个测试工单的详细描述",
        category_id=ticket_category.id,
        service_id=service_catalog.id,
        priority_id=ticket_priority.id,
        status_id=ticket_status.id,
        creator_id=test_user.id,
        assignee_id=admin_user.id
    )
    return ticket


@pytest_asyncio.fixture
async def multiple_test_tickets(
    test_user, 
    admin_user, 
    ticket_category, 
    ticket_priority, 
    ticket_status, 
    service_catalog
) -> List[Ticket]:
    """创建多个测试工单，用于列表和搜索测试"""
    tickets = []
    test_data = [
        {"title": "测试工单1", "description": "第一个测试工单"},
        {"title": "测试工单2", "description": "第二个测试工单"},
        {"title": "测试工单3", "description": "第三个测试工单"},
    ]
    
    for i, data in enumerate(test_data):
        ticket = await Ticket.create(
            ticket_no=f"{TEST_DATA_PREFIX}202501{str(i+10).zfill(3)}",
            title=f"{TEST_DATA_PREFIX}{data['title']}",
            description=data["description"],
            category_id=ticket_category.id,
            service_id=service_catalog.id,
            priority_id=ticket_priority.id,
            status_id=ticket_status.id,
            creator_id=test_user.id,
            assignee_id=admin_user.id
        )
        tickets.append(ticket)
    
    return tickets


def get_auth_headers(user_id: int = 1) -> dict:
    """获取认证头"""
    return {"token": "dev"} 