import os
import typing

from pydantic_settings import BaseSettings

from .env_manager import init_environment, get_current_environment, is_production

# 初始化环境配置
init_environment()

class Settings(BaseSettings):
    """
    应用配置类，从环境变量加载所有配置
    
    配置优先级：
    1. 环境变量
    2. 环境配置文件（env.development 或 env.production）
    3. 代码中的默认值
    """
    
    # 应用基本信息
    VERSION: str = "0.1.0"
    
    # 从环境变量获取应用配置
    APP_TITLE: str = os.getenv("APP_TITLE", "自动化运维系统")
    PROJECT_NAME: str = os.getenv("PROJECT_NAME", "自动化运维系统")
    APP_DESCRIPTION: str = os.getenv("APP_DESCRIPTION", "Description")
    ENVIRONMENT: str = get_current_environment()

    # CORS 配置 - 从环境变量获取
    @property
    def CORS_ORIGINS(self) -> typing.List[str]:
        """获取CORS允许的源列表"""
        origins_str = os.getenv("CORS_ORIGINS", "*")
        if origins_str == "*":
            return ["*"]
        return [origin.strip() for origin in origins_str.split(",")]
    
    CORS_ALLOW_CREDENTIALS: bool = os.getenv("CORS_ALLOW_CREDENTIALS", "true").lower() == "true"
    CORS_ALLOW_METHODS: typing.List = ["*"]
    CORS_ALLOW_HEADERS: typing.List = ["*"]

    # 调试模式 - 从环境变量获取
    DEBUG: bool = os.getenv("DEBUG", "true").lower() == "true"

    # 路径配置
    PROJECT_ROOT: str = os.path.abspath(os.path.join(os.path.dirname(__file__), os.pardir))
    BASE_DIR: str = os.path.abspath(os.path.join(PROJECT_ROOT, os.pardir))
    LOGS_ROOT: str = os.path.join(BASE_DIR, "app/logs")
    
    # 安全配置 - 从环境变量获取
    SECRET_KEY: str = os.getenv("SECRET_KEY", "dev_secret_key_for_development_only")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "10080"))
    
    # 数据库配置 - 动态构建
    @property
    def TORTOISE_ORM(self) -> dict:
        """
        从环境变量构建 Tortoise ORM 配置
        
        Returns:
            dict: Tortoise ORM 配置字典
        """
        return {
            "connections": {
                "default": {
                    "engine": "tortoise.backends.mysql",
                    "credentials": {
                        "host": os.getenv("DB_HOST", "localhost"),
                        "port": int(os.getenv("DB_PORT", "3306")),
                        "user": os.getenv("DB_USER", "root"),
                        "password": os.getenv("DB_PASSWORD", ""),
                        "database": os.getenv("DB_NAME", "fastapi_user"),
                        "charset": os.getenv("DB_CHARSET", "utf8mb4")
                    }
                }
            },
            "apps": {
                "models": {
                    "models": ["app.models", "aerich.models"],
                    "default_connection": "default",
                }
            },
            "use_tz": False,
            "timezone": "Asia/Shanghai",
        }
    
    DATETIME_FORMAT: str = "%Y-%m-%d %H:%M:%S"

    # ========== CMDB发现配置 ==========
    
    # Prometheus配置 - 从环境变量获取
    PROMETHEUS_URL: str = os.getenv("PROMETHEUS_URL", "http://localhost:9090")
    PROMETHEUS_TIMEOUT: int = int(os.getenv("PROMETHEUS_TIMEOUT", "30"))
    PROMETHEUS_ENABLED_JOBS: typing.List[str] = [
        "node-exporter",
        "kubernetes-pods", 
        "kubernetes-nodes",
        "blackbox",
        "mysql-exporter",
        "redis-exporter"
    ]
    PROMETHEUS_EXCLUDED_JOBS: typing.List[str] = [
        "prometheus"
    ]
    PROMETHEUS_SYNC_INTERVAL: int = int(os.getenv("PROMETHEUS_SYNC_INTERVAL", "1800"))
    
    # Zabbix配置 - 从环境变量获取
    ZABBIX_URL: str = os.getenv("ZABBIX_URL", "http://localhost/zabbix/api_jsonrpc.php")
    ZABBIX_USERNAME: str = os.getenv("ZABBIX_USERNAME", "admin")
    ZABBIX_PASSWORD: str = os.getenv("ZABBIX_PASSWORD", "")
    ZABBIX_ENABLED_GROUPS: typing.List[str] = [
        "Linux servers",
        "Windows servers", 
        "Network equipment",
        "Virtual machines"
    ]
    ZABBIX_EXCLUDED_HOSTS: typing.List[str] = [
        "test-host",
        "demo-host"
    ]
    ZABBIX_SYNC_INTERVAL: int = int(os.getenv("ZABBIX_SYNC_INTERVAL", "3600"))
    
    # 发现引擎配置 - 从环境变量获取
    DISCOVERY_ENABLED: bool = os.getenv("DISCOVERY_ENABLED", "true").lower() == "true"
    DISCOVERY_MAX_CONCURRENT_JOBS: int = int(os.getenv("DISCOVERY_MAX_CONCURRENT_JOBS", "5"))
    DISCOVERY_JOB_TIMEOUT: int = int(os.getenv("DISCOVERY_JOB_TIMEOUT", "1800"))
    DISCOVERY_RETRY_ATTEMPTS: int = int(os.getenv("DISCOVERY_RETRY_ATTEMPTS", "3"))
    DISCOVERY_RETRY_DELAY: int = int(os.getenv("DISCOVERY_RETRY_DELAY", "60"))
    
    # Redis配置 (用于Celery任务队列) - 从环境变量获取
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", "")
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", "")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 如果没有单独设置 Celery URL，则使用 Redis URL
        if not self.CELERY_BROKER_URL:
            self.CELERY_BROKER_URL = self.REDIS_URL
        if not self.CELERY_RESULT_BACKEND:
            self.CELERY_RESULT_BACKEND = self.REDIS_URL
    
    # 数据源服务发现规则配置
    SERVICE_DISCOVERY_RULES: typing.Dict = {
        "kubernetes": {
            "job_pattern": "kubernetes",
            "name_template": "{job}-{namespace}",
            "ci_type": "CONTAINER_SERVICE"
        },
        "mysql": {
            "job_pattern": "mysql",
            "name_template": "MySQL-{instance}",
            "ci_type": "DATABASE_SERVICE"
        },
        "redis": {
            "job_pattern": "redis", 
            "name_template": "Redis-{instance}",
            "ci_type": "CACHE_SERVICE"
        },
        "node": {
            "job_pattern": "node",
            "name_template": "Node-{instance}",
            "ci_type": "PHYSICAL_SERVER"
        }
    }
    
    # CI数据映射配置
    CI_MAPPING_CONFIG: typing.Dict = {
        "zabbix": {
            "host_to_server": {
                "name": "host",
                "display_name": "name", 
                "ip_addresses": "interfaces[*].ip",
                "hostname": "host",
                "operating_system": "inventory.os",
                "os_version": "inventory.os_full",
                "hardware_cpu": "inventory.hardware",
                "hardware_memory": "inventory.memory",
                "location": "inventory.location",
                "serial_number": "inventory.serialno_a"
            }
        },
        "prometheus": {
            "target_to_service": {
                "name": "job + '-' + instance",
                "display_name": "labels.instance",
                "ip_addresses": "instance.split(':')[0]",
                "port": "instance.split(':')[1]",
                "job_name": "job",
                "service_labels": "labels"
            }
        }
    }
    
    # 同步策略配置
    SYNC_STRATEGY: typing.Dict = {
        "conflict_resolution": "source_priority",  # source_priority, timestamp, manual
        "batch_size": 100,
        "enable_incremental": True,
        "enable_auto_cleanup": False,  # 是否自动清理不存在的CI
        "cleanup_grace_period": 7200,  # 清理宽限期(秒)
        "change_detection_fields": [
            "name",
            "operational_status", 
            "ip_addresses",
            "operating_system",
            "hardware_specifications",
            "location_name"
        ]
    }
    
    # 监控和告警配置 - 从环境变量构建
    @property
    def DISCOVERY_MONITORING(self) -> typing.Dict:
        """从环境变量构建发现引擎监控配置"""
        return {
            "enable_health_check": os.getenv("ENABLE_HEALTH_CHECK", "true").lower() == "true",
            "health_check_interval": int(os.getenv("HEALTH_CHECK_INTERVAL", "300")),
            "alert_on_failure": os.getenv("ALERT_ON_FAILURE", "true").lower() == "true",
            "alert_failure_threshold": int(os.getenv("ALERT_FAILURE_THRESHOLD", "3")),
            "alert_webhook_url": os.getenv("ALERT_WEBHOOK_URL", ""),
            "metrics_retention_days": 30
        }
    
    # 日志配置 - 从环境变量获取
    DISCOVERY_LOG_LEVEL: str = os.getenv("DISCOVERY_LOG_LEVEL", "INFO")
    DISCOVERY_LOG_MAX_SIZE: str = os.getenv("LOG_FILE_MAX_SIZE", "10MB")
    DISCOVERY_LOG_BACKUP_COUNT: int = int(os.getenv("LOG_BACKUP_COUNT", "5"))
    
    @property
    def DISCOVERY_LOG_FILE(self) -> str:
        """发现引擎日志文件路径"""
        return os.path.join(self.LOGS_ROOT, "discovery.log")


settings = Settings()


# ========== CMDB发现配置工厂函数 ==========

def get_discovery_config() -> typing.Dict[str, typing.Any]:
    """
    获取CMDB发现引擎的完整配置
    
    Returns:
        包含所有数据源配置的字典
    """
    return {
        "prometheus": {
            "url": settings.PROMETHEUS_URL,
            "timeout": settings.PROMETHEUS_TIMEOUT,
            "enabled_jobs": settings.PROMETHEUS_ENABLED_JOBS,
            "excluded_jobs": settings.PROMETHEUS_EXCLUDED_JOBS,
            "sync_interval": settings.PROMETHEUS_SYNC_INTERVAL,
            "service_discovery_rules": settings.SERVICE_DISCOVERY_RULES
        },
        "zabbix": {
            "url": settings.ZABBIX_URL,
            "username": settings.ZABBIX_USERNAME,
            "password": settings.ZABBIX_PASSWORD,
            "enabled_groups": settings.ZABBIX_ENABLED_GROUPS,
            "excluded_hosts": settings.ZABBIX_EXCLUDED_HOSTS,
            "sync_interval": settings.ZABBIX_SYNC_INTERVAL
        },
        "engine": {
            "enabled": settings.DISCOVERY_ENABLED,
            "max_concurrent_jobs": settings.DISCOVERY_MAX_CONCURRENT_JOBS,
            "job_timeout": settings.DISCOVERY_JOB_TIMEOUT,
            "retry_attempts": settings.DISCOVERY_RETRY_ATTEMPTS,
            "retry_delay": settings.DISCOVERY_RETRY_DELAY,
            "mapping_config": settings.CI_MAPPING_CONFIG,
            "sync_strategy": settings.SYNC_STRATEGY
        },
        "monitoring": settings.DISCOVERY_MONITORING,
        "celery": {
            "broker_url": settings.CELERY_BROKER_URL,
            "result_backend": settings.CELERY_RESULT_BACKEND
        }
    }


def get_prometheus_config() -> typing.Dict[str, typing.Any]:
    """
    获取Prometheus专用配置
    
    Returns:
        Prometheus连接器配置字典
    """
    return {
        "url": settings.PROMETHEUS_URL,
        "timeout": settings.PROMETHEUS_TIMEOUT,
        "enabled_jobs": settings.PROMETHEUS_ENABLED_JOBS,
        "excluded_jobs": settings.PROMETHEUS_EXCLUDED_JOBS,
        "service_discovery_rules": settings.SERVICE_DISCOVERY_RULES
    }


def get_zabbix_config() -> typing.Dict[str, typing.Any]:
    """
    获取Zabbix专用配置
    
    Returns:
        Zabbix连接器配置字典
    """
    return {
        "url": settings.ZABBIX_URL,
        "username": settings.ZABBIX_USERNAME,
        "password": settings.ZABBIX_PASSWORD,
        "enabled_groups": settings.ZABBIX_ENABLED_GROUPS,
        "excluded_hosts": settings.ZABBIX_EXCLUDED_HOSTS
    }


def get_celery_config() -> typing.Dict[str, typing.Any]:
    """
    获取Celery任务队列配置
    
    Returns:
        Celery配置字典
    """
    return {
        "broker_url": settings.CELERY_BROKER_URL,
        "result_backend": settings.CELERY_RESULT_BACKEND,
        "task_serializer": "json",
        "accept_content": ["json"],
        "result_serializer": "json",
        "timezone": "Asia/Shanghai",
        "enable_utc": True,
        "task_track_started": True,
        "task_time_limit": settings.DISCOVERY_JOB_TIMEOUT,
        "worker_prefetch_multiplier": 1,
        "worker_max_tasks_per_child": 1000
    }


def get_logging_config() -> typing.Dict[str, typing.Any]:
    """
    获取发现模块的日志配置
    
    Returns:
        日志配置字典
    """
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S"
            },
            "simple": {
                "format": "%(levelname)s - %(message)s"
            }
        },
        "handlers": {
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "filename": settings.DISCOVERY_LOG_FILE,
                "maxBytes": 10 * 1024 * 1024,  # 10MB
                "backupCount": settings.DISCOVERY_LOG_BACKUP_COUNT,
                "formatter": "detailed",
                "encoding": "utf-8"
            },
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "simple",
                "stream": "ext://sys.stdout"
            }
        },
        "loggers": {
            "app.discovery": {
                "level": settings.DISCOVERY_LOG_LEVEL,
                "handlers": ["file", "console"],
                "propagate": False
            }
        }
    }
