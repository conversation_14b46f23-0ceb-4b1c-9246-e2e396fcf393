import pymysql
from datetime import datetime

# 使用配置文件中的真实数据库配置
conn = pymysql.connect(
    host='************',
    port=3306,
    user='backup_user',
    password='v+SCbs/6LsYNovFngEY=',
    database='fastapi_user',
    charset='utf8mb4'
)

cursor = conn.cursor()

# 获取当前时间
now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

try:
    # 检查路由问题的核心是组件路径
    # 前端动态路由加载需要匹配格式: /src/views${e_child.component}/index.vue
    
    # 获取工单列表菜单
    cursor.execute("SELECT id, name, path, component, parent_id FROM menu WHERE name = '工单列表'")
    ticket_list_menu = cursor.fetchone()
    
    if ticket_list_menu:
        menu_id = ticket_list_menu[0]
        current_component = ticket_list_menu[3]
        print(f"工单列表菜单ID: {menu_id}, 当前组件路径: {current_component}")
        
        # 修复组件路径 - 匹配前端动态路由加载格式
        # 前端需要格式: /ticket
        cursor.execute("""
        UPDATE menu 
        SET component = %s, 
            updated_at = %s
        WHERE id = %s
        """, ('/ticket', now, menu_id))
        
        print(f"更新工单列表菜单组件路径，影响行数: {cursor.rowcount}")
    else:
        print("未找到工单列表菜单")
    
    # 确保父级菜单路径正确
    cursor.execute("""
    SELECT id, name, path, component, redirect, parent_id 
    FROM menu 
    WHERE name = '工单管理'
    """)
    ticket_menu = cursor.fetchone()
    
    if ticket_menu:
        menu_id = ticket_menu[0]
        print(f"工单管理菜单ID: {menu_id}, 路径: {ticket_menu[2]}, 组件: {ticket_menu[3]}, 跳转: {ticket_menu[4]}")
        
        # 更新目录菜单配置，确保与前端路由匹配
        cursor.execute("""
        UPDATE menu 
        SET path = %s,
            component = %s,
            redirect = %s,
            updated_at = %s
        WHERE id = %s
        """, ('/ticket', 'Layout', '/ticket/list', now, menu_id))
        
        print(f"更新工单管理目录配置，影响行数: {cursor.rowcount}")
        
    # 查询已更新的工单菜单
    print("\n更新后的工单相关菜单:")
    cursor.execute("SELECT id, name, path, component, redirect, parent_id FROM menu WHERE name LIKE '%工单%'")
    updated_menus = cursor.fetchall()
    
    for menu in updated_menus:
        print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, 跳转: {menu[4]}, 父级ID: {menu[5]}")
    
    # 提交事务
    conn.commit()

except Exception as e:
    # 发生错误时回滚
    conn.rollback()
    print(f"发生错误: {e}")

finally:
    # 关闭数据库连接
    cursor.close()
    conn.close()

print("\n前端路由配置已修复。请执行以下步骤来测试修复效果:")
print("1. 重启后端服务: python run.py")
print("2. 清除浏览器缓存，或使用无痕模式访问")
print("3. 重新登录系统")
print("4. 访问工单管理功能")
print("\n如果仍然有问题，请检查前端控制台是否有错误信息，并在开发者工具中监控网络请求") 