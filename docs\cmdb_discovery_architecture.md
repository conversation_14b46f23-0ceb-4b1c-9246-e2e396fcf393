# CMDB 自动发现与数据同步架构设计

## 1. 整体架构概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Zabbix API    │    │ Prometheus API  │    │   其他数据源     │
│   (主机信息)    │    │ (指标+元数据)   │    │  (云平台API)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │    Discovery Engine       │
                    │   (发现引擎)              │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │   Data Processor          │
                    │   (数据处理器)            │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     CMDB Database         │
                    │   (配置管理数据库)        │
                    └───────────────────────────┘
```

## 2. 数据源对接方案

### 2.1 Zabbix 集成

**获取数据类型：**
- 主机基本信息（IP、主机名、操作系统）
- 硬件信息（CPU、内存、磁盘）
- 网络接口信息
- 服务状态和可用性
- 性能指标历史数据

**实施方案：**
```python
# Zabbix API 集成示例
class ZabbixDiscovery:
    def __init__(self, zabbix_url, username, password):
        self.api = ZabbixAPI(zabbix_url)
        self.api.login(username, password)
    
    def discover_hosts(self):
        """发现所有主机"""
        hosts = self.api.host.get({
            "output": ["hostid", "host", "name", "status"],
            "selectInterfaces": ["interfaceid", "ip", "dns", "port"],
            "selectGroups": ["groupid", "name"],
            "selectInventory": "extend",
            "selectItems": ["itemid", "name", "key_", "value_type"]
        })
        return hosts
    
    def get_host_details(self, hostid):
        """获取主机详细信息"""
        host_info = self.api.host.get({
            "hostids": hostid,
            "output": "extend",
            "selectInventory": "extend",
            "selectMacros": "extend"
        })
        return host_info[0] if host_info else None
```

### 2.2 Prometheus 集成

**获取数据类型：**
- 容器和Pod信息（Kubernetes）
- 应用服务指标
- 基础设施指标
- 业务指标
- 自定义标签和元数据

**实施方案：**
```python
# Prometheus API 集成示例
class PrometheusDiscovery:
    def __init__(self, prometheus_url):
        self.base_url = prometheus_url
    
    def get_targets(self):
        """获取所有监控目标"""
        response = requests.get(f"{self.base_url}/api/v1/targets")
        return response.json()['data']['activeTargets']
    
    def get_metadata(self):
        """获取指标元数据"""
        response = requests.get(f"{self.base_url}/api/v1/metadata")
        return response.json()['data']
    
    def discover_services_from_labels(self):
        """从标签发现服务"""
        query = 'up{job!=""}'
        response = requests.get(f"{self.base_url}/api/v1/query", 
                              params={'query': query})
        return response.json()['data']['result']
```

## 3. 发现引擎设计

### 3.1 核心组件

```python
# 发现引擎主体架构
class CMDBDiscoveryEngine:
    def __init__(self):
        self.zabbix = ZabbixDiscovery(config.ZABBIX_URL, 
                                     config.ZABBIX_USER, 
                                     config.ZABBIX_PASSWORD)
        self.prometheus = PrometheusDiscovery(config.PROMETHEUS_URL)
        self.processors = {
            'server': ServerProcessor(),
            'service': ServiceProcessor(),
            'container': ContainerProcessor(),
            'network': NetworkProcessor()
        }
    
    async def run_discovery(self):
        """执行发现流程"""
        logger.info("开始CMDB自动发现...")
        
        # 1. 从各数据源收集数据
        zabbix_data = await self.collect_zabbix_data()
        prometheus_data = await self.collect_prometheus_data()
        
        # 2. 数据处理和标准化
        processed_data = await self.process_collected_data(
            zabbix_data, prometheus_data)
        
        # 3. 同步到CMDB
        await self.sync_to_cmdb(processed_data)
        
        logger.info("CMDB自动发现完成")
```

### 3.2 数据处理器

```python
class DataProcessor:
    """数据处理基类"""
    
    def normalize_data(self, raw_data, source_type):
        """数据标准化"""
        pass
    
    def detect_ci_type(self, data):
        """检测CI类型"""
        pass
    
    def map_to_cmdb_schema(self, data):
        """映射到CMDB数据模型"""
        pass

class ServerProcessor(DataProcessor):
    """服务器数据处理器"""
    
    def process_zabbix_host(self, host_data):
        """处理Zabbix主机数据"""
        return {
            'id': f"server_{host_data['hostid']}",
            'ci_type_id': self.get_ci_type_id('PHYSICAL_SERVER'),
            'name': host_data['name'],
            'ip_addresses': [iface['ip'] for iface in host_data['interfaces']],
            'operating_system': host_data.get('inventory', {}).get('os'),
            'hardware_specifications': {
                'cpu': host_data.get('inventory', {}).get('hardware'),
                'memory': host_data.get('inventory', {}).get('memory'),
                'storage': host_data.get('inventory', {}).get('storage')
            },
            'monitoring_enabled': True,
            'zabbix_host_id': host_data['hostid'],
            'discovery_source': 'SNMP',
            'last_discovered': datetime.now()
        }
```

## 4. 数据库设计增强

```sql
-- 数据源配置表
CREATE TABLE discovery_sources (
    id INT AUTO_INCREMENT PRIMARY KEY,
    source_name VARCHAR(50) NOT NULL UNIQUE COMMENT '数据源名称',
    source_type ENUM('zabbix', 'prometheus', 'snmp', 'api', 'agent') NOT NULL COMMENT '数据源类型',
    endpoint_url VARCHAR(200) NOT NULL COMMENT '数据源地址',
    auth_config JSON COMMENT '认证配置',
    sync_interval INT DEFAULT 300 COMMENT '同步间隔(秒)',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_sync_time TIMESTAMP NULL COMMENT '最后同步时间',
    sync_status ENUM('成功', '失败', '进行中') DEFAULT '成功' COMMENT '同步状态',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT='数据源配置表';

-- 发现规则表
CREATE TABLE discovery_rules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    source_id INT NOT NULL COMMENT '数据源ID',
    ci_type_id INT NOT NULL COMMENT '目标CI类型',
    discovery_method ENUM('api_query', 'snmp_scan', 'agent_report', 'log_parse') NOT NULL COMMENT '发现方法',
    query_config JSON NOT NULL COMMENT '查询配置',
    mapping_config JSON NOT NULL COMMENT '字段映射配置',
    filter_config JSON COMMENT '过滤条件',
    schedule_config JSON COMMENT '调度配置',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (source_id) REFERENCES discovery_sources(id),
    FOREIGN KEY (ci_type_id) REFERENCES ci_types(id),
    INDEX idx_source_type (source_id, ci_type_id)
) COMMENT='发现规则表';

-- 发现任务表
CREATE TABLE discovery_jobs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_name VARCHAR(100) NOT NULL COMMENT '任务名称',
    rule_id INT NOT NULL COMMENT '发现规则ID',
    status ENUM('等待', '运行中', '已完成', '失败', '已取消') DEFAULT '等待' COMMENT '任务状态',
    progress INT DEFAULT 0 COMMENT '执行进度(0-100)',
    
    -- 执行信息
    started_at TIMESTAMP NULL COMMENT '开始时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    total_items INT DEFAULT 0 COMMENT '总项目数',
    processed_items INT DEFAULT 0 COMMENT '已处理项目数',
    successful_items INT DEFAULT 0 COMMENT '成功项目数',
    failed_items INT DEFAULT 0 COMMENT '失败项目数',
    
    -- 结果信息
    result_summary JSON COMMENT '结果摘要',
    error_details JSON COMMENT '错误详情',
    log_file_path VARCHAR(200) COMMENT '日志文件路径',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (rule_id) REFERENCES discovery_rules(id),
    INDEX idx_status (status),
    INDEX idx_started (started_at)
) COMMENT='发现任务表';

-- CI同步历史表
CREATE TABLE ci_sync_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ci_id VARCHAR(50) NOT NULL COMMENT 'CI ID',
    job_id BIGINT NOT NULL COMMENT '发现任务ID',
    action_type ENUM('创建', '更新', '删除', '跳过') NOT NULL COMMENT '操作类型',
    
    -- 数据对比
    source_data JSON COMMENT '源数据',
    existing_data JSON COMMENT '现有数据',
    changes_detected JSON COMMENT '检测到的变更',
    
    -- 执行结果
    sync_status ENUM('成功', '失败', '冲突', '跳过') NOT NULL COMMENT '同步状态',
    error_message TEXT COMMENT '错误信息',
    conflict_resolution TEXT COMMENT '冲突解决方案',
    
    synced_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (ci_id) REFERENCES configuration_items(id),
    FOREIGN KEY (job_id) REFERENCES discovery_jobs(id),
    INDEX idx_ci_job (ci_id, job_id),
    INDEX idx_synced (synced_at)
) COMMENT='CI同步历史表';
```

## 5. 具体实施步骤

### 5.1 Phase 1: 基础架构搭建

1. **环境准备**
   ```bash
   # 安装依赖
   pip install zabbix-api prometheus-api-client asyncio celery redis
   
   # 配置数据库
   mysql -u root -p < cmdb_discovery_schema.sql
   ```

2. **配置管理**
   ```python
   # config/discovery_config.py
   DISCOVERY_CONFIG = {
       'zabbix': {
           'url': 'http://zabbix.example.com/api_jsonrpc.php',
           'username': 'admin',
           'password': 'password',
           'timeout': 30
       },
       'prometheus': {
           'url': 'http://prometheus.example.com:9090',
           'timeout': 30
       },
       'sync_intervals': {
           'servers': 3600,  # 1小时
           'services': 1800,  # 30分钟
           'metrics': 300     # 5分钟
       }
   }
   ```

### 5.2 Phase 2: 核心功能开发

1. **API连接器开发**
   ```python
   # app/discovery/connectors/zabbix_connector.py
   # app/discovery/connectors/prometheus_connector.py
   ```

2. **数据处理器开发**
   ```python
   # app/discovery/processors/
   # ├── base_processor.py
   # ├── server_processor.py
   # ├── service_processor.py
   # └── container_processor.py
   ```

3. **同步引擎开发**
   ```python
   # app/discovery/sync_engine.py
   ```

### 5.3 Phase 3: 调度和监控

1. **使用Celery实现异步任务调度**
   ```python
   # app/discovery/tasks.py
   from celery import Celery
   
   app = Celery('cmdb_discovery')
   
   @app.task
   def run_discovery_job(rule_id):
       """执行发现任务"""
       engine = CMDBDiscoveryEngine()
       return engine.execute_rule(rule_id)
   
   @app.periodic_task(run_every=crontab(minute=0))
   def scheduled_discovery():
       """定时发现任务"""
       active_rules = DiscoveryRule.get_active_rules()
       for rule in active_rules:
           run_discovery_job.delay(rule.id)
   ```

2. **监控和告警**
   ```python
   # app/discovery/monitoring.py
   class DiscoveryMonitor:
       def check_sync_health(self):
           """检查同步健康状态"""
           failed_jobs = DiscoveryJob.get_recent_failures()
           if len(failed_jobs) > threshold:
               self.send_alert(f"发现任务失败率过高: {len(failed_jobs)}")
   ```

## 6. API接口设计

```python
# app/api/v1/discovery.py
from fastapi import APIRouter, Depends

router = APIRouter(prefix="/api/v1/discovery", tags=["CMDB发现"])

@router.post("/sources", summary="创建数据源")
async def create_discovery_source(source: DiscoverySourceCreate):
    """创建新的数据源配置"""
    return await discovery_service.create_source(source)

@router.post("/rules", summary="创建发现规则")
async def create_discovery_rule(rule: DiscoveryRuleCreate):
    """创建新的发现规则"""
    return await discovery_service.create_rule(rule)

@router.post("/jobs/{rule_id}/run", summary="手动执行发现任务")
async def run_discovery_job(rule_id: int):
    """手动触发发现任务"""
    job = await discovery_service.create_job(rule_id)
    run_discovery_job.delay(job.id)
    return {"job_id": job.id, "status": "启动中"}

@router.get("/jobs/{job_id}/status", summary="查询任务状态")
async def get_job_status(job_id: int):
    """获取发现任务执行状态"""
    return await discovery_service.get_job_status(job_id)
```

## 7. 前端界面设计

```vue
<!-- web/src/views/cmdb/discovery/DiscoveryManagement.vue -->
<template>
  <div class="discovery-management">
    <!-- 数据源管理 -->
    <n-card title="数据源配置" class="mb-4">
      <discovery-sources-table 
        :sources="sources" 
        @refresh="loadSources"
        @test-connection="testConnection"
      />
    </n-card>
    
    <!-- 发现规则 -->
    <n-card title="发现规则" class="mb-4">
      <discovery-rules-table 
        :rules="rules"
        @run-discovery="runDiscovery"
        @edit-rule="editRule"
      />
    </n-card>
    
    <!-- 任务执行状态 -->
    <n-card title="执行状态">
      <discovery-jobs-monitor 
        :jobs="recentJobs"
        @view-details="viewJobDetails"
      />
    </n-card>
  </div>
</template>
```

## 8. 部署和运维

### 8.1 Docker化部署

```dockerfile
# Dockerfile.discovery
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY app/ ./app/
CMD ["celery", "worker", "-A", "app.discovery.tasks", "--loglevel=info"]
```

### 8.2 监控告警

```yaml
# prometheus监控配置
- alert: CMDBDiscoveryJobFailure
  expr: increase(cmdb_discovery_job_failures_total[5m]) > 0
  labels:
    severity: warning
  annotations:
    summary: "CMDB发现任务失败"
    description: "{{ $labels.job_name }} 发现任务执行失败"
```

这个方案提供了完整的CMDB自动发现和数据同步架构，支持多数据源集成，具有良好的扩展性和可维护性。您可以根据具体需求调整实现细节。 