import pymysql
from datetime import datetime

# 使用配置文件中的真实数据库配置
conn = pymysql.connect(
    host='************',
    port=3306,
    user='backup_user',
    password='v+SCbs/6LsYNovFngEY=',
    database='fastapi_user',
    charset='utf8mb4'
)

cursor = conn.cursor()

try:
    # 获取角色ID（管理员角色）
    cursor.execute("SELECT id FROM role WHERE name = '管理员' LIMIT 1")
    admin_role = cursor.fetchone()
    admin_role_id = admin_role[0] if admin_role else 1  # 默认使用ID为1的角色
    
    print(f"找到管理员角色ID: {admin_role_id}")
    
    # 获取工单相关API ID
    cursor.execute("SELECT id FROM api WHERE path LIKE %s", ('%/api/v1/tickets%',))
    api_ids = cursor.fetchall()
    
    if not api_ids:
        print("未找到工单相关API，请先运行脚本添加API配置")
    else:
        print(f"找到 {len(api_ids)} 个工单相关API")
        
        # 为管理员角色添加API权限
        for api_id in api_ids:
            # 检查权限是否已存在
            cursor.execute("SELECT 1 FROM role_api WHERE role_id = %s AND api_id = %s", (admin_role_id, api_id[0]))
            exists = cursor.fetchone()
            
            if not exists:
                cursor.execute("""
                INSERT INTO role_api (role_id, api_id)
                VALUES (%s, %s)
                """, (admin_role_id, api_id[0]))
                print(f"为角色 {admin_role_id} 添加API权限 {api_id[0]}")
            else:
                print(f"角色 {admin_role_id} 已有API权限 {api_id[0]}")
    
    # 获取所有API
    cursor.execute("SELECT id FROM api")
    all_api_ids = cursor.fetchall()
    
    print(f"\n为确保管理员角色有完整权限，将添加所有API权限")
    # 为管理员角色添加所有API权限
    for api_id in all_api_ids:
        # 检查权限是否已存在
        cursor.execute("SELECT 1 FROM role_api WHERE role_id = %s AND api_id = %s", (admin_role_id, api_id[0]))
        exists = cursor.fetchone()
        
        if not exists:
            cursor.execute("""
            INSERT INTO role_api (role_id, api_id)
            VALUES (%s, %s)
            """, (admin_role_id, api_id[0]))
            print(f"为角色 {admin_role_id} 添加API权限 {api_id[0]}")
    
    # 获取工单相关菜单ID
    cursor.execute("SELECT id FROM menu WHERE name LIKE %s", ('%工单%',))
    menu_ids = cursor.fetchall()
    
    if not menu_ids:
        print("未找到工单相关菜单，请先运行脚本添加菜单配置")
    else:
        print(f"\n找到 {len(menu_ids)} 个工单相关菜单")
        
        # 为管理员角色添加菜单权限
        for menu_id in menu_ids:
            # 检查权限是否已存在
            cursor.execute("SELECT 1 FROM role_menu WHERE role_id = %s AND menu_id = %s", (admin_role_id, menu_id[0]))
            exists = cursor.fetchone()
            
            if not exists:
                cursor.execute("""
                INSERT INTO role_menu (role_id, menu_id)
                VALUES (%s, %s)
                """, (admin_role_id, menu_id[0]))
                print(f"为角色 {admin_role_id} 添加菜单权限 {menu_id[0]}")
            else:
                print(f"角色 {admin_role_id} 已有菜单权限 {menu_id[0]}")
    
    # 提交事务
    conn.commit()
    
    # 检查角色和API权限
    print("\n检查角色和API权限关联...")
    cursor.execute("""
    SELECT r.id, r.name, COUNT(ra.api_id) as api_count 
    FROM role r 
    LEFT JOIN role_api ra ON r.id = ra.role_id 
    GROUP BY r.id
    """)
    role_apis = cursor.fetchall()
    
    print("角色和API权限数量:")
    for role in role_apis:
        print(f"角色ID: {role[0]}, 角色名称: {role[1]}, API权限数量: {role[2]}")

except Exception as e:
    # 发生错误时回滚
    conn.rollback()
    print(f"发生错误: {e}")

finally:
    # 关闭数据库连接
    cursor.close()
    conn.close()

print("\n角色-API权限配置已添加完成。请刷新前端页面，并重新登录系统尝试访问工单管理功能。")
print("如果页面仍然显示404，请尝试清除浏览器缓存后重新登录。") 