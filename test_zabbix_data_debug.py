import asyncio
import sys
sys.path.append('.')
from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector, ZabbixDataMapper
from app.settings.config import get_zabbix_config
import json

async def test_zabbix_data():
    """测试Zabbix数据映射问题"""
    config = get_zabbix_config()
    connector = ZabbixDiscoveryConnector(config)
    
    hosts = await connector.discover_hosts()
    print(f"发现了 {len(hosts)} 个主机")
    
    if hosts:
        # 取前3个主机进行分析
        for i, host in enumerate(hosts[:3]):
            print(f"\n=== 主机 {i+1} ===")
            print(f"Host ID: {host.hostid}")
            print(f"Host: {host.host}")
            print(f"Name: {host.name}")
            print(f"Status: {host.status}")
            print(f"Interfaces ({len(host.interfaces)}个):")
            for j, interface in enumerate(host.interfaces[:2]):
                print(f"  Interface {j+1}: {interface}")
            
            print(f"Inventory 键值 ({len(host.inventory)}个): {list(host.inventory.keys())[:10]}")
            if host.inventory:
                print(f"  OS: {host.inventory.get('os')}")
                print(f"  Location: {host.inventory.get('location')}")
                print(f"  Hardware: {host.inventory.get('hardware_full')}")
            
            # 映射数据
            mapped_data = ZabbixDataMapper.map_host_to_ci(host)
            print(f"\n映射后的数据:")
            print(f"  ID: {mapped_data.get('id')}")
            print(f"  Asset Tag: {mapped_data.get('asset_tag')}")
            print(f"  Hostname: {mapped_data.get('hostname')}")
            print(f"  IP Addresses: {mapped_data.get('ip_addresses')}")
            print(f"  Operating System: {mapped_data.get('operating_system')}")
            print(f"  CI Type: {mapped_data.get('ci_type_code')}")

if __name__ == "__main__":
    asyncio.run(test_zabbix_data()) 