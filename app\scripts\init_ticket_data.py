import asyncio
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from tortoise import Tortoise, run_async
from app.models.ticket import TicketCategory, TicketPriority, TicketStatus, ServiceCatalog

# 定义数据库配置
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.mysql",
            "credentials": {
                "host": "************",
                "port": "3306",
                "user": "backup_user",
                "password": "v+SCbs/6LsYNovFngEY=",
                "database": "fastapi_user",
                "charset": "utf8mb4"
            }
        }
    },
    "apps": {
        "models": {
            "models": ["app.models.admin", "app.models.ticket", "aerich.models"],
            "default_connection": "default",
        }
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai",
}

async def init_ticket_data():
    """初始化工单相关基础数据"""
    print("正在初始化工单基础数据...")
    
    # 初始化Tortoise ORM
    await Tortoise.init(config=TORTOISE_ORM)
    
    # 初始化工单分类
    categories = [
        {"name": "技术问题", "desc": "技术相关问题", "order": 1, "is_active": True},
        {"name": "账户问题", "desc": "用户账户相关问题", "order": 2, "is_active": True},
        {"name": "功能请求", "desc": "新功能需求", "order": 3, "is_active": True},
        {"name": "bug报告", "desc": "程序错误报告", "order": 4, "is_active": True},
        {"name": "其他", "desc": "其他类型问题", "order": 5, "is_active": True}
    ]
    
    print("创建工单分类...")
    for category in categories:
        await TicketCategory.get_or_create(**category)
    
    # 初始化工单优先级
    priorities = [
        {"name": "紧急", "level": 1, "color": "#f5222d", "sla_hours": 2},
        {"name": "高", "level": 2, "color": "#fa8c16", "sla_hours": 8},
        {"name": "中", "level": 3, "color": "#1890ff", "sla_hours": 24},
        {"name": "低", "level": 4, "color": "#52c41a", "sla_hours": 48}
    ]
    
    print("创建工单优先级...")
    for priority in priorities:
        await TicketPriority.get_or_create(**priority)
    
    # 初始化工单状态
    statuses = [
        {"name": "新建", "code": "new", "color": "#1890ff", "is_final": False, "order": 1},
        {"name": "处理中", "code": "in_progress", "color": "#fa8c16", "is_final": False, "order": 2},
        {"name": "已解决", "code": "resolved", "color": "#52c41a", "is_final": False, "order": 3},
        {"name": "已关闭", "code": "closed", "color": "#8c8c8c", "is_final": True, "order": 4},
        {"name": "重新打开", "code": "reopened", "color": "#f5222d", "is_final": False, "order": 5}
    ]
    
    print("创建工单状态...")
    for status in statuses:
        await TicketStatus.get_or_create(**status)
    
    # 获取第一个分类和优先级，用于服务目录的外键关联
    first_category = await TicketCategory.first()
    first_priority = await TicketPriority.first()
    
    if not first_category or not first_priority:
        print("错误: 必须先创建分类和优先级")
        return
    
    # 初始化服务目录
    services = [
        {
            "name": "应用程序支持", 
            "desc": "应用程序相关支持", 
            "category_id": first_category,
            "sla_priority_id": first_priority,
            "is_active": True
        },
        {
            "name": "硬件支持", 
            "desc": "硬件相关支持", 
            "category_id": first_category,
            "sla_priority_id": first_priority,
            "is_active": True
        },
        {
            "name": "网络支持", 
            "desc": "网络相关支持", 
            "category_id": first_category,
            "sla_priority_id": first_priority,
            "is_active": True
        },
        {
            "name": "系统支持", 
            "desc": "系统相关支持", 
            "category_id": first_category,
            "sla_priority_id": first_priority,
            "is_active": True
        },
        {
            "name": "用户培训", 
            "desc": "用户培训支持", 
            "category_id": first_category,
            "sla_priority_id": first_priority,
            "is_active": True
        }
    ]
    
    print("创建服务目录...")
    for service in services:
        await ServiceCatalog.get_or_create(**service)
    
    print("工单基础数据初始化完成！")
    
    # 关闭连接
    await Tortoise.close_connections()

if __name__ == "__main__":
    run_async(init_ticket_data()) 