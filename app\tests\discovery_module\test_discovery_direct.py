#!/usr/bin/env python3
"""
直接测试发现引擎功能，避免Celery异步问题
"""

import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.settings.env_manager import init_environment
from app.core.database import init_database
from app.models.cmdb import DiscoverySource, ConfigurationItem, DiscoveryJob
from app.discovery.engine import create_discovery_engine
from tortoise import Tortoise
from loguru import logger

async def test_direct_discovery():
    """直接测试发现引擎功能"""
    
    try:
        # 初始化环境
        logger.info("初始化环境配置...")
        init_environment()
        
        # 初始化数据库
        logger.info("初始化数据库连接...")
        await init_database()
        
        logger.info("🚀 开始直接测试发现引擎")
        logger.info("=" * 60)
        
        # 检查数据源状态
        logger.info("📋 检查发现数据源状态...")
        sources = await DiscoverySource.filter(is_enabled=True)
        
        for source in sources:
            logger.info(f"  ✅ {source.name} ({source.source_type}) - 健康状态: {source.is_healthy}")
        
        if not sources:
            logger.error("❌ 未找到可用的发现数据源！")
            return
        
        # 统计执行前的配置项数量
        before_count = await ConfigurationItem.all().count()
        logger.info(f"📊 执行前配置项数量: {before_count}")
        
        # 创建发现引擎
        logger.info("🔧 创建发现引擎...")
        discovery_engine = create_discovery_engine()
        
        # 执行发现
        logger.info("🔍 开始执行发现...")
        start_time = datetime.now()
        
        results = await discovery_engine.run_discovery()
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        # 显示结果
        logger.info("📈 发现结果:")
        logger.info(f"总执行时间: {execution_time:.2f}秒")
        
        for source_name, result in results.items():
            logger.info(f"\n📊 {source_name} 数据源:")
            logger.info(f"  状态: {result.status.value}")
            logger.info(f"  总项目: {result.total_items}")
            logger.info(f"  成功项目: {result.successful_items}")
            logger.info(f"  失败项目: {result.failed_items}")
            logger.info(f"  跳过项目: {result.skipped_items}")
            logger.info(f"  执行时间: {result.execution_time:.2f}秒")
            
            if result.errors:
                logger.error(f"  错误信息:")
                for error in result.errors[:3]:  # 只显示前3个错误
                    logger.error(f"    - {error}")
        
        # 统计执行后的配置项数量
        after_count = await ConfigurationItem.all().count()
        logger.info(f"📊 执行后配置项数量: {after_count}")
        logger.info(f"📈 新增配置项: {after_count - before_count}")
        
        # 显示按数据源分组的配置项统计
        logger.info("\n📋 配置项按数据源分组:")
        for source in sources:
            source_type = source.source_type.lower()
            count = await ConfigurationItem.filter(data_source=source_type).count()
            logger.info(f"  {source.name}: {count} 个配置项")
        
        # 显示最新的配置项样例
        logger.info("\n🔍 最新配置项样例:")
        latest_cis = await ConfigurationItem.all().order_by("-created_at").limit(5)
        
        for ci in latest_cis:
            ip_list = ", ".join(ci.ip_addresses) if ci.ip_addresses else "无IP"
            logger.info(f"  - {ci.name} ({ci.ci_type}) - {ip_list} - {ci.data_source}")
        
        logger.info("=" * 60)
        logger.info("✅ 直接发现测试完成!")
        
    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {str(e)}")
        logger.exception("详细错误信息:")
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_direct_discovery()) 