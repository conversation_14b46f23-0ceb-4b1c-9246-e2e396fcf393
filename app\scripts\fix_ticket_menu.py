import mysql.connector
from datetime import datetime

def fix_ticket_menu():
    """修复工单管理菜单结构"""
    print("开始修复工单菜单结构...")
    
    # 连接到MySQL数据库
    conn = mysql.connector.connect(
        host="localhost",
        user="root",
        password="123456",
        database="fastapi_admin"
    )
    cursor = conn.cursor()
    
    # 获取当前时间
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    try:
        # 查询现有的工单菜单
        cursor.execute("SELECT id, name, path, component, parent_id FROM menu WHERE name LIKE '%工单%'")
        ticket_menus = cursor.fetchall()
        
        print(f"找到 {len(ticket_menus)} 个工单相关菜单:")
        for menu in ticket_menus:
            print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, 父级ID: {menu[4]}")
        
        # 检查是否有工单管理目录菜单
        cursor.execute("SELECT id FROM menu WHERE name = '工单管理' AND menu_type = 'catalog'")
        ticket_catalog = cursor.fetchone()
        
        if not ticket_catalog:
            print("未找到工单管理目录菜单，创建新的工单管理目录...")
            cursor.execute("""
            INSERT INTO menu (created_at, updated_at, name, path, component, redirect, icon, menu_type, `order`, parent_id, is_hidden, keepalive)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (now, now, '工单管理', '/ticket', 'Layout', '/ticket/list', 'ph:ticket-bold', 'catalog', 3, 0, 0, 0))
            ticket_catalog_id = cursor.lastrowid
            print(f"已创建工单管理目录，ID: {ticket_catalog_id}")
        else:
            ticket_catalog_id = ticket_catalog[0]
            print(f"已找到工单管理目录，ID: {ticket_catalog_id}")
            
            # 更新工单管理目录菜单
            cursor.execute("""
            UPDATE menu SET 
                path = '/ticket', 
                component = 'Layout',
                redirect = '/ticket/list',
                icon = 'ph:ticket-bold',
                menu_type = 'catalog',
                `order` = 3,
                parent_id = 0,
                is_hidden = 0
            WHERE id = %s
            """, (ticket_catalog_id,))
            print(f"已更新工单管理目录菜单配置")
        
        # 检查是否有工单列表子菜单
        cursor.execute("SELECT id FROM menu WHERE name = '工单列表' AND parent_id = %s", (ticket_catalog_id,))
        ticket_list = cursor.fetchone()
        
        if not ticket_list:
            print("未找到工单列表子菜单，创建新的工单列表菜单...")
            cursor.execute("""
            INSERT INTO menu (created_at, updated_at, name, path, component, redirect, icon, menu_type, `order`, parent_id, is_hidden, keepalive)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (now, now, '工单列表', 'list', '/ticket/TicketList', '', 'ph:list-bold', 'menu', 1, ticket_catalog_id, 0, 1))
            ticket_list_id = cursor.lastrowid
            print(f"已创建工单列表子菜单，ID: {ticket_list_id}")
        else:
            ticket_list_id = ticket_list[0]
            print(f"已找到工单列表子菜单，ID: {ticket_list_id}")
            
            # 更新工单列表子菜单
            cursor.execute("""
            UPDATE menu SET 
                path = 'list', 
                component = '/ticket/TicketList',
                icon = 'ph:list-bold',
                menu_type = 'menu',
                `order` = 1,
                parent_id = %s,
                is_hidden = 0,
                keepalive = 1
            WHERE id = %s
            """, (ticket_catalog_id, ticket_list_id))
            print(f"已更新工单列表子菜单配置")
        
        # 清理其他不正确的工单菜单
        cursor.execute("""
        SELECT id FROM menu 
        WHERE name LIKE '%工单%' 
        AND id NOT IN (%s, %s)
        """, (ticket_catalog_id, ticket_list_id))
        other_menus = cursor.fetchall()
        
        for menu_id in other_menus:
            cursor.execute("DELETE FROM menu WHERE id = %s", (menu_id[0],))
            print(f"已删除冗余工单菜单，ID: {menu_id[0]}")
        
        # 提交事务
        conn.commit()
        
        # 验证修复结果
        cursor.execute("SELECT id, name, path, component, parent_id FROM menu WHERE name LIKE '%工单%'")
        fixed_menus = cursor.fetchall()
        
        print("\n修复后的工单菜单:")
        for menu in fixed_menus:
            print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, 父级ID: {menu[4]}")
        
        # 检查用户角色是否有权限访问工单菜单
        cursor.execute("SELECT id, name FROM role")
        roles = cursor.fetchall()
        
        for role_id, role_name in roles:
            # 检查角色是否已授权工单管理菜单
            cursor.execute("""
            SELECT COUNT(*) FROM role_menu 
            WHERE role_id = %s AND menu_id IN (%s, %s)
            """, (role_id, ticket_catalog_id, ticket_list_id))
            has_ticket_auth = cursor.fetchone()[0] > 0
            
            if not has_ticket_auth:
                print(f"角色 '{role_name}' 未授权访问工单菜单，正在授权...")
                # 授权工单目录菜单
                cursor.execute("""
                INSERT INTO role_menu (role_id, menu_id)
                VALUES (%s, %s)
                """, (role_id, ticket_catalog_id))
                
                # 授权工单列表菜单
                cursor.execute("""
                INSERT INTO role_menu (role_id, menu_id)
                VALUES (%s, %s)
                """, (role_id, ticket_list_id))
                
                print(f"已授权角色 '{role_name}' 访问工单菜单")
        
        # 提交事务
        conn.commit()
        
        print("\n工单菜单修复完成！请刷新前端页面查看效果。")
        
    except Exception as e:
        conn.rollback()
        print(f"修复工单菜单时出错: {str(e)}")
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    fix_ticket_menu() 