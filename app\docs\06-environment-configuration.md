# 环境配置管理

本项目采用多环境配置管理方案，支持开发/测试环境共用一套配置，生产环境使用独立配置。

## 🏗️ 环境架构

### 环境类型

| 环境类型 | 配置文件 | 用途 | 特点 |
|---------|---------|------|------|
| **development** | `env.development` | 开发环境 | 调试模式开启，本地数据库 |
| **testing** | `env.development` | 测试环境 | 共用开发环境配置 |
| **production** | `env.production` | 生产环境 | 性能优化，安全加固 |

### 配置文件位置

```
app/settings/
├── env.example          # 原始配置模板（保留）
├── env.development      # 开发/测试环境配置
├── env.production       # 生产环境配置
├── env_manager.py       # 环境管理器
└── config.py           # 配置类（已更新）
```

## 🚀 快速开始

### 1. 使用环境设置脚本（推荐）

```bash
# 查看环境信息
python app/scripts/setup_env.py --info

# 设置为开发环境
python app/scripts/setup_env.py --env development

# 设置为生产环境
python app/scripts/setup_env.py --env production

# 验证当前配置
python app/scripts/setup_env.py --validate
```

### 2. 手动设置环境

```bash
# 开发环境
cp app/settings/env.development .env

# 生产环境
cp app/settings/env.production .env

# 编辑配置文件
vim .env  # 或使用其他编辑器
```

## ⚙️ 环境变量说明

### 核心配置

| 变量名 | 说明 | 开发环境默认值 | 生产环境示例 |
|--------|------|---------------|-------------|
| `ENVIRONMENT` | 环境标识 | `development` | `production` |
| `DEBUG` | 调试模式 | `true` | `false` |
| `SECRET_KEY` | JWT密钥 | 开发专用密钥 | 强随机密钥 |

### 数据库配置

| 变量名 | 说明 | 开发环境 | 生产环境 |
|--------|------|----------|----------|
| `DB_HOST` | 数据库主机 | `************` | `your_production_db_host` |
| `DB_PORT` | 数据库端口 | `3306` | `3306` |
| `DB_USER` | 数据库用户 | `backup_user` | `your_production_db_user` |
| `DB_PASSWORD` | 数据库密码 | `v+SCbs/6LsYNovFngEY=` | `your_production_db_password` |
| `DB_NAME` | 数据库名称 | `fastapi_user` | `fastapi_user_prod` |

### 外部服务配置

| 服务 | 开发环境 | 生产环境 |
|------|----------|----------|
| **Prometheus** | `http://*************:31891` | `http://your_prometheus_host:9090` |
| **Zabbix** | `http://************/zabbix/api_jsonrpc.php` | `http://your_zabbix_host/zabbix/api_jsonrpc.php` |
| **Redis** | `redis://:CjK1rry1as@************:6379/0` | `redis://your_production_redis_host:6379/0` |

## 🔒 安全最佳实践

### 开发环境安全

- ✅ 使用基于 env.example 的实际服务器配置
- ✅ 开启调试模式便于排错
- ✅ 允许所有跨域请求
- ⚠️  与实际服务器环境连接（注意数据安全）

### 生产环境安全

- 🔐 **必须修改默认 SECRET_KEY**
- 🔐 使用强密码和加密连接
- 🔐 关闭调试模式
- 🔐 限制跨域请求源
- 🔐 启用 HTTPS
- 🔐 定期更新敏感信息

### 密钥管理

```bash
# 生成强随机密钥（推荐32字符以上）
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 或使用 openssl
openssl rand -base64 32
```

## 🛠️ 配置验证

### 自动验证

环境管理器会自动验证：

- ✅ 必需环境变量是否存在
- ✅ 生产环境安全检查
- ✅ 配置文件格式正确性
- ✅ 数据库连接参数

### 手动验证

```bash
# 使用设置脚本验证
python app/scripts/setup_env.py --validate

# 使用环境管理器验证
python -c "from app.settings.env_manager import init_environment; init_environment()"
```

## 🔄 环境切换

### 开发 → 生产

```bash
# 1. 备份当前配置
cp .env .env.backup

# 2. 切换到生产环境
python app/scripts/setup_env.py --env production

# 3. 修改敏感信息
vim .env

# 4. 验证配置
python app/scripts/setup_env.py --validate
```

### 生产 → 开发

```bash
# 切换回开发环境
python app/scripts/setup_env.py --env development
```

## 📝 配置文件示例

### 开发环境 (`env.development`)

```env
# 环境标识
ENVIRONMENT=development

# 数据库配置 - 基于 env.example 的实际配置
DB_HOST=************
DB_USER=backup_user
DB_PASSWORD=v+SCbs/6LsYNovFngEY=
DB_NAME=fastapi_user

# 安全配置 - 开发环境
SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
DEBUG=true

# 外部服务配置
PROMETHEUS_URL=http://*************:31891
ZABBIX_URL=http://************/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=niuyeji
ZABBIX_PASSWORD=Changjiu123!
REDIS_URL=redis://:CjK1rry1as@************:6379/0

# 应用配置
APP_TITLE=自动化运维系统
CORS_ORIGINS=*
```

### 生产环境 (`env.production`)

```env
# 环境标识
ENVIRONMENT=production

# 数据库配置 - 生产环境（需要根据实际环境修改）
DB_HOST=your_production_db_host
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password
DB_NAME=fastapi_user_prod

# 安全配置 - 生产环境（必须修改为强密钥）
SECRET_KEY=please_change_this_to_a_strong_production_secret_key_32_chars_or_more
DEBUG=false

# 外部服务配置（需要根据实际环境修改）
PROMETHEUS_URL=http://your_prometheus_host:9090
ZABBIX_URL=http://your_zabbix_host/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=your_production_zabbix_user
ZABBIX_PASSWORD=your_production_zabbix_password
REDIS_URL=redis://your_production_redis_host:6379/0

# 应用配置
APP_TITLE=自动化运维系统
CORS_ORIGINS=https://yourdomain.com,https://admin.yourdomain.com
```

## ⚠️ 注意事项

### 文件管理

- 📝 **`.env` 文件已添加到 `.gitignore`**，不会被提交到版本控制
- 📝 配置模板文件（`env.development`, `env.production`）会被提交
- 📝 生产环境配置中的敏感信息需要手动替换

### 环境变量优先级

1. **系统环境变量**（最高优先级）
2. **`.env` 文件**
3. **配置文件默认值**（最低优先级）

### 部署建议

```bash
# 生产环境部署流程
1. git clone 项目代码
2. python app/scripts/setup_env.py --env production
3. 编辑 .env 文件，设置真实的生产环境配置
4. python app/scripts/setup_env.py --validate
5. 启动应用
```

## 🐛 故障排除

### 常见问题

**问题：缺少必需的环境变量**
```bash
# 解决方案
python app/scripts/setup_env.py --validate
# 根据提示补充缺失的变量
```

**问题：生产环境安全检查失败**
```bash
# 检查 SECRET_KEY 是否为生产环境专用
# 检查 DEBUG 是否关闭
```

**问题：数据库连接失败**
```bash
# 检查数据库配置是否正确
# 检查数据库服务是否运行
# 检查网络连通性
```

### 调试模式

```bash
# 开启详细日志
export DISCOVERY_LOG_LEVEL=DEBUG

# 查看环境信息
python -c "
from app.settings.env_manager import env_manager
env_manager.print_env_info()
"
```

---

# 🎉 CMDB发现系统完整实现

## 📋 系统概述

基于现有配置基础，CMDB发现系统已完整实现，提供从Zabbix和Prometheus自动发现配置项并同步到数据库的完整解决方案。

## ✅ 已实现组件

### 1. 数据模型 (`app/models/cmdb.py`)
- **ConfigurationItem**: 配置项核心模型，存储发现的所有资源
- **DiscoverySource**: 数据源配置，管理Prometheus/Zabbix连接
- **DiscoveryRule**: 发现规则，定义自动发现策略
- **DiscoveryJob**: 发现任务记录，跟踪执行状态
- **DiscoveryAuditLog**: 审计日志，记录所有变更操作

### 2. 发现引擎 (`app/discovery/engine.py`)
- **CMDBDiscoveryEngine**: 统一发现调度引擎
- **数据处理器**: Zabbix/Prometheus数据标准化
- **同步引擎**: 智能配置项入库和变更检测

### 3. 异步任务系统 (`app/discovery/tasks.py`)
- **run_discovery_job**: 异步发现任务执行
- **定时任务**: Prometheus(30分钟)/Zabbix(1小时)自动发现
- **health_check_task**: 数据源健康状态监控
- **cleanup_old_jobs**: 自动清理历史任务记录

### 4. REST API (`app/api/v1/discovery.py`)
- `/api/v1/discovery/health`: 系统健康检查
- `/api/v1/discovery/jobs`: 发现任务管理
- `/api/v1/discovery/sources`: 数据源配置管理
- `/api/v1/discovery/configuration-items`: 配置项查询
- `/api/v1/discovery/statistics`: 发现统计报表

### 5. 初始化脚本 (`app/scripts/init_cmdb_discovery.py`)
- 自动创建数据库表结构
- 初始化Prometheus和Zabbix数据源
- 创建默认发现规则
- 测试数据源连接
- 执行初始发现任务

## 🚀 快速部署

### 1. 系统初始化
```bash
# 确保环境配置正确
python app/scripts/setup_env.py --env development --validate

# 初始化CMDB发现系统
python app/scripts/init_cmdb_discovery.py
```

### 2. 启动服务
```bash
# 启动FastAPI应用
uvicorn app.run:app --host 0.0.0.0 --port 9999 --reload

# 启动Celery Worker (异步任务)
celery -A app.core.celery_app worker --loglevel=info --queues=discovery,monitoring

# 启动Celery Beat (定时任务)
celery -A app.core.celery_app beat --loglevel=info
```

### 3. 系统验证
```bash
# 健康检查
curl http://localhost:9999/api/v1/discovery/health

# 查看API文档
open http://localhost:9999/docs

# 查看配置项
curl "http://localhost:9999/api/v1/discovery/configuration-items?page=1&page_size=10"
```

## 📊 核心功能

### ✅ 已实现功能
- **多数据源支持**: 完整的Prometheus和Zabbix集成
- **智能数据映射**: 自动识别配置项类型和属性
- **增量同步**: 基于数据哈希的智能变更检测
- **异步任务**: Celery驱动的高性能异步处理
- **定时发现**: 自动化的定期数据同步
- **完整审计**: 所有操作的详细审计日志
- **健康监控**: 实时监控数据源连接状态
- **REST API**: 完整的RESTful API接口
- **统计报表**: 丰富的发现统计和分析

### 🔄 数据流程
1. **数据收集**: 从配置的Prometheus/Zabbix获取原始数据
2. **数据处理**: 标准化格式并自动识别配置项类型
3. **变更检测**: 通过数据哈希智能检测数据变更
4. **数据入库**: 创建新配置项或更新现有配置项
5. **审计记录**: 记录完整的变更历史和操作日志

## 🔧 配置说明

系统使用现有的环境配置，关键配置项：

```env
# Prometheus配置
PROMETHEUS_URL=http://*************:31891
PROMETHEUS_TIMEOUT=30

# Zabbix配置
ZABBIX_URL=http://************/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=niuyeji
ZABBIX_PASSWORD=Changjiu123!

# Redis配置 (Celery消息队列)
REDIS_URL=redis://:CjK1rry1as@************:6379/0

# 数据库配置
DB_HOST=************
DB_USER=backup_user
DB_PASSWORD=v+SCbs/6LsYNovFngEY=
DB_NAME=fastapi_user
```

## 📈 使用示例

### API调用示例
```bash
# 1. 创建手动发现任务
curl -X POST http://localhost:9999/api/v1/discovery/jobs \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "job_name": "手动Prometheus发现",
    "description": "测试手动发现功能", 
    "source_id": 1,
    "force_sync": true
  }'

# 2. 查看物理服务器配置项
curl "http://localhost:9999/api/v1/discovery/configuration-items?ci_type=PHYSICAL_SERVER&page=1&page_size=20"

# 3. 获取发现统计信息
curl http://localhost:9999/api/v1/discovery/statistics

# 4. 测试数据源连接
curl -X POST http://localhost:9999/api/v1/discovery/sources/1/test
```

### Python代码示例
```python
# 使用发现引擎
from app.discovery.engine import create_discovery_engine
from app.settings.config import get_discovery_config

async def run_custom_discovery():
    config = get_discovery_config()
    engine = create_discovery_engine(config)
    
    # 执行发现
    results = await engine.run_discovery(["prometheus", "zabbix"])
    
    for source_name, result in results.items():
        print(f"{source_name}: {result.status}, "
              f"发现{result.successful_items}个配置项")
```

## 🎯 业务价值

### 🔍 自动化发现
- **减少手工维护**: 自动发现和同步基础设施信息
- **实时更新**: 定期同步确保配置数据的时效性
- **全面覆盖**: 同时支持监控系统和主机清单

### 📊 统一视图
- **配置项管理**: 统一管理所有基础设施配置项
- **关系追踪**: 建立配置项之间的依赖关系
- **变更审计**: 完整的配置变更历史记录

### 🔧 运维支撑
- **故障定位**: 快速查找相关配置信息
- **容量规划**: 基于实际配置数据进行容量分析
- **合规审计**: 满足IT治理和审计要求

## 🚀 下一步扩展

系统已提供良好的扩展接口，可继续扩展：

1. **新数据源**: 添加其他监控系统或CMDB集成
2. **高级规则**: 实现更复杂的发现和映射规则
3. **关系发现**: 自动发现配置项之间的依赖关系
4. **告警集成**: 与监控告警系统集成
5. **可视化**: 配置项拓扑图和关系图展示

## 📝 总结

CMDB发现系统现已完整实现并可投入生产使用。系统具备：

- ✅ **完整的数据模型和API**
- ✅ **强大的异步处理能力**
- ✅ **灵活的配置和扩展机制**
- ✅ **完善的监控和审计功能**
- ✅ **便捷的部署和维护工具**

系统已集成到现有的环境配置体系中，可以与其他模块无缝协作，为企业IT资产管理提供坚实的数据基础。 