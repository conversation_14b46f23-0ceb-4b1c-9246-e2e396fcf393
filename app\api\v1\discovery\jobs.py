# -*- coding: utf-8 -*-
"""
CMDB发现任务管理API

提供发现任务的创建、查询、管理接口
"""

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from typing import Optional
from datetime import datetime
import logging

from app.models.cmdb import DiscoveryJob, DiscoverySource, JobStatusEnum
from app.schemas.discovery import DiscoveryJobCreate, BatchDiscoveryRequest
from app.discovery.tasks import run_discovery_job
from app.core.response import success
from app.core.dependency import AuthControl
from app.utils.pagination import paginate

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("", summary="创建发现任务")
async def create_discovery_job(
    job_data: DiscoveryJobCreate,
    background_tasks: BackgroundTasks,
    current_user=Depends(AuthControl.is_authed)
):
    """创建新的发现任务"""
    try:
        # 获取数据源
        discovery_source = await DiscoverySource.get_or_none(id=job_data.source_id)
        if not discovery_source:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        if not discovery_source.is_enabled:
            raise HTTPException(status_code=400, detail="数据源未启用")
        
        # 创建任务
        job = await DiscoveryJob.create(
            job_name=job_data.job_name,
            description=job_data.description,
            discovery_source=discovery_source,
            job_config={
                "force_sync": job_data.force_sync,
                "created_by": current_user.username
            }
        )
        
        # 异步执行任务
        background_tasks.add_task(run_discovery_job.delay, job.id, job_data.force_sync)
        
        return success(data={
            "job_id": job.id,
            "job_name": job.job_name,
            "status": job.status,
            "message": "发现任务已创建并开始执行"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建发现任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")


@router.get("", summary="获取发现任务列表")
async def get_discovery_jobs(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[JobStatusEnum] = None,
    source_id: Optional[int] = None
):
    """获取发现任务列表"""
    try:
        query = DiscoveryJob.all()
        
        # 添加过滤条件
        if status:
            query = query.filter(status=status)
        if source_id:
            query = query.filter(discovery_source_id=source_id)
        
        # 排序
        query = query.order_by("-created_at")
        
        # 分页
        result = await paginate(query, page, page_size)
        
        # 序列化数据
        jobs_data = []
        for job in result["items"]:
            job_data = {
                "id": job.id,
                "job_name": job.job_name,
                "description": job.description,
                "status": job.status,
                "progress": job.progress,
                "total_items": job.total_items,
                "successful_items": job.successful_items,
                "failed_items": job.failed_items,
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "execution_duration": job.execution_duration,
                "created_at": job.created_at.isoformat()
            }
            jobs_data.append(job_data)
        
        return success(data={
            "items": jobs_data,
            "total": result["total"],
            "page": page,
            "page_size": page_size,
            "total_pages": result["total_pages"]
        })
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.get("/{job_id}", summary="获取任务详情")
async def get_discovery_job(job_id: int):
    """获取发现任务详情"""
    try:
        job = await DiscoveryJob.get_or_none(id=job_id).prefetch_related("discovery_source")
        if not job:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return success(data={
            "id": job.id,
            "job_name": job.job_name,
            "description": job.description,
            "status": job.status,
            "progress": job.progress,
            "source": {
                "id": job.discovery_source.id,
                "name": job.discovery_source.name,
                "type": job.discovery_source.source_type
            },
            "execution": {
                "started_at": job.started_at.isoformat() if job.started_at else None,
                "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                "duration": job.execution_duration
            },
            "results": {
                "total_items": job.total_items,
                "processed_items": job.processed_items,
                "successful_items": job.successful_items,
                "failed_items": job.failed_items,
                "skipped_items": job.skipped_items
            },
            "summary": job.result_summary,
            "errors": job.error_details,
            "created_at": job.created_at.isoformat()
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务详情失败: {str(e)}")


@router.post("/batch-discovery", summary="批量发现")
async def batch_discovery(
    request: BatchDiscoveryRequest,
    background_tasks: BackgroundTasks,
    current_user=Depends(AuthControl.is_authed)
):
    """批量执行发现任务"""
    try:
        # 验证数据源
        sources = await DiscoverySource.filter(
            id__in=request.source_ids,
            is_enabled=True
        ).all()
        
        if len(sources) != len(request.source_ids):
            raise HTTPException(status_code=400, detail="部分数据源不存在或未启用")
        
        created_jobs = []
        
        for source in sources:
            job = await DiscoveryJob.create(
                job_name=f"批量发现-{source.name}-{datetime.now().strftime('%Y%m%d_%H%M')}",
                description=request.description or f"批量发现任务 - {source.display_name}",
                discovery_source=source,
                job_config={
                    "force_sync": request.force_sync,
                    "batch_discovery": True,
                    "created_by": current_user.username
                }
            )
            created_jobs.append(job)
            
            # 异步执行任务
            if request.parallel:
                background_tasks.add_task(run_discovery_job.delay, job.id, request.force_sync)
            else:
                # 串行执行需要等待前一个任务完成，这里简化处理
                background_tasks.add_task(run_discovery_job.delay, job.id, request.force_sync)
        
        return success(data={
            "created_jobs": [
                {
                    "job_id": job.id,
                    "job_name": job.job_name,
                    "source_name": job.discovery_source.name
                }
                for job in created_jobs
            ],
            "total_jobs": len(created_jobs),
            "message": f"已创建 {len(created_jobs)} 个批量发现任务"
        })
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量发现失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量发现失败: {str(e)}") 