# -*- coding: utf-8 -*-
"""
CMDB发现功能相关的Pydantic模型

定义数据源配置、发现规则、发现任务等的请求和响应模型。
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class SourceType(str, Enum):
    """数据源类型枚举"""
    ZABBIX = "zabbix"
    PROMETHEUS = "prometheus"
    SNMP = "snmp"
    API = "api"
    AGENT = "agent"


class DiscoveryMethod(str, Enum):
    """发现方法枚举"""
    API_QUERY = "api_query"
    SNMP_SCAN = "snmp_scan"
    AGENT_REPORT = "agent_report"
    LOG_PARSE = "log_parse"


class JobStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "等待"
    RUNNING = "运行中"
    COMPLETED = "已完成"
    FAILED = "失败"
    CANCELLED = "已取消"


class SyncStatus(str, Enum):
    """同步状态枚举"""
    SUCCESS = "成功"
    FAILED = "失败"
    IN_PROGRESS = "进行中"


# ========== 数据源相关模型 ==========

class DiscoverySourceBase(BaseModel):
    """数据源基础模型"""
    source_name: str = Field(..., min_length=1, max_length=50, description="数据源名称")
    source_type: SourceType = Field(..., description="数据源类型")
    endpoint_url: str = Field(..., min_length=1, max_length=200, description="数据源地址")
    auth_config: Optional[Dict[str, Any]] = Field(None, description="认证配置")
    sync_interval: int = Field(300, ge=60, le=86400, description="同步间隔(秒)")
    is_enabled: bool = Field(True, description="是否启用")
    description: Optional[str] = Field(None, max_length=500, description="描述")

    @validator('auth_config')
    def validate_auth_config(cls, v, values):
        """验证认证配置"""
        if v is None:
            return v
        
        source_type = values.get('source_type')
        if source_type == SourceType.ZABBIX:
            required_fields = ['username', 'password']
        elif source_type == SourceType.PROMETHEUS:
            required_fields = []  # Prometheus通常不需要认证
        else:
            required_fields = []
        
        for field in required_fields:
            if field not in v:
                raise ValueError(f"认证配置缺少必需字段: {field}")
        
        return v


class DiscoverySourceCreate(DiscoverySourceBase):
    """创建数据源请求模型"""
    pass


class DiscoverySourceUpdate(BaseModel):
    """更新数据源请求模型"""
    source_name: Optional[str] = Field(None, min_length=1, max_length=50)
    endpoint_url: Optional[str] = Field(None, min_length=1, max_length=200)
    auth_config: Optional[Dict[str, Any]] = None
    sync_interval: Optional[int] = Field(None, ge=60, le=86400)
    is_enabled: Optional[bool] = None
    description: Optional[str] = Field(None, max_length=500)


class DiscoverySourceResponse(DiscoverySourceBase):
    """数据源响应模型"""
    id: int
    last_sync_time: Optional[datetime] = None
    sync_status: SyncStatus = SyncStatus.SUCCESS
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# ========== 发现规则相关模型 ==========

class DiscoveryRuleBase(BaseModel):
    """发现规则基础模型"""
    rule_name: str = Field(..., min_length=1, max_length=100, description="规则名称")
    source_id: int = Field(..., description="数据源ID")
    ci_type_id: int = Field(..., description="目标CI类型ID")
    discovery_method: DiscoveryMethod = Field(..., description="发现方法")
    query_config: Dict[str, Any] = Field(..., description="查询配置")
    mapping_config: Dict[str, Any] = Field(..., description="字段映射配置")
    filter_config: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    schedule_config: Optional[Dict[str, Any]] = Field(None, description="调度配置")
    is_active: bool = Field(True, description="是否活跃")
    description: Optional[str] = Field(None, max_length=500, description="描述")

    @validator('query_config')
    def validate_query_config(cls, v):
        """验证查询配置"""
        if not isinstance(v, dict) or len(v) == 0:
            raise ValueError("查询配置不能为空")
        return v

    @validator('mapping_config')
    def validate_mapping_config(cls, v):
        """验证映射配置"""
        if not isinstance(v, dict) or len(v) == 0:
            raise ValueError("字段映射配置不能为空")
        
        # 检查必需的映射字段
        required_mappings = ['name', 'ci_type_code']
        for field in required_mappings:
            if field not in v:
                raise ValueError(f"映射配置缺少必需字段: {field}")
        
        return v


class DiscoveryRuleCreate(DiscoveryRuleBase):
    """创建发现规则请求模型"""
    pass


class DiscoveryRuleUpdate(BaseModel):
    """更新发现规则请求模型"""
    rule_name: Optional[str] = Field(None, min_length=1, max_length=100)
    ci_type_id: Optional[int] = None
    discovery_method: Optional[DiscoveryMethod] = None
    query_config: Optional[Dict[str, Any]] = None
    mapping_config: Optional[Dict[str, Any]] = None
    filter_config: Optional[Dict[str, Any]] = None
    schedule_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    description: Optional[str] = Field(None, max_length=500)


class DiscoveryRuleResponse(DiscoveryRuleBase):
    """发现规则响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    # 关联信息
    source_name: Optional[str] = None
    ci_type_name: Optional[str] = None
    last_execution: Optional[datetime] = None
    execution_count: int = 0

    class Config:
        from_attributes = True


# ========== 发现任务相关模型 ==========

class DiscoveryJobBase(BaseModel):
    """发现任务基础模型"""
    job_name: str = Field(..., min_length=1, max_length=100, description="任务名称")
    description: Optional[str] = Field(None, max_length=500, description="任务描述")


class DiscoveryJobCreate(DiscoveryJobBase):
    """创建发现任务请求模型"""
    rule_ids: List[int] = Field(..., min_items=1, description="发现规则ID列表")
    force_sync: bool = Field(False, description="是否强制同步")


class DiscoveryJobResponse(DiscoveryJobBase):
    """发现任务响应模型"""
    id: int
    rule_id: Optional[int] = None
    status: JobStatus
    progress: int = Field(0, ge=0, le=100, description="执行进度")
    
    # 执行信息
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    
    # 结果信息
    result_summary: Optional[Dict[str, Any]] = None
    error_details: Optional[Dict[str, Any]] = None
    log_file_path: Optional[str] = None
    
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class DiscoveryJobDetails(DiscoveryJobResponse):
    """发现任务详细信息模型"""
    # 包含同步操作历史
    sync_history: List[Dict[str, Any]] = Field(default_factory=list)
    
    # 执行日志摘要
    recent_logs: List[str] = Field(default_factory=list)
    
    # 性能指标
    execution_metrics: Optional[Dict[str, Any]] = None


# ========== 连接测试相关模型 ==========

class ConnectionTestResponse(BaseModel):
    """连接测试响应模型"""
    success: bool = Field(..., description="连接是否成功")
    message: str = Field(..., description="测试结果消息")
    response_time: Optional[float] = Field(None, description="响应时间(秒)")
    version: Optional[str] = Field(None, description="目标系统版本")
    additional_info: Optional[Dict[str, Any]] = Field(None, description="附加信息")
    timestamp: datetime = Field(..., description="测试时间")


# ========== 统计信息相关模型 ==========

class SourceStatistics(BaseModel):
    """数据源统计信息"""
    source_id: int
    source_name: str
    source_type: SourceType
    ci_count: int = 0
    last_sync_time: Optional[datetime] = None
    sync_success_rate: float = 0.0
    avg_sync_duration: Optional[float] = None


class DiscoveryStatistics(BaseModel):
    """发现统计信息"""
    total_jobs: int = 0
    successful_jobs: int = 0
    failed_jobs: int = 0
    avg_execution_time: Optional[float] = None
    last_execution: Optional[datetime] = None


class DiscoveryStatisticsResponse(BaseModel):
    """发现统计响应模型"""
    # 总体统计
    total_cis: int = 0
    total_sources: int = 0
    active_rules: int = 0
    
    # 时间范围内的统计
    time_range: str
    period_jobs: int = 0
    period_success_rate: float = 0.0
    
    # 各数据源统计
    source_statistics: List[SourceStatistics] = Field(default_factory=list)
    
    # 发现任务统计
    discovery_statistics: DiscoveryStatistics
    
    # CI类型分布
    ci_type_distribution: Dict[str, int] = Field(default_factory=dict)
    
    # 错误统计
    error_summary: Dict[str, int] = Field(default_factory=dict)
    
    # 更新时间
    updated_at: datetime


# ========== 健康检查相关模型 ==========

class SourceHealthStatus(BaseModel):
    """数据源健康状态"""
    source_id: int
    source_name: str
    is_healthy: bool
    last_check: datetime
    error_message: Optional[str] = None
    response_time: Optional[float] = None


class ServiceHealthResponse(BaseModel):
    """服务健康检查响应模型"""
    overall_health: bool = Field(..., description="总体健康状态")
    
    # 数据源健康状态
    sources_health: List[SourceHealthStatus] = Field(default_factory=list)
    
    # 系统资源状态
    system_metrics: Dict[str, Any] = Field(default_factory=dict)
    
    # 最近错误统计
    recent_errors: Dict[str, int] = Field(default_factory=dict)
    
    # 任务队列状态
    queue_status: Dict[str, Any] = Field(default_factory=dict)
    
    # 检查时间
    check_time: datetime


# ========== CI类型相关模型 ==========

class SupportedCIType(BaseModel):
    """支持的CI类型模型"""
    id: int
    code: str = Field(..., description="CI类型代码")
    name: str = Field(..., description="CI类型名称")
    category: str = Field(..., description="CI分类")
    description: Optional[str] = None
    icon: Optional[str] = None
    is_discoverable: bool = True


class SupportedCITypesResponse(BaseModel):
    """支持的CI类型响应模型"""
    ci_types: List[SupportedCIType] = Field(default_factory=list)
    categories: List[str] = Field(default_factory=list)


# ========== 同步历史相关模型 ==========

class SyncHistoryItem(BaseModel):
    """同步历史项模型"""
    id: int
    ci_id: str
    action_type: str = Field(..., description="操作类型")
    sync_status: str = Field(..., description="同步状态")
    source_data: Optional[Dict[str, Any]] = None
    existing_data: Optional[Dict[str, Any]] = None
    changes_detected: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    synced_at: datetime

    class Config:
        from_attributes = True


class SyncHistoryResponse(BaseModel):
    """同步历史响应模型"""
    items: List[SyncHistoryItem] = Field(default_factory=list)
    total: int = 0
    skip: int = 0
    limit: int = 100


# ========== 批量操作相关模型 ==========

class BatchDiscoveryRequest(BaseModel):
    """批量发现请求模型"""
    source_ids: List[int] = Field(..., min_items=1, description="数据源ID列表")
    force_sync: bool = Field(False, description="是否强制同步")
    parallel: bool = Field(True, description="是否并行执行")
    description: Optional[str] = Field(None, description="批量任务描述")


class BatchDiscoveryResponse(BaseModel):
    """批量发现响应模型"""
    batch_id: str = Field(..., description="批量任务ID")
    job_ids: List[int] = Field(..., description="创建的任务ID列表")
    total_jobs: int = Field(..., description="总任务数")
    message: str = Field(..., description="响应消息") 