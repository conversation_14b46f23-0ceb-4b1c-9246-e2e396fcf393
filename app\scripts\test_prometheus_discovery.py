#!/usr/bin/env python3
"""
单独测试Prometheus发现功能

测试Prometheus连接器的目标发现和服务发现功能
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from app.settings.config import get_prometheus_config
from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector


async def test_prometheus_targets():
    """测试Prometheus目标发现"""
    print("=== 测试Prometheus目标发现 ===")
    
    try:
        config = get_prometheus_config()
        connector = PrometheusDiscoveryConnector(config)
        
        print(f"Prometheus URL: {config['url']}")
        print(f"启用的Jobs: {config['enabled_jobs']}")
        print(f"排除的Jobs: {config['excluded_jobs']}")
        
        # 发现目标
        targets = await connector.discover_targets()
        
        print(f"\n发现了 {len(targets)} 个监控目标:")
        
        # 按job分组统计
        job_stats = {}
        for target in targets:
            job = target.job
            if job not in job_stats:
                job_stats[job] = {"total": 0, "up": 0, "down": 0}
            
            job_stats[job]["total"] += 1
            if target.health == "up":
                job_stats[job]["up"] += 1
            else:
                job_stats[job]["down"] += 1
        
        print("\n按Job分组统计:")
        for job, stats in job_stats.items():
            print(f"  {job}: {stats['total']} 个目标 (运行: {stats['up']}, 故障: {stats['down']})")
        
        # 显示前5个目标的详细信息
        print("\n前5个目标详细信息:")
        for i, target in enumerate(targets[:5]):
            print(f"  {i+1}. Job: {target.job}")
            print(f"     Instance: {target.instance}")
            print(f"     Health: {target.health}")
            print(f"     Labels: {dict(list(target.labels.items())[:3])}...")
            print()
        
        return targets
        
    except Exception as e:
        print(f"❌ 目标发现失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


async def test_prometheus_services():
    """测试Prometheus服务发现"""
    print("=== 测试Prometheus服务发现 ===")
    
    try:
        config = get_prometheus_config()
        connector = PrometheusDiscoveryConnector(config)
        
        # 发现服务
        services = await connector.discover_services()
        
        print(f"\n发现了 {len(services)} 个服务:")
        
        for i, service in enumerate(services[:10]):  # 只显示前10个
            print(f"  {i+1}. 服务名: {service.service_name}")
            print(f"     Job: {service.job}")
            print(f"     实例数: {len(service.instances)}")
            print(f"     健康状态: {service.health_status}")
            print(f"     标签: {dict(list(service.labels.items())[:2])}...")
            print()
        
        return services
        
    except Exception as e:
        print(f"❌ 服务发现失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return []


async def test_data_mapping():
    """测试数据映射功能"""
    print("=== 测试数据映射功能 ===")
    
    try:
        config = get_prometheus_config()
        connector = PrometheusDiscoveryConnector(config)
        
        # 获取几个目标进行映射测试
        targets = await connector.discover_targets()
        
        if targets:
            from app.discovery.connectors.prometheus_connector import PrometheusDataMapper
            
            print(f"测试前3个目标的CI映射:")
            for i, target in enumerate(targets[:3]):
                print(f"\n目标 {i+1}: {target.job} - {target.instance}")
                
                try:
                    ci_data = PrometheusDataMapper.map_target_to_ci(target)
                    print(f"  CI ID: {ci_data['id']}")
                    print(f"  CI类型: {ci_data['ci_type_code']}")
                    print(f"  显示名称: {ci_data['display_name']}")
                    print(f"  运行状态: {ci_data['operational_status']}")
                    print(f"  IP地址: {ci_data['ip_addresses']}")
                    print(f"  标签数: {len(ci_data['tags'])}")
                    
                except Exception as e:
                    print(f"  ❌ 映射失败: {str(e)}")
        
        # 测试服务映射
        services = await connector.discover_services()
        if services:
            print(f"\n测试前2个服务的CI映射:")
            for i, service in enumerate(services[:2]):
                print(f"\n服务 {i+1}: {service.service_name}")
                
                try:
                    from app.discovery.connectors.prometheus_connector import PrometheusDataMapper
                    ci_data = PrometheusDataMapper.map_service_to_ci(service)
                    print(f"  CI ID: {ci_data['id']}")
                    print(f"  CI类型: {ci_data['ci_type_code']}")
                    print(f"  显示名称: {ci_data['display_name']}")
                    print(f"  实例数: {ci_data['custom_attributes']['instance_count']}")
                    
                except Exception as e:
                    print(f"  ❌ 映射失败: {str(e)}")
        
    except Exception as e:
        print(f"❌ 数据映射测试失败: {str(e)}")


async def main():
    """主测试函数"""
    print("Prometheus发现功能单独测试")
    print("=" * 50)
    
    # 测试目标发现
    targets = await test_prometheus_targets()
    
    # 测试服务发现
    services = await test_prometheus_services()
    
    # 测试数据映射
    await test_data_mapping()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结:")
    print(f"  发现目标数: {len(targets)}")
    print(f"  发现服务数: {len(services)}")
    
    if targets or services:
        print("\n✅ Prometheus发现功能测试通过！")
        return 0
    else:
        print("\n❌ Prometheus发现功能存在问题")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 