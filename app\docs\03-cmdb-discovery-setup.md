# CMDB 自动发现功能配置指南

## 概述

CMDB自动发现功能能够从多种监控和管理工具中自动收集基础设施和应用程序的配置信息，并将其同步到配置管理数据库中。系统目前支持从Prometheus和Zabbix自动发现和同步配置项。

## 架构设计

```
数据源层           发现引擎层           处理层              存储层
┌─────────────┐   ┌─────────────┐   ┌─────────────┐   ┌─────────────┐
│ Prometheus  │   │             │   │   数据处理   │   │             │
│   - Targets │◄──┤  发现引擎    │◄──┤   标准化    │◄──┤   CMDB      │
│   - Services│   │             │   │   映射      │   │   数据库     │
└─────────────┘   │             │   └─────────────┘   └─────────────┘
┌─────────────┐   │             │   ┌─────────────┐
│   Zabbix    │   │             │   │   同步引擎   │
│   - Hosts   │◄──┤             │◄──┤   冲突处理   │
│   - Groups  │   │             │   │   变更检测   │
└─────────────┘   └─────────────┘   └─────────────┘
```

## 功能特性

### 🔍 多数据源支持
- **Prometheus**: 自动发现监控目标和服务
- **Zabbix**: 发现主机和主机组信息
- **扩展性**: 可轻松添加新的数据源连接器

### 🔄 智能数据处理
- **数据标准化**: 将不同数据源的数据转换为统一格式
- **CI类型识别**: 自动识别配置项类型（服务器、服务、容器等）
- **关系映射**: 处理配置项之间的依赖关系

### ⚡ 异步处理
- **Celery任务队列**: 支持大规模并发发现任务
- **实时监控**: 任务状态和进度实时跟踪
- **错误恢复**: 自动重试和故障处理

### 📊 完整监控
- **健康检查**: 数据源连接状态监控
- **统计报告**: 发现结果和性能指标
- **日志审计**: 详细的操作日志记录

## 快速开始

### 1. 环境配置

创建环境变量文件 `.env`：

```bash
# Prometheus 配置
PROMETHEUS_URL=http://*************:31891
PROMETHEUS_TIMEOUT=30

# Zabbix 配置  
ZABBIX_URL=http://************/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=niuyeji
ZABBIX_PASSWORD=Changjiu123!

# Redis 配置 (用于Celery)
REDIS_URL=redis://:CjK1rry1as@************:6379/0
CELERY_BROKER_URL=redis://:CjK1rry1as@************:6379/0
CELERY_RESULT_BACKEND=redis://:CjK1rry1as@************:6379/0

# 发现引擎配置
DISCOVERY_ENABLED=true
DISCOVERY_MAX_CONCURRENT_JOBS=5
DISCOVERY_JOB_TIMEOUT=1800
DISCOVERY_RETRY_ATTEMPTS=3
```

### 2. 初始化系统

运行初始化脚本：

```bash
python app/scripts/init_discovery.py
```

这将：
- 创建默认数据源配置
- 测试所有数据源连接
- 设置基本发现规则
- 运行初始发现任务

### 3. 测试连接

验证所有组件连接：

```bash
python app/scripts/test_discovery_config.py
```

预期结果：
```
✅ 4/4 项测试全部通过:
- Prometheus连接: 成功连接，发现68个监控目标
- Redis连接: 成功连接，读写操作正常
- Zabbix连接: 成功连接，发现36个主机组
- 发现引擎: 成功创建，注册了2个连接器
```

## 详细配置

### Prometheus 配置

在 `app/settings/config.py` 中配置：

```python
class Settings(BaseSettings):
    # Prometheus配置
    PROMETHEUS_URL: str = "http://*************:31891"
    PROMETHEUS_TIMEOUT: int = 30
    PROMETHEUS_ENABLED_JOBS: List[str] = [
        "node-exporter",
        "kubernetes-pods", 
        "kubernetes-nodes",
        "blackbox",
        "mysql-exporter",
        "redis-exporter"
    ]
    PROMETHEUS_EXCLUDED_JOBS: List[str] = ["prometheus"]
    PROMETHEUS_SYNC_INTERVAL: int = 1800  # 30分钟
```

### 服务发现规则

定义如何识别和命名不同类型的服务：

```python
SERVICE_DISCOVERY_RULES: Dict = {
    "kubernetes": {
        "job_pattern": "kubernetes",
        "name_template": "{job}-{namespace}",
        "ci_type": "CONTAINER_SERVICE"
    },
    "mysql": {
        "job_pattern": "mysql",
        "name_template": "MySQL-{instance}",
        "ci_type": "DATABASE_SERVICE"
    },
    "redis": {
        "job_pattern": "redis", 
        "name_template": "Redis-{instance}",
        "ci_type": "CACHE_SERVICE"
    },
    "node": {
        "job_pattern": "node",
        "name_template": "Node-{instance}",
        "ci_type": "PHYSICAL_SERVER"
    }
}
```

### Zabbix 配置

```python
class Settings(BaseSettings):
    # Zabbix配置
    ZABBIX_URL: str = "http://************/zabbix/api_jsonrpc.php"
    ZABBIX_USERNAME: str = "niuyeji"
    ZABBIX_PASSWORD: str = "Changjiu123!"
    ZABBIX_ENABLED_GROUPS: List[str] = [
        "Linux servers",
        "Windows servers", 
        "Network equipment",
        "Virtual machines"
    ]
    ZABBIX_EXCLUDED_HOSTS: List[str] = ["test-host", "demo-host"]
    ZABBIX_SYNC_INTERVAL: int = 3600  # 1小时
```

## API 使用指南

### 启动服务

1. **启动FastAPI应用**：
```bash
uvicorn app.run:app --host 0.0.0.0 --port 9999 --reload
```

2. **启动Celery Worker**：
```bash
celery -A app.celery worker --loglevel=info
```

3. **启动定时任务**：
```bash
celery -A app.celery beat --loglevel=info
```

### 核心API端点

#### 1. 健康检查
```bash
curl http://localhost:9999/api/v1/discovery/health
```

响应示例：
```json
{
  "status": "healthy",
  "data_sources": {
    "prometheus": {"status": "connected", "targets": 68},
    "zabbix": {"status": "connected", "hosts": 177}
  },
  "engine": {"status": "ready", "connectors": 2}
}
```

#### 2. 创建发现任务
```bash
curl -X POST http://localhost:9999/api/v1/discovery/jobs \
  -H "Content-Type: application/json" \
  -d '{
    "name": "全量发现",
    "source_names": ["prometheus", "zabbix"],
    "discovery_mode": "full"
  }'
```

#### 3. 查询任务状态
```bash
curl http://localhost:9999/api/v1/discovery/jobs/{job_id}
```

#### 4. 数据源管理
```bash
# 查看所有数据源
curl http://localhost:9999/api/v1/discovery/sources

# 测试数据源连接
curl -X POST http://localhost:9999/api/v1/discovery/sources/{source_id}/test
```

#### 5. 统计信息
```bash
curl http://localhost:9999/api/v1/discovery/statistics
```

## 运维管理

### 监控和日志

#### 查看发现日志
```bash
tail -f app/logs/discovery.log
```

#### 监控Celery任务
```bash
# 查看Celery worker状态
celery -A app.celery inspect active

# 查看任务队列状态
celery -A app.celery inspect reserved
```

#### Redis监控
```bash
# 连接Redis查看任务队列
redis-cli -h ************ -p 6379 -a CjK1rry1as
> KEYS celery*
> LLEN celery
```

### 性能调优

#### Celery并发配置
```bash
# 设置worker并发数
celery -A app.celery worker --concurrency=8

# 设置内存限制
celery -A app.celery worker --max-memory-per-child=200000
```

#### 发现频率配置
```python
# 在 app/settings/config.py 中调整
PROMETHEUS_SYNC_INTERVAL: int = 1800  # 30分钟
ZABBIX_SYNC_INTERVAL: int = 3600      # 1小时
DISCOVERY_MAX_CONCURRENT_JOBS: int = 5
```

### 故障排除

#### 常见问题

1. **Prometheus连接失败**
```bash
# 检查网络连通性
curl http://*************:31891/api/v1/targets

# 检查配置
python -c "from app.settings.config import get_prometheus_config; print(get_prometheus_config())"
```

2. **Zabbix认证失败**
```bash
# 验证凭据
python app/scripts/test_discovery_config.py
```

3. **Redis连接问题**
```bash
# 测试Redis连接
redis-cli -h ************ -p 6379 -a CjK1rry1as ping
```

4. **任务卡死问题**
```bash
# 清理僵尸任务
celery -A app.celery purge

# 重启worker
celery -A app.celery control shutdown
celery -A app.celery worker --loglevel=info
```

## 数据模型映射

### Prometheus 数据映射

| Prometheus字段 | CMDB字段 | 说明 |
|---------------|----------|------|
| job | prometheus_job | 监控任务名称 |
| instance | asset_tag | 实例标识 |
| labels.instance | hostname | 主机名 |
| health | operational_status | 运行状态 |
| scrape_url | custom_attributes | 抓取URL |

### Zabbix 数据映射

| Zabbix字段 | CMDB字段 | 说明 |
|-----------|----------|------|
| host | name | 主机名 |
| interfaces.ip | ip_addresses | IP地址列表 |
| inventory.os | operating_system | 操作系统 |
| inventory.hardware | hardware_specifications | 硬件规格 |
| groups | tags | 主机组标签 |

## 扩展开发

### 添加新数据源

1. **创建连接器**：
```python
class CustomConnector:
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    async def discover_items(self) -> List[Any]:
        # 实现发现逻辑
        pass
    
    async def test_connection(self) -> Dict[str, Any]:
        # 实现连接测试
        pass
```

2. **创建数据处理器**：
```python
class CustomProcessor(DataProcessor):
    def normalize_data(self, raw_data: Any, source_type: str) -> Dict[str, Any]:
        # 实现数据标准化
        pass
```

3. **注册到引擎**：
```python
# 在 create_discovery_engine 中添加
engine.register_connector("custom", CustomConnector, config["custom"])
engine.processors["custom"] = CustomProcessor(engine)
```

### 自定义CI类型

在配置中定义新的CI类型映射：

```python
CI_TYPE_MAPPING = {
    "DATABASE_SERVER": "数据库服务器",
    "WEB_SERVER": "Web服务器", 
    "LOAD_BALANCER": "负载均衡器",
    "STORAGE_DEVICE": "存储设备"
}
```

## 最佳实践

### 1. 性能优化
- 合理设置发现频率，避免过于频繁
- 使用Redis集群提高任务队列性能
- 监控内存使用，及时调整worker数量

### 2. 数据质量
- 定期验证数据源连接状态
- 设置数据验证规则
- 建立数据清理策略

### 3. 安全考虑
- 使用环境变量存储敏感信息
- 定期更新API密钥和密码
- 限制网络访问权限

### 4. 监控告警
- 设置发现任务失败告警
- 监控数据源健康状态
- 建立运维响应流程

## 总结

CMDB自动发现功能为运维团队提供了强大的基础设施管理能力，通过自动化的方式确保配置数据的准确性和时效性。系统的模块化设计使其易于扩展和维护，能够适应不断变化的基础设施环境。

更多技术细节和API文档请参考：
- [FastAPI自动生成文档](http://localhost:9999/docs)
- [配置详情文档](04-cmdb-discovery-config-summary.md)
- [项目根目录README](../../README.md) 