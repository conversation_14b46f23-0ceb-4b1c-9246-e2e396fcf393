import sqlite3
from datetime import datetime

# 连接到数据库
conn = sqlite3.connect('../../db.sqlite3')
cursor = conn.cursor()

# 获取当前时间
now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# 添加工单管理菜单目录
cursor.execute("""
INSERT INTO menu (created_at, updated_at, name, path, component, redirect, icon, menu_type, order, parent_id, is_hidden, keepalive)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
""", (now, now, '工单管理', '/ticket', 'Layout', '/ticket/list', 'ph:ticket-bold', 'catalog', 3, 0, 0, 0))

# 获取新增菜单目录的ID
ticket_menu_id = cursor.lastrowid
print(f"添加工单管理菜单目录，ID: {ticket_menu_id}")

# 添加工单列表子菜单
cursor.execute("""
INSERT INTO menu (created_at, updated_at, name, path, component, redirect, icon, menu_type, order, parent_id, is_hidden, keepalive)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
""", (now, now, '工单列表', 'list', '/ticket/TicketList', '', 'ph:list-bold', 'menu', 1, ticket_menu_id, 0, 1))
print(f"添加工单列表子菜单，ID: {cursor.lastrowid}")

# 添加工单API配置
ticket_apis = [
    ('/api/v1/tickets', 'GET', '获取工单列表', '工单管理'),
    ('/api/v1/tickets', 'POST', '创建工单', '工单管理'),
    ('/api/v1/tickets/{ticket_id}', 'GET', '获取工单详情', '工单管理'),
    ('/api/v1/tickets/{ticket_id}', 'PUT', '更新工单', '工单管理'),
    ('/api/v1/tickets/{ticket_id}', 'DELETE', '删除工单', '工单管理'),
    ('/api/v1/tickets/ticket/categories', 'GET', '获取工单分类', '工单管理'),
    ('/api/v1/tickets/ticket/priorities', 'GET', '获取工单优先级', '工单管理'),
    ('/api/v1/tickets/ticket/statuses', 'GET', '获取工单状态', '工单管理'),
    ('/api/v1/tickets/ticket/service-catalogs', 'GET', '获取服务目录', '工单管理')
]

for path, method, summary, tags in ticket_apis:
    cursor.execute("""
    INSERT INTO api (created_at, updated_at, path, method, summary, tags)
    VALUES (?, ?, ?, ?, ?, ?)
    """, (now, now, path, method, summary, tags))
    print(f"添加API: {method} {path}, ID: {cursor.lastrowid}")

# 提交事务
conn.commit()

# 查询已添加的工单菜单
print("\n检查工单相关菜单...")
cursor.execute("SELECT id, name, path, component, menu_type, parent_id FROM menu WHERE name LIKE '%工单%'")
ticket_menus = cursor.fetchall()

print("工单相关菜单:")
for menu in ticket_menus:
    print(f"ID: {menu[0]}, 名称: {menu[1]}, 路径: {menu[2]}, 组件: {menu[3]}, "
          f"类型: {menu[4]}, 父级ID: {menu[5]}")

# 查询已添加的工单API
print("\n检查工单相关API...")
cursor.execute("SELECT id, path, method, summary, tags FROM api WHERE path LIKE '%tickets%'")
ticket_apis = cursor.fetchall()

print("工单相关API:")
for api in ticket_apis:
    print(f"ID: {api[0]}, 路径: {api[1]}, 方法: {api[2]}, 描述: {api[3]}, 标签: {api[4]}")

# 关闭数据库连接
conn.close()

print("\n菜单和API配置已添加完成。请刷新前端页面，并确保当前用户有权限访问工单菜单和API。") 