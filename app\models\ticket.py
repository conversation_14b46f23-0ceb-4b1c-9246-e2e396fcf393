from tortoise import fields
from .base import BaseModel, TimestampMixin

class TicketCategory(BaseModel, TimestampMixin):
    """工单分类"""
    name = fields.CharField(max_length=50, unique=True, description="分类名称", db_index=True)
    desc = fields.CharField(max_length=200, null=True, description="描述")
    is_active = fields.BooleanField(default=True, description="是否启用", db_index=True)
    order = fields.IntField(default=0, description="排序", db_index=True)

class TicketPriority(BaseModel, TimestampMixin):
    """工单优先级"""
    name = fields.CharField(max_length=20, unique=True, description="优先级名称", db_index=True)
    level = fields.IntField(unique=True, description="优先级等级(数字越小优先级越高)", db_index=True)
    color = fields.CharField(max_length=7, description="显示颜色")
    sla_hours = fields.IntField(description="SLA响应时间(小时)", db_index=True)

class TicketStatus(BaseModel, TimestampMixin):
    """工单状态"""
    name = fields.CharField(max_length=20, unique=True, description="状态名称", db_index=True)
    code = fields.CharField(max_length=20, unique=True, description="状态代码", db_index=True)
    color = fields.CharField(max_length=7, description="显示颜色")
    is_final = fields.BooleanField(default=False, description="是否为终态", db_index=True)
    order = fields.IntField(default=0, description="排序", db_index=True)

class ServiceCatalog(BaseModel, TimestampMixin):
    """服务目录"""
    name = fields.CharField(max_length=100, description="服务名称", db_index=True)
    desc = fields.TextField(null=True, description="服务描述")
    category_id = fields.ForeignKeyField("models.TicketCategory", description="所属分类", db_index=True)
    sla_priority_id = fields.ForeignKeyField("models.TicketPriority", description="默认优先级", db_index=True)
    is_active = fields.BooleanField(default=True, description="是否启用", db_index=True)
    auto_assign_rules = fields.JSONField(null=True, description="自动分配规则")

class Ticket(BaseModel, TimestampMixin):
    """工单主表"""
    ticket_no = fields.CharField(max_length=20, unique=True, description="工单编号", db_index=True)
    title = fields.CharField(max_length=200, description="工单标题", db_index=True)
    description = fields.TextField(description="详细描述")
    
    # 分类信息
    category_id = fields.ForeignKeyField("models.TicketCategory", description="工单分类", db_index=True)
    service_id = fields.ForeignKeyField("models.ServiceCatalog", null=True, description="服务类型", db_index=True)
    priority_id = fields.ForeignKeyField("models.TicketPriority", description="优先级", db_index=True)
    status_id = fields.ForeignKeyField("models.TicketStatus", description="当前状态", db_index=True)
    
    # 人员信息
    creator_id = fields.ForeignKeyField("models.User", related_name="created_tickets", description="创建人", db_index=True)
    assignee_id = fields.ForeignKeyField("models.User", null=True, related_name="assigned_tickets", description="分配给", db_index=True)
    
    # 时间信息
    due_time = fields.DatetimeField(null=True, description="期望完成时间", db_index=True)
    first_response_time = fields.DatetimeField(null=True, description="首次响应时间", db_index=True)
    resolution_time = fields.DatetimeField(null=True, description="解决时间", db_index=True)
    closed_time = fields.DatetimeField(null=True, description="关闭时间", db_index=True)
    
    # 评价信息
    satisfaction_score = fields.IntField(null=True, description="满意度评分(1-5)", db_index=True)
    satisfaction_comment = fields.TextField(null=True, description="满意度评价")
    
    # 其他
    source = fields.CharField(max_length=20, default="web", description="来源渠道", db_index=True)
    tags = fields.JSONField(null=True, description="标签")
    is_deleted = fields.BooleanField(default=False, description="软删除标记", db_index=True)

class TicketFlow(BaseModel, TimestampMixin):
    """工单流转记录"""
    ticket_id = fields.ForeignKeyField("models.Ticket", description="工单ID", db_index=True)
    from_status_id = fields.ForeignKeyField("models.TicketStatus", null=True, related_name="flow_from", description="原状态", db_index=True)
    to_status_id = fields.ForeignKeyField("models.TicketStatus", related_name="flow_to", description="新状态", db_index=True)
    from_assignee_id = fields.ForeignKeyField("models.User", null=True, related_name="flow_from_user", description="原分配人", db_index=True)
    to_assignee_id = fields.ForeignKeyField("models.User", null=True, related_name="flow_to_user", description="新分配人", db_index=True)
    operator_id = fields.ForeignKeyField("models.User", related_name="flow_operator", description="操作人", db_index=True)
    action = fields.CharField(max_length=50, description="操作动作", db_index=True)
    comment = fields.TextField(null=True, description="操作备注")

class TicketComment(BaseModel, TimestampMixin):
    """工单评论"""
    ticket_id = fields.ForeignKeyField("models.Ticket", description="工单ID", db_index=True)
    user_id = fields.ForeignKeyField("models.User", description="评论人", db_index=True)
    content = fields.TextField(description="评论内容")
    is_internal = fields.BooleanField(default=False, description="是否内部评论", db_index=True)
    parent_id = fields.IntField(null=True, description="父评论ID", db_index=True)

class TicketAttachment(BaseModel, TimestampMixin):
    """工单附件"""
    ticket_id = fields.ForeignKeyField("models.Ticket", description="工单ID", db_index=True)
    comment_id = fields.ForeignKeyField("models.TicketComment", null=True, description="关联评论", db_index=True)
    user_id = fields.ForeignKeyField("models.User", description="上传人", db_index=True)
    filename = fields.CharField(max_length=255, description="文件名")
    original_name = fields.CharField(max_length=255, description="原始文件名")
    file_path = fields.CharField(max_length=500, description="文件路径")
    file_size = fields.BigIntField(description="文件大小(字节)")
    mime_type = fields.CharField(max_length=100, description="文件类型")

class SLARule(BaseModel, TimestampMixin):
    """SLA规则"""
    name = fields.CharField(max_length=100, description="规则名称", db_index=True)
    priority_id = fields.ForeignKeyField("models.TicketPriority", description="适用优先级", db_index=True)
    category_id = fields.ForeignKeyField("models.TicketCategory", null=True, description="适用分类", db_index=True)
    response_hours = fields.IntField(description="响应时间(小时)")
    resolution_hours = fields.IntField(description="解决时间(小时)")
    escalation_hours = fields.IntField(null=True, description="升级时间(小时)")
    is_active = fields.BooleanField(default=True, description="是否启用", db_index=True)

class AutoAssignRule(BaseModel, TimestampMixin):
    """自动分配规则"""
    name = fields.CharField(max_length=100, description="规则名称", db_index=True)
    category_id = fields.ForeignKeyField("models.TicketCategory", null=True, description="适用分类", db_index=True)
    service_id = fields.ForeignKeyField("models.ServiceCatalog", null=True, description="适用服务", db_index=True)
    assign_type = fields.CharField(max_length=20, description="分配类型(round_robin/skill_match/load_balance)", db_index=True)
    conditions = fields.JSONField(null=True, description="分配条件")
    target_users = fields.JSONField(description="目标用户ID列表")
    is_active = fields.BooleanField(default=True, description="是否启用", db_index=True)
    priority = fields.IntField(default=0, description="规则优先级", db_index=True)

class KnowledgeCategory(BaseModel, TimestampMixin):
    """知识库分类"""
    name = fields.CharField(max_length=100, description="分类名称", db_index=True)
    desc = fields.CharField(max_length=500, null=True, description="分类描述")
    parent_id = fields.IntField(default=0, description="父分类ID", db_index=True)
    order = fields.IntField(default=0, description="排序", db_index=True)
    is_active = fields.BooleanField(default=True, description="是否启用", db_index=True)

class KnowledgeBase(BaseModel, TimestampMixin):
    """知识库"""
    title = fields.CharField(max_length=200, description="标题", db_index=True)
    content = fields.TextField(description="内容")
    summary = fields.CharField(max_length=500, null=True, description="摘要")
    category_id = fields.ForeignKeyField("models.KnowledgeCategory", description="分类", db_index=True)
    author_id = fields.ForeignKeyField("models.User", description="作者", db_index=True)
    tags = fields.JSONField(null=True, description="标签")
    view_count = fields.BigIntField(default=0, description="查看次数", db_index=True)
    helpful_count = fields.IntField(default=0, description="有用次数", db_index=True)
    is_published = fields.BooleanField(default=False, description="是否发布", db_index=True)
    is_featured = fields.BooleanField(default=False, description="是否精选", db_index=True)

class KnowledgeTicketRelation(BaseModel, TimestampMixin):
    """知识库与工单关联"""
    knowledge_id = fields.ForeignKeyField("models.KnowledgeBase", description="知识库", db_index=True)
    ticket_id = fields.ForeignKeyField("models.Ticket", description="工单", db_index=True)
    relation_type = fields.CharField(max_length=20, description="关联类型(reference/solution)", db_index=True)

class Notification(BaseModel, TimestampMixin):
    """通知消息"""
    user_id = fields.ForeignKeyField("models.User", description="接收用户", db_index=True)
    title = fields.CharField(max_length=200, description="通知标题")
    content = fields.TextField(description="通知内容")
    type = fields.CharField(max_length=20, description="通知类型", db_index=True)
    related_id = fields.IntField(null=True, description="关联对象ID", db_index=True)
    related_type = fields.CharField(max_length=20, null=True, description="关联对象类型", db_index=True)
    is_read = fields.BooleanField(default=False, description="是否已读", db_index=True)
    read_time = fields.DatetimeField(null=True, description="阅读时间", db_index=True)

class TicketStatistics(BaseModel, TimestampMixin):
    """工单统计快照"""
    date = fields.DateField(description="统计日期", db_index=True)
    category_id = fields.ForeignKeyField("models.TicketCategory", null=True, description="分类", db_index=True)
    total_count = fields.IntField(default=0, description="总工单数")
    created_count = fields.IntField(default=0, description="新建工单数")
    resolved_count = fields.IntField(default=0, description="解决工单数")
    avg_response_hours = fields.FloatField(null=True, description="平均响应时间(小时)")
    avg_resolution_hours = fields.FloatField(null=True, description="平均解决时间(小时)")
    satisfaction_avg = fields.FloatField(null=True, description="平均满意度")