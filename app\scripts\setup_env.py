#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境设置脚本

用于快速设置项目环境配置，支持：
1. 设置当前运行环境 (development/testing/production)
2. 复制对应的环境配置文件
3. 验证环境配置
"""

import os
import sys
import shutil
import argparse
from pathlib import Path


def get_project_root() -> Path:
    """获取项目根目录"""
    current_file = Path(__file__).resolve()
    # 从 app/scripts/setup_env.py 到项目根目录
    return current_file.parent.parent.parent


def get_settings_dir() -> Path:
    """获取设置目录"""
    return get_project_root() / "app" / "settings"


def copy_env_config(env_type: str) -> bool:
    """
    复制环境配置文件
    
    Args:
        env_type: 环境类型 (development, testing, production)
        
    Returns:
        bool: 是否成功
    """
    settings_dir = get_settings_dir()
    project_root = get_project_root()
    
    # 环境配置文件映射
    env_files = {
        'development': 'env.development',
        'testing': 'env.development',  # 测试环境使用开发环境配置
        'production': 'env.production'
    }
    
    if env_type not in env_files:
        print(f"❌ 不支持的环境类型: {env_type}")
        print(f"支持的环境类型: {', '.join(env_files.keys())}")
        return False
    
    source_file = settings_dir / env_files[env_type]
    target_file = project_root / ".env"
    
    if not source_file.exists():
        print(f"❌ 环境配置文件不存在: {source_file}")
        return False
    
    try:
        # 如果目标文件存在，先备份
        if target_file.exists():
            backup_file = project_root / ".env.backup"
            shutil.copy2(target_file, backup_file)
            print(f"📋 已备份现有配置到: {backup_file}")
        
        # 复制新的配置文件
        shutil.copy2(source_file, target_file)
        
        # 设置环境变量
        with open(target_file, 'a', encoding='utf-8') as f:
            f.write(f"\n# Environment set by setup script\n")
            f.write(f"ENV={env_type}\n")
        
        print(f"✅ 已设置为 {env_type} 环境")
        print(f"📁 配置文件: {target_file}")
        print(f"📄 源文件: {source_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 复制配置文件失败: {e}")
        return False


def validate_env_config() -> bool:
    """
    验证环境配置
    
    Returns:
        bool: 配置是否有效
    """
    project_root = get_project_root()
    env_file = project_root / ".env"
    
    if not env_file.exists():
        print("❌ .env 文件不存在")
        print("请先运行: python setup_env.py --env development")
        return False
    
    # 检查必需的环境变量
    required_vars = [
        'DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME',
        'SECRET_KEY', 'JWT_ALGORITHM'
    ]
    
    env_vars = {}
    missing_vars = []
    
    try:
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
        
        # 检查必需的变量
        for var in required_vars:
            if var not in env_vars or not env_vars[var]:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
            return False
        
        # 检查环境类型
        env_type = env_vars.get('ENVIRONMENT', env_vars.get('ENV', 'unknown'))
        print(f"✅ 环境配置验证通过")
        print(f"🏷️  环境类型: {env_type}")
        print(f"🗄️  数据库: {env_vars.get('DB_HOST')}:{env_vars.get('DB_PORT', '3306')}/{env_vars.get('DB_NAME')}")
        
        # 安全检查
        if env_type == 'production':
            secret_key = env_vars.get('SECRET_KEY', '')
            if 'dev_secret_key' in secret_key or 'development' in secret_key:
                print("⚠️  警告: 生产环境使用了开发环境的密钥，请立即修改!")
                return False
            
            debug = env_vars.get('DEBUG', 'false').lower()
            if debug == 'true':
                print("⚠️  警告: 生产环境开启了调试模式，建议关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证配置文件失败: {e}")
        return False


def show_env_info():
    """显示环境信息"""
    project_root = get_project_root()
    settings_dir = get_settings_dir()
    
    print("🔧 Vue FastAPI Admin - 环境配置工具")
    print("=" * 50)
    print(f"📁 项目根目录: {project_root}")
    print(f"⚙️  设置目录: {settings_dir}")
    print()
    
    # 显示可用的环境配置文件
    print("📋 可用的环境配置:")
    env_files = {
        'development': 'env.development',
        'testing': 'env.development (共用开发环境配置)',
        'production': 'env.production'
    }
    
    for env_type, file_desc in env_files.items():
        config_file = settings_dir / file_desc.split(' ')[0]
        status = "✅" if config_file.exists() else "❌"
        print(f"  {status} {env_type.ljust(12)} -> {file_desc}")
    
    print()
    
    # 显示当前环境状态
    env_file = project_root / ".env"
    if env_file.exists():
        print("📄 当前 .env 文件存在")
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line.startswith('ENVIRONMENT=') or line.startswith('ENV='):
                        env_value = line.split('=', 1)[1].strip()
                        print(f"🏷️  当前环境: {env_value}")
                        break
                else:
                    print("🏷️  当前环境: 未设置")
        except Exception:
            print("⚠️  读取 .env 文件失败")
    else:
        print("📄 当前 .env 文件不存在")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Vue FastAPI Admin 环境配置工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python setup_env.py --info                    # 显示环境信息
  python setup_env.py --env development         # 设置为开发环境
  python setup_env.py --env production          # 设置为生产环境
  python setup_env.py --validate               # 验证当前配置
        """
    )
    
    parser.add_argument(
        '--env', 
        choices=['development', 'testing', 'production'],
        help='设置环境类型'
    )
    
    parser.add_argument(
        '--validate',
        action='store_true',
        help='验证当前环境配置'
    )
    
    parser.add_argument(
        '--info',
        action='store_true',
        help='显示环境信息'
    )
    
    args = parser.parse_args()
    
    # 如果没有提供参数，显示帮助信息
    if not any([args.env, args.validate, args.info]):
        show_env_info()
        print()
        parser.print_help()
        return
    
    # 显示环境信息
    if args.info:
        show_env_info()
        return
    
    # 设置环境
    if args.env:
        if copy_env_config(args.env):
            print()
            print("🔍 验证配置...")
            validate_env_config()
        return
    
    # 验证配置
    if args.validate:
        validate_env_config()


if __name__ == "__main__":
    main() 