# -*- coding: utf-8 -*-
"""
CMDB发现引擎

统一管理和调度各种数据源的发现任务，包括数据收集、处理、映射和同步。
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
import json
from enum import Enum

from app.discovery.connectors.zabbix_connector import ZabbixDiscoveryConnector, ZabbixDataMapper
from app.discovery.connectors.prometheus_connector import PrometheusDiscoveryConnector, PrometheusDataMapper
# from app.models.base import CRUDBase  # 暂时注释，后续根据实际需要调整
# from app.core.database import get_async_session  # 暂时注释，后续根据实际数据库结构调整

logger = logging.getLogger(__name__)


class DiscoveryStatus(Enum):
    """发现状态枚举"""
    PENDING = "等待"
    RUNNING = "运行中"
    COMPLETED = "已完成"
    FAILED = "失败"
    CANCELLED = "已取消"


class ActionType(Enum):
    """操作类型枚举"""
    CREATE = "创建"
    UPDATE = "更新"
    DELETE = "删除"
    SKIP = "跳过"


@dataclass
class DiscoveryResult:
    """发现结果数据结构"""
    source_name: str
    source_type: str
    status: DiscoveryStatus
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    skipped_items: int = 0
    errors: List[str] = field(default_factory=list)
    summary: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


@dataclass
class SyncAction:
    """同步操作数据结构"""
    ci_id: str
    action_type: ActionType
    source_data: Dict[str, Any]
    existing_data: Optional[Dict[str, Any]] = None
    changes_detected: Dict[str, Any] = field(default_factory=dict)
    sync_status: str = "成功"
    error_message: Optional[str] = None


class DataProcessor:
    """数据处理器基类"""
    
    def __init__(self, discovery_engine: 'CMDBDiscoveryEngine'):
        self.engine = discovery_engine
    
    def normalize_data(self, raw_data: Any, source_type: str) -> Dict[str, Any]:
        """数据标准化"""
        raise NotImplementedError
    
    def detect_ci_type(self, data: Dict[str, Any]) -> str:
        """检测CI类型"""
        raise NotImplementedError
    
    def map_to_cmdb_schema(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """映射到CMDB数据模型"""
        raise NotImplementedError
    
    async def process_data(self, raw_data: List[Any], source_type: str) -> List[Dict[str, Any]]:
        """处理数据"""
        processed_data = []
        
        for item in raw_data:
            try:
                # 对于Prometheus，根据数据项类型确定具体的source_type
                if source_type == "prometheus":
                    item_source_type = self._determine_prometheus_item_type(item)
                else:
                    item_source_type = source_type
                
                # 标准化数据
                normalized = self.normalize_data(item, item_source_type)
                
                # 映射到CMDB模型
                mapped = self.map_to_cmdb_schema(normalized)
                
                processed_data.append(mapped)
                
            except Exception as e:
                logger.error(f"处理数据项失败: {str(e)}")
                continue
        
        return processed_data
    
    def _determine_prometheus_item_type(self, item: Any) -> str:
        """确定Prometheus数据项类型（子类可重写）"""
        return "unknown"


class ZabbixProcessor(DataProcessor):
    """Zabbix数据处理器"""
    
    def normalize_data(self, raw_data: Any, source_type: str) -> Dict[str, Any]:
        """标准化Zabbix数据"""
        return ZabbixDataMapper.map_host_to_ci(raw_data)
    
    def detect_ci_type(self, data: Dict[str, Any]) -> str:
        """检测CI类型"""
        return data.get("ci_type_code", "PHYSICAL_SERVER")
    
    def map_to_cmdb_schema(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """映射到CMDB数据模型"""

        # 映射Zabbix数据到ConfigurationItem模型字段
        mapped_data = {}

        # 保留原始ID用于同步逻辑
        mapped_data["id"] = data.get("id", "")

        # 基本信息
        mapped_data["name"] = data.get("name", "")
        mapped_data["display_name"] = data.get("display_name", "")

        # 生成唯一的asset_tag
        asset_tag = data.get("asset_tag", "")
        if not asset_tag:
            # 使用完整的ID作为asset_tag以确保唯一性
            raw_id = data.get("id", "")
            asset_tag = raw_id

        # 确保asset_tag不为空
        if not asset_tag:
            import uuid
            asset_tag = f"zabbix_{data.get('hostname', 'unknown')}_{str(uuid.uuid4())[:8]}"

        mapped_data["asset_tag"] = asset_tag

        # CI类型
        mapped_data["ci_type"] = data.get("ci_type", "PHYSICAL_SERVER")

        # 外部系统ID
        mapped_data["external_id"] = data.get("external_id", "")

        # 网络信息
        mapped_data["hostname"] = data.get("hostname", "")
        mapped_data["ip_addresses"] = data.get("ip_addresses", [])
        mapped_data["mac_addresses"] = data.get("mac_addresses", [])

        # 系统信息
        mapped_data["operating_system"] = data.get("operating_system", "")
        mapped_data["os_version"] = data.get("os_version", "")

        # 状态信息
        mapped_data["operational_status"] = data.get("operational_status", "未知")
        mapped_data["health_status"] = data.get("health_status", "未知")
        mapped_data["monitoring_enabled"] = data.get("monitoring_enabled", True)

        # 位置和环境
        mapped_data["environment"] = data.get("environment", "zabbix")
        mapped_data["location"] = data.get("location", "")

        # 服务相关字段（用于Prometheus数据）
        mapped_data["service_name"] = data.get("service_name", "")
        mapped_data["service_port"] = data.get("service_port", "")

        # 硬件规格
        mapped_data["hardware_specifications"] = data.get("hardware_specifications", {})

        # 自定义属性
        mapped_data["custom_attributes"] = data.get("custom_attributes", {})
        mapped_data["tags"] = data.get("tags", [])

        # 数据来源
        mapped_data["data_source"] = data.get("data_source", "zabbix")

        # 发现时间
        mapped_data["last_discovered_at"] = data.get("last_discovered_at", datetime.now())

        return mapped_data


class PrometheusProcessor(DataProcessor):
    """Prometheus数据处理器"""
    
    def _determine_prometheus_item_type(self, item: Any) -> str:
        """确定Prometheus数据项类型"""
        from app.discovery.connectors.prometheus_connector import PrometheusTarget, PrometheusService
        
        if isinstance(item, PrometheusTarget):
            return "target"
        elif isinstance(item, PrometheusService):
            return "service"
        else:
            logger.warning(f"未知的Prometheus数据类型: {type(item)}")
            return "unknown"
    
    def normalize_data(self, raw_data: Any, source_type: str) -> Dict[str, Any]:
        """标准化Prometheus数据"""
        if source_type == "target":
            return PrometheusDataMapper.map_target_to_ci(raw_data)
        elif source_type == "service":
            return PrometheusDataMapper.map_service_to_ci(raw_data)
        else:
            raise ValueError(f"不支持的Prometheus数据类型: {source_type}")
    
    def detect_ci_type(self, data: Dict[str, Any]) -> str:
        """检测CI类型"""
        return data.get("ci_type_code", "APPLICATION_SERVICE")
    
    def map_to_cmdb_schema(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """映射到CMDB数据模型"""
        
        # 映射Prometheus数据到ConfigurationItem模型字段
        mapped_data = {}
        
        # 保留原始ID用于同步逻辑
        mapped_data["id"] = data.get("id", "")
        
        # 基本信息
        mapped_data["name"] = data.get("name", "")
        mapped_data["display_name"] = data.get("display_name", "")
        
        # 生成唯一的asset_tag
        asset_tag = data.get("asset_tag", "")
        if not asset_tag:
            # 使用完整的ID作为asset_tag以确保唯一性
            raw_id = data.get("id", "")
            asset_tag = raw_id
        
        # 确保asset_tag不为空
        if not asset_tag:
            import uuid
            asset_tag = f"prometheus_{data.get('prometheus_job', 'unknown')}_{str(uuid.uuid4())[:8]}"
            
        mapped_data["asset_tag"] = asset_tag
        
        # CI类型映射 - 修复字段名
        ci_type = data.get("ci_type", "APPLICATION_SERVICE")
        mapped_data["ci_type"] = ci_type

        # 外部系统ID
        mapped_data["external_id"] = data.get("external_id", "")
        
        # 网络信息
        mapped_data["hostname"] = data.get("hostname", "")
        mapped_data["ip_addresses"] = data.get("ip_addresses", [])
        mapped_data["mac_addresses"] = data.get("mac_addresses", [])

        # 服务信息
        mapped_data["service_name"] = data.get("service_name", "")
        service_port = data.get("service_port")
        if service_port and str(service_port).isdigit():
            mapped_data["service_port"] = int(service_port)
        else:
            mapped_data["service_port"] = None
        
        # 位置和环境
        mapped_data["environment"] = "prometheus"
        mapped_data["cluster_name"] = data.get("cluster_name", "")
        mapped_data["namespace"] = data.get("namespace", "")
        
        # 状态信息
        mapped_data["operational_status"] = data.get("operational_status", "未知")
        mapped_data["health_status"] = data.get("discovery_status", "未知")
        mapped_data["monitoring_enabled"] = data.get("monitoring_enabled", True)
        
        # 自定义属性和标签
        mapped_data["custom_attributes"] = data.get("custom_attributes", {})
        mapped_data["tags"] = data.get("tags", [])
        
        # 数据来源
        mapped_data["data_source"] = data.get("data_source", "prometheus")

        # 发现时间
        mapped_data["last_discovered_at"] = data.get("last_discovered_at", datetime.now())

        return mapped_data


class CMDBSyncEngine:
    """CMDB同步引擎"""
    
    def __init__(self, discovery_engine: 'CMDBDiscoveryEngine'):
        self.engine = discovery_engine
    
    async def sync_cis(self, processed_data: List[Dict[str, Any]], job_id: int) -> List[SyncAction]:
        """同步配置项到CMDB"""
        sync_actions = []
        
        for ci_data in processed_data:
            try:
                action = await self._sync_single_ci(ci_data, job_id)
                sync_actions.append(action)
                
            except Exception as e:
                logger.error(f"同步CI {ci_data.get('id')} 失败: {str(e)}")
                
                action = SyncAction(
                    ci_id=ci_data.get("id", "unknown"),
                    action_type=ActionType.SKIP,
                    source_data=ci_data,
                    sync_status="失败",
                    error_message=str(e)
                )
                sync_actions.append(action)
        
        return sync_actions
    
    async def _sync_single_ci(self, ci_data: Dict[str, Any], job_id: int) -> SyncAction:
        """同步单个配置项"""
        ci_id = ci_data["id"]
        asset_tag = ci_data.get("asset_tag", ci_id)

        # 检查CI是否已存在（使用asset_tag作为查找键）
        existing_ci = await self._get_existing_ci(asset_tag)
        
        if existing_ci:
            # 检测变更
            changes = self._detect_changes(existing_ci, ci_data)
            
            if changes:
                # 执行更新
                await self._update_ci(asset_tag, ci_data)
                action_type = ActionType.UPDATE
            else:
                # 无变更，跳过
                action_type = ActionType.SKIP
            
            return SyncAction(
                ci_id=ci_id,
                action_type=action_type,
                source_data=ci_data,
                existing_data=existing_ci,
                changes_detected=changes
            )
        else:
            # 创建新CI
            await self._create_ci(ci_data)
            
            return SyncAction(
                ci_id=ci_id,
                action_type=ActionType.CREATE,
                source_data=ci_data
            )
    
    async def _get_existing_ci(self, ci_id: str) -> Optional[Dict[str, Any]]:
        """获取现有CI"""
        try:
            from app.models.cmdb import ConfigurationItem
            existing_ci = await ConfigurationItem.filter(asset_tag=ci_id).first()
            
            if existing_ci:
                return {
                    "id": existing_ci.id,
                    "name": existing_ci.name,
                    "ci_type": existing_ci.ci_type,
                    "hostname": existing_ci.hostname,
                    "ip_addresses": existing_ci.ip_addresses,
                    "custom_attributes": existing_ci.custom_attributes,
                    "data_hash": existing_ci.data_hash
                }
            return None
        except Exception as e:
            logger.error(f"查询现有CI失败: {str(e)}")
            return None
    
    def _detect_changes(self, existing: Dict[str, Any], new: Dict[str, Any]) -> Dict[str, Any]:
        """检测数据变更"""
        changes = {}
        
        # 比较关键字段
        key_fields = [
            "name", "operational_status", "ip_addresses", "operating_system",
            "hardware_specifications", "location_name"
        ]
        
        for field in key_fields:
            existing_value = existing.get(field)
            new_value = new.get(field)
            
            if existing_value != new_value:
                changes[field] = {
                    "old": existing_value,
                    "new": new_value
                }
        
        return changes
    
    async def _create_ci(self, ci_data: Dict[str, Any]):
        """创建新CI"""
        try:
            from app.models.cmdb import ConfigurationItem
            import hashlib
            import json
            from datetime import datetime
            
            # 处理datetime对象以便JSON序列化
            def serialize_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: serialize_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [serialize_datetime(item) for item in obj]
                return obj
            
            # 序列化安全的数据副本
            serializable_data = serialize_datetime(ci_data.copy())
            
            # 计算数据哈希
            data_hash = hashlib.md5(
                json.dumps(serializable_data, sort_keys=True).encode()
            ).hexdigest()
            
            # 添加数据哈希
            ci_data["data_hash"] = data_hash
            
            # 移除不属于模型的字段
            model_data = ci_data.copy()
            model_data.pop("id", None)  # 移除id字段，因为这是内部使用的
            
            new_ci = await ConfigurationItem.create(**model_data)
            logger.info(f"创建CI成功: {new_ci.name} (ID: {new_ci.id})")
            return new_ci
        except Exception as e:
            logger.error(f"创建CI失败: {str(e)}")
            raise e
    
    async def _update_ci(self, ci_id: str, ci_data: Dict[str, Any]):
        """更新CI"""
        try:
            from app.models.cmdb import ConfigurationItem
            import hashlib
            import json
            from datetime import datetime
            
            # 处理datetime对象以便JSON序列化
            def serialize_datetime(obj):
                if isinstance(obj, datetime):
                    return obj.isoformat()
                elif isinstance(obj, dict):
                    return {k: serialize_datetime(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [serialize_datetime(item) for item in obj]
                return obj
            
            # 序列化安全的数据副本
            serializable_data = serialize_datetime(ci_data.copy())
            
            # 计算数据哈希
            data_hash = hashlib.md5(
                json.dumps(serializable_data, sort_keys=True).encode()
            ).hexdigest()
            
            existing_ci = await ConfigurationItem.filter(asset_tag=ci_id).first()
            if existing_ci:
                # 更新所有字段（排除内部字段）
                for key, value in ci_data.items():
                    if key not in ["id"] and hasattr(existing_ci, key):
                        setattr(existing_ci, key, value)
                
                existing_ci.data_hash = data_hash
                await existing_ci.save()
                logger.info(f"更新CI成功: {existing_ci.name}")
                return existing_ci
            else:
                logger.warning(f"未找到要更新的CI: {ci_id}")
                return None
        except Exception as e:
            logger.error(f"更新CI失败: {str(e)}")
            raise e


class CMDBDiscoveryEngine:
    """CMDB发现引擎"""
    
    def __init__(self):
        self.connectors = {}
        self.processors = {
            'zabbix': ZabbixProcessor(self),
            'prometheus': PrometheusProcessor(self)
        }
        self.sync_engine = CMDBSyncEngine(self)
    
    def register_connector(self, name: str, connector_class: type, config: Dict[str, Any]):
        """注册数据源连接器"""
        self.connectors[name] = connector_class(config)
        logger.info(f"注册数据源连接器: {name}")
    
    async def run_discovery(self, source_names: List[str] = None, job_id: int = None) -> Dict[str, DiscoveryResult]:
        """执行发现流程"""
        logger.info("开始CMDB自动发现...")
        
        # 确定要运行的数据源
        sources_to_run = source_names or list(self.connectors.keys())
        
        results = {}
        
        for source_name in sources_to_run:
            if source_name not in self.connectors:
                logger.warning(f"未找到数据源连接器: {source_name}")
                continue
            
            try:
                result = await self._run_single_source_discovery(source_name, job_id)
                results[source_name] = result
                
            except Exception as e:
                logger.error(f"数据源 {source_name} 发现失败: {str(e)}")
                
                results[source_name] = DiscoveryResult(
                    source_name=source_name,
                    source_type=source_name,
                    status=DiscoveryStatus.FAILED,
                    errors=[str(e)]
                )
        
        logger.info("CMDB自动发现完成")
        return results
    
    async def _run_single_source_discovery(self, source_name: str, job_id: int = None) -> DiscoveryResult:
        """运行单个数据源的发现"""
        start_time = datetime.now()
        
        result = DiscoveryResult(
            source_name=source_name,
            source_type=source_name,
            status=DiscoveryStatus.RUNNING,
            start_time=start_time
        )
        
        try:
            connector = self.connectors[source_name]
            processor = self.processors.get(source_name)
            
            if not processor:
                raise ValueError(f"未找到数据处理器: {source_name}")
            
            # 1. 收集原始数据
            raw_data = await self._collect_data_from_source(source_name, connector)
            result.total_items = len(raw_data)
            
            # 2. 处理数据
            processed_data = await processor.process_data(raw_data, source_name)
            result.processed_items = len(processed_data)
            
            # 3. 同步到CMDB
            sync_actions = await self.sync_engine.sync_cis(processed_data, job_id)
            
            # 4. 统计结果
            result.successful_items = len([a for a in sync_actions if a.sync_status == "成功"])
            result.failed_items = len([a for a in sync_actions if a.sync_status == "失败"])
            result.skipped_items = len([a for a in sync_actions if a.action_type == ActionType.SKIP])
            
            result.status = DiscoveryStatus.COMPLETED
            result.summary = {
                "actions": {
                    "created": len([a for a in sync_actions if a.action_type == ActionType.CREATE]),
                    "updated": len([a for a in sync_actions if a.action_type == ActionType.UPDATE]),
                    "deleted": len([a for a in sync_actions if a.action_type == ActionType.DELETE]),
                    "skipped": len([a for a in sync_actions if a.action_type == ActionType.SKIP])
                }
            }
            
        except Exception as e:
            result.status = DiscoveryStatus.FAILED
            result.errors.append(str(e))
            logger.error(f"数据源 {source_name} 发现失败: {str(e)}")
        
        finally:
            result.end_time = datetime.now()
            result.execution_time = (result.end_time - result.start_time).total_seconds()
        
        return result
    
    async def _collect_data_from_source(self, source_name: str, connector) -> List[Any]:
        """从数据源收集数据"""
        if source_name == "zabbix":
            return await connector.discover_hosts()
        elif source_name == "prometheus":
            # Prometheus可以发现多种类型的数据
            targets = await connector.discover_targets()
            services = await connector.discover_services()
            return targets + services
        else:
            raise ValueError(f"不支持的数据源类型: {source_name}")
    
    async def test_all_connections(self) -> Dict[str, Dict[str, Any]]:
        """测试所有数据源连接"""
        results = {}
        
        for source_name, connector in self.connectors.items():
            try:
                result = await connector.test_connection()
                results[source_name] = result
            except Exception as e:
                results[source_name] = {
                    "success": False,
                    "message": f"测试失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }
        
        return results
    
    async def get_discovery_statistics(self) -> Dict[str, Any]:
        """获取发现统计信息"""
        # 这里应该查询数据库获取统计信息
        return {
            "total_cis": 0,
            "last_discovery": None,
            "sources": {
                source_name: {
                    "enabled": True,
                    "last_sync": None,
                    "ci_count": 0
                }
                for source_name in self.connectors.keys()
            }
        }


# 工厂函数
def create_discovery_engine(config: Dict[str, Any] = None) -> CMDBDiscoveryEngine:
    """创建发现引擎实例"""
    if config is None:
        from app.settings.config import get_discovery_config
        config = get_discovery_config()
    
    engine = CMDBDiscoveryEngine()
    
    # 注册Zabbix连接器
    if "zabbix" in config:
        engine.register_connector(
            "zabbix",
            ZabbixDiscoveryConnector,
            config["zabbix"]
        )
    
    # 注册Prometheus连接器
    if "prometheus" in config:
        engine.register_connector(
            "prometheus",
            PrometheusDiscoveryConnector,
            config["prometheus"]
        )
    
    return engine


# 使用示例
async def test_discovery_engine():
    """测试发现引擎"""
    from app.settings.config import get_discovery_config
    
    # 使用统一配置
    config = get_discovery_config()
    
    engine = create_discovery_engine(config)
    
    # 测试连接
    connection_results = await engine.test_all_connections()
    print(f"连接测试结果: {json.dumps(connection_results, indent=2, ensure_ascii=False)}")
    
    # 运行发现
    discovery_results = await engine.run_discovery()
    print(f"发现结果: {json.dumps({k: v.__dict__ for k, v in discovery_results.items()}, indent=2, ensure_ascii=False, default=str)}")


if __name__ == "__main__":
    asyncio.run(test_discovery_engine()) 