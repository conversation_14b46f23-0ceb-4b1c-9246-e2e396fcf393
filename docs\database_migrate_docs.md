# 数据库迁移流程总结

## 1. 数据库初始化

使用 `init_db.py` 脚本初始化数据库结构：

```bash
python init_db.py
```

这个脚本会根据你的模型定义创建所有数据库表。

## 2. 添加基础数据

使用 `init_seed_data.py` 脚本添加基础数据：

```bash
python init_seed_data.py
```

这个脚本会添加工单分类、优先级、状态和服务目录等基础数据。

## 3. 模型变更迁移流程

当你需要修改或添加新模型时，按照以下步骤操作：

### 步骤 1: 修改模型文件

在 `app/models` 目录下修改现有模型或创建新的模型文件。

### 步骤 2: 更新模型导入

确保在 `app/models/__init__.py` 中导入了新模型：

```python
# 新增model需要在这里导入
from .admin import *
from .ticket import *
from .your_new_model import *  # 添加新模型的导入
```

### 步骤 3: 创建迁移文件

使用 `aerich_migrate.py` 脚本创建迁移文件：

```bash
python aerich_migrate.py migrate
```

这会在 `migrations/models` 目录下生成新的迁移文件。

### 步骤 4: 应用迁移

使用 `aerich_migrate.py` 脚本应用迁移：

```bash
python aerich_migrate.py upgrade
```

这会将新的模型变更应用到数据库。

### 步骤 5: 添加新的种子数据（如需要）

如果新模型需要基础数据，可以创建一个新的种子数据脚本或修改现有的 `init_seed_data.py`。

## 其他有用的迁移命令

- **查看迁移历史**：
  ```bash
  python aerich_migrate.py history
  ```

- **回滚迁移**：
  ```bash
  python aerich_migrate.py downgrade
  ```
  或指定版本回滚：
  ```bash
  python aerich_migrate.py downgrade 版本号
  ```

## 注意事项

1. 确保数据库配置正确（主机、端口、用户名、密码等）
2. 模型修改后一定要先创建迁移文件，再应用迁移
3. 大型变更建议先在测试环境验证
4. 生产环境迁移前务必备份数据库

通过这套流程，你可以方便地管理数据库模式的演化，确保应用程序和数据库结构保持同步。
