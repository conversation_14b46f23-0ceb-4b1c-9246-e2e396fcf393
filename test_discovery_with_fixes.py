import asyncio
import sys
import json
sys.path.insert(0, '.')
from app.settings.env_manager import init_environment
from app.models.cmdb import ConfigurationItem
from app.discovery.engine import create_discovery_engine
from tortoise import Tortoise

async def test_discovery_with_fixes():
    """测试修复后的发现引擎是否能正确采集和存储数据"""
    init_environment()
    from app.settings.config import settings
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    print("=== 测试修复后的数据发现和存储 ===")
    
    # 记录修复前的数据统计
    print("修复前数据统计:")
    total_zabbix_before = await ConfigurationItem.filter(data_source='zabbix').count()
    total_prometheus_before = await ConfigurationItem.filter(data_source='prometheus').count()
    zabbix_with_external_id_before = await ConfigurationItem.filter(
        data_source='zabbix', 
        external_id__not_isnull=True
    ).exclude(external_id='').count()
    prometheus_with_external_id_before = await ConfigurationItem.filter(
        data_source='prometheus', 
        external_id__not_isnull=True
    ).exclude(external_id='').count()
    
    print(f"  Zabbix配置项: {total_zabbix_before} (有external_id: {zabbix_with_external_id_before})")
    print(f"  Prometheus配置项: {total_prometheus_before} (有external_id: {prometheus_with_external_id_before})")
    print()
    
    try:
        # 创建发现引擎
        print("创建发现引擎...")
        from app.settings.config import get_discovery_config
        config = get_discovery_config()
        engine = create_discovery_engine(config)
        
        # 测试连接
        print("测试数据源连接...")
        connection_results = await engine.test_all_connections()
        
        for source_name, result in connection_results.items():
            status = "✅ 成功" if result.get("success") else "❌ 失败"
            print(f"  {source_name}: {status} - {result.get('message', '')}")
        
        # 检查是否有可用的数据源
        available_sources = [name for name, result in connection_results.items() if result.get("success")]
        
        if not available_sources:
            print("❌ 没有可用的数据源，无法进行测试")
            return
        
        print(f"\n可用数据源: {', '.join(available_sources)}")
        
        # 运行发现（限制为一个数据源以便快速测试）
        test_source = available_sources[0]  # 选择第一个可用的数据源
        print(f"\n开始运行 {test_source} 数据发现...")
        
        discovery_results = await engine.run_discovery([test_source])
        
        # 显示发现结果
        for source_name, result in discovery_results.items():
            print(f"\n{source_name} 发现结果:")
            print(f"  状态: {result.status}")
            print(f"  总项目: {result.total_items}")
            print(f"  处理项目: {result.processed_items}")
            print(f"  成功项目: {result.successful_items}")
            print(f"  失败项目: {result.failed_items}")
            print(f"  跳过项目: {result.skipped_items}")
            print(f"  执行时间: {result.execution_time:.2f}秒")
            
            if result.errors:
                print(f"  错误: {result.errors}")
        
        # 检查修复后的数据质量
        print("\n=== 修复后数据质量检查 ===")
        
        # 获取最新的几个配置项
        if test_source == 'zabbix':
            latest_items = await ConfigurationItem.filter(
                data_source='zabbix'
            ).order_by('-created_at').limit(3)
            
            print("最新的Zabbix配置项:")
            for i, item in enumerate(latest_items, 1):
                print(f"{i}. Asset Tag: {item.asset_tag}")
                print(f"   External ID: {item.external_id}")
                print(f"   Hostname: {item.hostname}")
                print(f"   IP Addresses: {item.ip_addresses}")
                print(f"   Operating System: {item.operating_system}")
                print(f"   Created: {item.created_at}")
                print()
        
        elif test_source == 'prometheus':
            latest_items = await ConfigurationItem.filter(
                data_source='prometheus'
            ).order_by('-created_at').limit(3)
            
            print("最新的Prometheus配置项:")
            for i, item in enumerate(latest_items, 1):
                print(f"{i}. Asset Tag: {item.asset_tag}")
                print(f"   External ID: {item.external_id}")
                print(f"   Hostname: {item.hostname}")
                print(f"   IP Addresses: {item.ip_addresses}")
                print(f"   Service Name: {item.service_name}")
                print(f"   Created: {item.created_at}")
                print()
        
        # 统计修复后的数据
        total_zabbix_after = await ConfigurationItem.filter(data_source='zabbix').count()
        total_prometheus_after = await ConfigurationItem.filter(data_source='prometheus').count()
        zabbix_with_external_id_after = await ConfigurationItem.filter(
            data_source='zabbix', 
            external_id__not_isnull=True
        ).exclude(external_id='').count()
        prometheus_with_external_id_after = await ConfigurationItem.filter(
            data_source='prometheus', 
            external_id__not_isnull=True
        ).exclude(external_id='').count()
        
        print("修复后数据统计:")
        print(f"  Zabbix配置项: {total_zabbix_after} (有external_id: {zabbix_with_external_id_after})")
        print(f"  Prometheus配置项: {total_prometheus_after} (有external_id: {prometheus_with_external_id_after})")
        
        # 计算改进情况
        if test_source == 'zabbix':
            new_items = total_zabbix_after - total_zabbix_before
            new_with_external_id = zabbix_with_external_id_after - zabbix_with_external_id_before
            if new_items > 0:
                success_rate = (new_with_external_id / new_items) * 100
                print(f"\n✅ 新增 {new_items} 个Zabbix配置项，其中 {new_with_external_id} 个有external_id (成功率: {success_rate:.1f}%)")
            else:
                print(f"\n⚠️ 没有新增Zabbix配置项")
        
        elif test_source == 'prometheus':
            new_items = total_prometheus_after - total_prometheus_before
            new_with_external_id = prometheus_with_external_id_after - prometheus_with_external_id_before
            if new_items > 0:
                success_rate = (new_with_external_id / new_items) * 100
                print(f"\n✅ 新增 {new_items} 个Prometheus配置项，其中 {new_with_external_id} 个有external_id (成功率: {success_rate:.1f}%)")
            else:
                print(f"\n⚠️ 没有新增Prometheus配置项")
        
        # 验证修复效果
        if test_source == 'zabbix' and new_items > 0 and success_rate > 80:
            print("🎉 Zabbix数据映射修复成功！")
        elif test_source == 'prometheus' and new_items > 0 and success_rate > 80:
            print("🎉 Prometheus数据映射修复成功！")
        else:
            print("⚠️ 可能需要进一步检查数据映射逻辑")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        await Tortoise.close_connections()

if __name__ == "__main__":
    asyncio.run(test_discovery_with_fixes())
