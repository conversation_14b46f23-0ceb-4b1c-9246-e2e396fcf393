# Vue FastAPI Admin - 环境变量配置示例
# 复制此文件为 .env 并放在项目根目录，然后填入实际值

# =================
# 数据库配置
# =================
DB_HOST=************
DB_PORT=3306
DB_USER=backup_user
DB_PASSWORD=v+SCbs/6LsYNovFngEY=
DB_NAME=fastapi_user

# =================
# CMDB发现配置
# =================

# Prometheus配置
PROMETHEUS_URL=http://*************:31891
PROMETHEUS_TIMEOUT=30

# Zabbix配置
ZABBIX_URL=http://************/zabbix/api_jsonrpc.php
ZABBIX_USERNAME=niuyeji
ZABBIX_PASSWORD=Changjiu123!

# Redis配置 (用于Celery任务队列)
REDIS_URL=redis://:CjK1rry1as@************:6379/0
CELERY_BROKER_URL=${REDIS_URL}
CELERY_RESULT_BACKEND=${REDIS_URL}

# =================
# 发现引擎配置
# =================
DISCOVERY_ENABLED=true
DISCOVERY_MAX_CONCURRENT_JOBS=5
DISCOVERY_JOB_TIMEOUT=1800
DISCOVERY_RETRY_ATTEMPTS=3
DISCOVERY_RETRY_DELAY=60

# =================
# 日志配置
# =================
DISCOVERY_LOG_LEVEL=INFO
LOG_FILE_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# =================
# 监控和告警配置
# =================
ENABLE_HEALTH_CHECK=true
HEALTH_CHECK_INTERVAL=300
ALERT_ON_FAILURE=true
ALERT_FAILURE_THRESHOLD=3
ALERT_WEBHOOK_URL=

# =================
# 安全配置
# =================
SECRET_KEY=3488a63e1765035d386f05409663f55c83bfae3b3c61a932744b20ad14244dcf
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=10080

# =================
# 应用配置
# =================
APP_TITLE=自动化运维系统
PROJECT_NAME=自动化运维系统
DEBUG=true
CORS_ORIGINS=["*"] 