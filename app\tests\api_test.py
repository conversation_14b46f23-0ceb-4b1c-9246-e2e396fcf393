import requests
import json

BASE_URL = "http://localhost:9999"  # 调整为您的API服务器地址

# 首先登录获取token
def get_token():
    login_data = {
        "username": "admin",  # 使用默认的超级管理员账号
        "password": "123456"
    }
    try:
        response = requests.post(f"{BASE_URL}/api/v1/base/login", json=login_data)
        if response.status_code == 200:
            return response.json().get("data", {}).get("token")
        print(f"登录失败: {response.status_code}, {response.text}")
        return "dev"  # 使用开发模式token
    except Exception as e:
        print(f"登录异常: {str(e)}")
        return "dev"  # 使用开发模式token

def test_ticket_api():
    """测试工单API路由是否正确"""
    # 获取token
    token = get_token()
    headers = {"token": token}
    print(f"使用token: {token}")
    
    # 测试工单列表API
    response = requests.get(f"{BASE_URL}/api/v1/tickets", headers=headers)
    print(f"工单列表API状态码: {response.status_code}")
    if response.status_code == 200:
        print("工单API可访问")
        print(f"响应数据: {response.json()}")
    else:
        print("工单API无法访问, 详细信息:", response.text)
    
    # 测试工单基础数据API
    endpoints = [
        "/api/v1/tickets/ticket/categories",
        "/api/v1/tickets/ticket/priorities",
        "/api/v1/tickets/ticket/statuses",
        "/api/v1/tickets/ticket/service-catalogs"
    ]
    
    for endpoint in endpoints:
        response = requests.get(f"{BASE_URL}{endpoint}", headers=headers)
        print(f"{endpoint} 状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"数据: {response.json()}")

if __name__ == "__main__":
    test_ticket_api() 