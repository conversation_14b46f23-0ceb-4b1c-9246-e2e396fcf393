import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from tortoise import Tortoise, run_async
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MySQL 配置
TORTOISE_ORM = {
    "connections": {
        "default": {
            "engine": "tortoise.backends.mysql",
            "credentials": {
                "host": "************",
                "port": "3306",
                "user": "backup_user",
                "password": "v+SCbs/6LsYNovFngEY=",
                "database": "fastapi_user",
                "charset": "utf8mb4"
            }
        }
    },
    "apps": {
        "models": {
            "models": ["app.models.admin", "app.models.ticket", "aerich.models"],
            "default_connection": "default",
        }
    },
    "use_tz": False,
    "timezone": "Asia/Shanghai",
}

async def check_data():
    """检查工单相关数据"""
    print("正在连接数据库...")
    
    try:
        # 初始化Tortoise ORM
        await Tortoise.init(config=TORTOISE_ORM)
        print("数据库连接成功")
        
        # 导入模型
        from app.models.ticket import TicketCategory, TicketPriority, TicketStatus, ServiceCatalog
        
        # 检查工单状态
        print("\n=== 工单状态 ===")
        statuses = await TicketStatus.all()
        print(f"找到 {len(statuses)} 条工单状态记录")
        if statuses:
            for status in statuses:
                print(f"ID: {status.id}, 名称: {status.name}, 代码: {status.code}")
        else:
            print("没有工单状态数据")
        
        # 检查工单分类
        print("\n=== 工单分类 ===")
        categories = await TicketCategory.all()
        print(f"找到 {len(categories)} 条工单分类记录")
        if categories:
            for category in categories:
                print(f"ID: {category.id}, 名称: {category.name}")
        else:
            print("没有工单分类数据")
        
        # 检查工单优先级
        print("\n=== 工单优先级 ===")
        priorities = await TicketPriority.all()
        print(f"找到 {len(priorities)} 条工单优先级记录")
        if priorities:
            for priority in priorities:
                print(f"ID: {priority.id}, 名称: {priority.name}, 级别: {priority.level}")
        else:
            print("没有工单优先级数据")
        
        # 检查服务目录
        print("\n=== 服务目录 ===")
        services = await ServiceCatalog.all()
        print(f"找到 {len(services)} 条服务目录记录")
        if services:
            for service in services:
                print(f"ID: {service.id}, 名称: {service.name}")
        else:
            print("没有服务目录数据")
        
    except Exception as e:
        print(f"发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭连接
        await Tortoise.close_connections()
        print("数据库连接已关闭")

if __name__ == "__main__":
    print("开始检查工单数据...")
    run_async(check_data())
    print("检查完成") 