import asyncio
import os
import sys
import importlib
import inspect
from datetime import datetime
from pathlib import Path
from enum import Enum

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from tortoise import Tortoise, run_async
from tortoise.models import Model

# 定义迁移目录路径
MIGRATIONS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "migrations")

def discover_model_modules():
    """自动发现所有模型模块"""
    models_dir = Path(__file__).parent.parent / "models"
    model_modules = []
    
    print("🔍 正在发现模型模块...")
    
    for py_file in models_dir.glob("*.py"):
        if py_file.name.startswith("__"):
            continue
            
        module_name = f"app.models.{py_file.stem}"
        try:
            # 尝试导入模块
            module = importlib.import_module(module_name)
            
            # 检查是否包含Tortoise模型
            has_models = False
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and 
                    issubclass(obj, Model) and 
                    obj != Model and
                    hasattr(obj, '_meta')):
                    has_models = True
                    break
            
            if has_models:
                model_modules.append(module_name)
                print(f"  ✅ 发现模型模块: {module_name}")
            else:
                # 检查是否是枚举模块（包含枚举但不包含模型）
                has_enums = any(
                    inspect.isclass(obj) and 
                    issubclass(obj, Enum) and 
                    obj != Enum
                    for name, obj in inspect.getmembers(module)
                )
                if has_enums:
                    print(f"  📋 枚举模块: {module_name} (包含枚举定义)")
                else:
                    print(f"  ⚠️  模块 {module_name} 不包含有效模型")
                
        except ImportError as e:
            print(f"  ❌ 无法导入模块 {module_name}: {str(e)}")
        except Exception as e:
            print(f"  ⚠️  检查模块 {module_name} 时出错: {str(e)}")
    
    # 总是包含aerich.models
    model_modules.append("aerich.models")
    
    print(f"📋 共发现 {len(model_modules)-1} 个应用模型模块")
    return model_modules

def get_tortoise_config():
    """获取Tortoise ORM配置"""
    try:
        # 尝试从设置中获取配置
        from app.settings.config import settings
        if hasattr(settings, 'TORTOISE_ORM'):
            config = settings.TORTOISE_ORM.copy()
        else:
            # 使用默认配置
            config = {
                "connections": {
                    "default": {
                        "engine": "tortoise.backends.mysql",
                        "credentials": {
                            "host": "************",
                            "port": "3306",
                            "user": "backup_user",
                            "password": "v+SCbs/6LsYNovFngEY=",
                            "database": "fastapi_user",
                            "charset": "utf8mb4"
                        }
                    }
                },
                "apps": {
                    "models": {
                        "models": [],
                        "default_connection": "default",
                    }
                },
                "use_tz": False,
                "timezone": "Asia/Shanghai",
            }
    except ImportError:
        print("⚠️  无法导入设置，使用默认数据库配置")
        config = {
            "connections": {
                "default": {
                    "engine": "tortoise.backends.mysql",
                    "credentials": {
                        "host": "************",
                        "port": "3306",
                        "user": "backup_user",
                        "password": "v+SCbs/6LsYNovFngEY=",
                        "database": "fastapi_user",
                        "charset": "utf8mb4"
                    }
                }
            },
            "apps": {
                "models": {
                    "models": [],
                    "default_connection": "default",
                }
            },
            "use_tz": False,
            "timezone": "Asia/Shanghai",
        }
    
    # 自动发现并设置模型模块
    model_modules = discover_model_modules()
    config["apps"]["models"]["models"] = model_modules
    
    return config

async def init_tortoise():
    """初始化Tortoise ORM"""
    config = get_tortoise_config()
    await Tortoise.init(config=config)
    return config

async def check_database_status():
    """检查数据库连接和表状态"""
    print("\n🔍 检查数据库状态...")
    
    try:
        config = await init_tortoise()
        conn = Tortoise.get_connection("default")
        
        # 检查数据库连接
        await conn.execute_query("SELECT 1")
        print("  ✅ 数据库连接正常")
        
        # 获取所有表
        try:
            tables_result = await conn.execute_query("SHOW TABLES")
            existing_tables = []
            
            # 处理不同格式的查询结果
            if isinstance(tables_result, (list, tuple)) and len(tables_result) > 0:
                # 如果是嵌套结构，取第二个元素
                if isinstance(tables_result, tuple) and len(tables_result) > 1:
                    data = tables_result[1] if tables_result[1] else []
                else:
                    data = tables_result
                
                # 提取表名
                for row in data:
                    if isinstance(row, dict):
                        # MySQL SHOW TABLES 返回的是字典格式
                        for key, value in row.items():
                            if key.startswith('Tables_in_'):
                                existing_tables.append(str(value))
                                break
                    elif isinstance(row, (list, tuple)) and len(row) > 0:
                        existing_tables.append(str(row[0]))
                    elif isinstance(row, str):
                        existing_tables.append(row)
                
                print(f"  📊 数据库中共有 {len(existing_tables)} 个表")
            else:
                print(f"  ⚠️  查询表结果格式异常: {type(tables_result)}")
        except Exception as e:
            print(f"  ❌ 获取表列表失败: {str(e)}")
            existing_tables = []
        
        if existing_tables:
            # 检查aerich迁移表
            if "aerich" in existing_tables:
                print("  ✅ Aerich迁移表存在")
                
                # 获取迁移历史
                try:
                    migration_result = await conn.execute_query("SELECT version, app FROM aerich ORDER BY id DESC LIMIT 5")
                    if isinstance(migration_result, tuple) and len(migration_result) > 1 and migration_result[1]:
                        print(f"  📋 最近的 {len(migration_result[1])} 次迁移:")
                        for row in migration_result[1]:
                            print(f"    - {row[0]} (app: {row[1]})")
                except Exception as e:
                    print(f"  ⚠️  获取迁移历史失败: {str(e)}")
            else:
                print("  ❌ Aerich迁移表不存在，需要初始化")
        else:
            print("  ⚠️  数据库为空")
        
        await Tortoise.close_connections()
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库检查失败: {str(e)}")
        return False

async def init_aerich():
    """初始化Aerich"""
    print("\n🚀 初始化Aerich迁移系统...")
    
    from aerich import Command
    
    # 确保迁移目录存在
    os.makedirs(MIGRATIONS_DIR, exist_ok=True)
    print(f"📁 迁移目录: {MIGRATIONS_DIR}")
    
    # 初始化Tortoise ORM
    config = await init_tortoise()
    
    try:
        command = Command(tortoise_config=config, app="models", location=MIGRATIONS_DIR)
        await command.init()
        await command.init_db(safe=True)
        print("✅ Aerich 初始化完成！")
        
        # 显示配置信息
        model_count = len(config["apps"]["models"]["models"]) - 1  # 减去aerich.models
        print(f"📋 已配置 {model_count} 个模型模块")
        
    except Exception as e:
        print(f"❌ Aerich 初始化失败: {str(e)}")
        raise
    finally:
        await Tortoise.close_connections()

async def create_migration(name=None):
    """创建迁移文件"""
    if not name:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        name = f"auto_migration_{timestamp}"
    
    print(f"\n📝 创建迁移: {name}")
    
    from aerich import Command
    
    # 初始化Tortoise ORM
    config = await init_tortoise()
    
    try:
        command = Command(tortoise_config=config, app="models", location=MIGRATIONS_DIR)
        result = await command.migrate(name)
        
        if result:
            print(f"✅ 迁移文件创建完成: {result}")
        else:
            print("ℹ️  没有检测到模型变更，无需创建迁移")
            
    except Exception as e:
        print(f"❌ 创建迁移失败: {str(e)}")
        raise
    finally:
        await Tortoise.close_connections()

async def upgrade():
    """应用迁移"""
    print("\n⬆️  应用数据库迁移...")
    
    from aerich import Command
    
    # 初始化Tortoise ORM
    config = await init_tortoise()
    
    try:
        command = Command(tortoise_config=config, app="models", location=MIGRATIONS_DIR)
        result = await command.upgrade()
        
        if result:
            print("✅ 迁移应用完成！")
        else:
            print("ℹ️  数据库已是最新状态，无需迁移")
            
    except Exception as e:
        print(f"❌ 应用迁移失败: {str(e)}")
        raise
    finally:
        await Tortoise.close_connections()

async def downgrade(version=None):
    """回滚迁移"""
    version_info = f" 到版本 {version}" if version else ""
    print(f"\n⬇️  回滚数据库迁移{version_info}...")
    
    from aerich import Command
    
    # 初始化Tortoise ORM
    config = await init_tortoise()
    
    try:
        command = Command(tortoise_config=config, app="models", location=MIGRATIONS_DIR)
        if version:
            await command.downgrade(version)
        else:
            await command.downgrade()
        print("✅ 迁移回滚完成！")
        
    except Exception as e:
        print(f"❌ 回滚迁移失败: {str(e)}")
        raise
    finally:
        await Tortoise.close_connections()

async def history():
    """查看迁移历史"""
    print("\n📜 迁移历史记录:")
    
    from aerich import Command
    
    # 初始化Tortoise ORM
    config = await init_tortoise()
    
    try:
        command = Command(tortoise_config=config, app="models", location=MIGRATIONS_DIR)
        history_list = await command.history()
        
        if history_list:
            for i, h in enumerate(history_list, 1):
                status = "✅ 已应用" if h.get('applied', False) else "⏳ 待应用"
                print(f"  {i}. 版本: {h['version']} | 名称: {h['name']} | 状态: {status}")
        else:
            print("  ℹ️  暂无迁移历史")
            
    except Exception as e:
        print(f"❌ 获取迁移历史失败: {str(e)}")
    finally:
        await Tortoise.close_connections()

async def generate_tables_directly():
    """直接使用Tortoise生成表结构（当aerich失败时的备用方法）"""
    print("\n🔧 使用直接方式生成表结构...")
    
    try:
        config = await init_tortoise()
        conn = Tortoise.get_connection("default")
        
        # 检查现有表
        print("📋 检查现有表...")
        tables_result = await conn.execute_query("SHOW TABLES")
        existing_tables = []
        
        if isinstance(tables_result, tuple) and len(tables_result) > 1 and tables_result[1]:
            data = tables_result[1]
            for row in data:
                if isinstance(row, dict):
                    for key, value in row.items():
                        if key.startswith('Tables_in_'):
                            existing_tables.append(str(value))
                            break
        
        print(f"📊 当前数据库有 {len(existing_tables)} 个表")
        
        # 使用 generate_schemas 创建表
        print("⚙️  正在生成数据库表结构...")
        await Tortoise.generate_schemas(safe=True)
        
        print("✅ 表结构生成完成")
        
        # 再次检查表是否创建成功
        print("\n🔍 验证表创建结果...")
        tables_result = await conn.execute_query("SHOW TABLES")
        new_existing_tables = []
        
        if isinstance(tables_result, tuple) and len(tables_result) > 1 and tables_result[1]:
            data = tables_result[1]
            for row in data:
                if isinstance(row, dict):
                    for key, value in row.items():
                        if key.startswith('Tables_in_'):
                            new_existing_tables.append(str(value))
                            break
        
        new_tables_count = len(new_existing_tables) - len(existing_tables)
        
        print(f"\n📊 表创建总结:")
        print(f"  - 总表数: {len(new_existing_tables)} (+{new_tables_count})")
        
        if new_tables_count > 0:
            print(f"✅ 成功创建 {new_tables_count} 个新表")
            return True
        else:
            print("ℹ️  没有新表需要创建")
            return True
        
    except Exception as e:
        print(f"❌ 直接生成表失败: {str(e)}")
        return False
    finally:
        await Tortoise.close_connections()

async def test_models():
    """测试所有模型的基本操作"""
    print("\n🧪 测试模型操作...")
    
    try:
        config = await init_tortoise()
        
        # 动态导入所有发现的模型模块
        model_modules = discover_model_modules()
        tested_models = []
        
        for module_name in model_modules:
            if module_name == "aerich.models":
                continue
                
            try:
                module = importlib.import_module(module_name)
                
                # 获取模块中的所有Tortoise模型
                for name, obj in inspect.getmembers(module):
                    if (inspect.isclass(obj) and 
                        issubclass(obj, Model) and 
                        obj != Model and
                        hasattr(obj, '_meta')):
                        
                        # 测试基本查询
                        count = await obj.all().count()
                        tested_models.append((obj.__name__, count))
                        
            except Exception as e:
                print(f"  ⚠️  测试模块 {module_name} 失败: {str(e)}")
        
        if tested_models:
            print(f"📊 模型测试结果:")
            for model_name, count in tested_models:
                print(f"  - {model_name}: {count} 条记录")
            print("✅ 所有模型测试通过")
        else:
            print("⚠️  没有找到可测试的模型")
            
        return True
        
    except Exception as e:
        print(f"❌ 模型测试失败: {str(e)}")
        return False
    finally:
        await Tortoise.close_connections()

async def auto_migrate():
    """自动迁移：检查状态并执行必要的迁移"""
    print("\n🤖 自动迁移模式")
    
    # 1. 检查数据库状态
    db_ok = await check_database_status()
    
    if not db_ok:
        print("❌ 数据库连接失败，无法进行迁移")
        return
    
    try:
        # 2. 检查是否需要初始化Aerich
        config = await init_tortoise()
        conn = Tortoise.get_connection("default")
        
        tables_result = await conn.execute_query("SHOW TABLES")
        existing_tables = []
        if isinstance(tables_result, tuple) and len(tables_result) > 1 and tables_result[1]:
            data = tables_result[1]
            for row in data:
                if isinstance(row, dict):
                    # MySQL SHOW TABLES 返回的是字典格式
                    for key, value in row.items():
                        if key.startswith('Tables_in_'):
                            existing_tables.append(str(value))
                            break
                elif isinstance(row, (list, tuple)) and len(row) > 0:
                    existing_tables.append(str(row[0]))
        
        await Tortoise.close_connections()
        
        # 3. 尝试使用Aerich进行迁移
        aerich_success = False
        
        if "aerich" not in existing_tables:
            print("🔧 检测到未初始化的数据库，正在初始化Aerich...")
            try:
                await init_aerich()
                aerich_success = True
            except Exception as e:
                print(f"⚠️  Aerich初始化失败: {str(e)}")
        else:
            aerich_success = True
        
        if aerich_success:
            try:
                # 尝试创建迁移
                print("🔍 检查模型变更...")
                await create_migration()
                
                # 应用迁移
                await upgrade()
                
                print("✅ Aerich迁移完成")
                
            except Exception as e:
                print(f"⚠️  Aerich迁移失败: {str(e)}")
                print("🔄 切换到直接表生成模式...")
                aerich_success = False
        
        # 4. 如果Aerich失败，使用直接方式
        if not aerich_success:
            print("📋 使用备用方式生成表结构...")
            await generate_tables_directly()
        
        # 5. 测试模型
        await test_models()
        
        # 6. 显示最终状态
        await check_database_status()
        
        print("\n🎉 自动迁移完成！")
        
    except Exception as e:
        print(f"❌ 自动迁移失败: {str(e)}")
        print("🔄 尝试使用备用方式...")
        try:
            await generate_tables_directly()
            print("✅ 备用方式创建表成功")
        except Exception as backup_e:
            print(f"❌ 备用方式也失败: {str(backup_e)}")

def print_usage():
    """打印使用说明"""
    print("""
🔧 数据库迁移工具使用说明

基本命令:
  python aerich_migrate.py <命令> [参数]

可用命令:
  status      - 检查数据库和迁移状态
  init        - 初始化Aerich迁移系统
  migrate     - 创建新的迁移文件 [可选: 迁移名称]
  upgrade     - 应用所有待执行的迁移
  downgrade   - 回滚迁移 [可选: 目标版本]
  history     - 查看迁移历史记录
  auto        - 自动模式：检查并执行必要的迁移
  generate    - 直接生成表结构（绕过Aerich）
  test        - 测试所有模型的基本操作

示例:
  python aerich_migrate.py status
  python aerich_migrate.py auto
  python aerich_migrate.py migrate "添加新的CMDB模型"
  python aerich_migrate.py upgrade
  python aerich_migrate.py downgrade 20231201_120000_initial

特性:
  ✨ 自动发现所有模型模块
  🔍 智能检查数据库状态
  🤖 自动迁移模式
  📋 详细的操作日志
  🔧 错误处理和恢复建议
""")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print_usage()
        sys.exit(1)
    
    action = sys.argv[1].lower()
    
    try:
        if action == "status":
            run_async(check_database_status())
        elif action == "init":
            run_async(init_aerich())
        elif action == "migrate":
            name = sys.argv[2] if len(sys.argv) > 2 else None
            run_async(create_migration(name))
        elif action == "upgrade":
            run_async(upgrade())
        elif action == "downgrade":
            version = sys.argv[2] if len(sys.argv) > 2 else None
            run_async(downgrade(version))
        elif action == "history":
            run_async(history())
        elif action == "auto":
            run_async(auto_migrate())
        elif action == "generate":
            run_async(generate_tables_directly())
        elif action == "test":
            run_async(test_models())
        elif action in ["help", "--help", "-h"]:
            print_usage()
        else:
            print(f"❌ 未知命令: {action}")
            print_usage()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {str(e)}")
        sys.exit(1) 